<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0"><dict><key>0</key><dict><key>NSCameraUsageDescription</key><string>Application requests access to the device&#39;s camera.</string></dict><key>1</key><dict><key>NSMicrophoneUsageDescription</key><string>Application requests access to the device&#39;s microphone.</string></dict><key>2</key><dict><key>NSDocumentsFolderUsageDescription</key><string>Application requests access to the user&#39;s Documents folder to save converted images.</string></dict><key>3</key><dict><key>NSDownloadsFolderUsageDescription</key><string>Application requests access to the user&#39;s Downloads folder to save converted images.</string></dict><key>4</key><dict><key>NSNetworkVolumesUsageDescription</key><string>Application requests access to network volumes for file operations.</string></dict><key>5</key><dict><key>NSRemovableVolumesUsageDescription</key><string>Application requests access to removable volumes for file operations.</string></dict><key>6</key><dict><key>LSApplicationCategoryType</key><string>public.app-category.graphics-design</string></dict><key>CFBundleDisplayName</key><string>Observer-137</string><key>CFBundleExecutable</key><string>Observer-137</string><key>CFBundleIconFile</key><string>icon.icns</string><key>CFBundleIdentifier</key><string>com.electron.app</string><key>CFBundleInfoDictionaryVersion</key><string>6.0</string><key>CFBundleName</key><string>Observer-137</string><key>CFBundlePackageType</key><string>APPL</string><key>CFBundleShortVersionString</key><string>1.0.0</string><key>CFBundleVersion</key><string>1.0.0</string><key>DTCompiler</key><string>com.apple.compilers.llvm.clang.1_0</string><key>DTSDKBuild</key><string>23F73</string><key>DTSDKName</key><string>macosx14.5</string><key>DTXcode</key><string>1540</string><key>DTXcodeBuild</key><string>15F31d</string><key>ElectronAsarIntegrity</key><dict><key>Resources/app.asar</key><dict><key>algorithm</key><string>SHA256</string><key>hash</key><string>0215bb6a196fdcbcb960b559b862280652a7fb860eb0c4e068f5ba760e8c7c2f</string></dict></dict><key>LSApplicationCategoryType</key><string>public.app-category.developer-tools</string><key>LSEnvironment</key><dict><key>MallocNanoZone</key><string>0</string></dict><key>LSMinimumSystemVersion</key><string>11.0</string><key>NSAppTransportSecurity</key><dict><key>NSAllowsArbitraryLoads</key><true/><key>NSAllowsLocalNetworking</key><true/><key>NSExceptionDomains</key><dict><key>127.0.0.1</key><dict><key>NSIncludesSubdomains</key><false/><key>NSTemporaryExceptionAllowsInsecureHTTPLoads</key><true/><key>NSTemporaryExceptionAllowsInsecureHTTPSLoads</key><false/><key>NSTemporaryExceptionMinimumTLSVersion</key><string>1.0</string><key>NSTemporaryExceptionRequiresForwardSecrecy</key><false/></dict><key>localhost</key><dict><key>NSIncludesSubdomains</key><false/><key>NSTemporaryExceptionAllowsInsecureHTTPLoads</key><true/><key>NSTemporaryExceptionAllowsInsecureHTTPSLoads</key><false/><key>NSTemporaryExceptionMinimumTLSVersion</key><string>1.0</string><key>NSTemporaryExceptionRequiresForwardSecrecy</key><false/></dict></dict></dict><key>NSBluetoothAlwaysUsageDescription</key><string>This app needs access to Bluetooth</string><key>NSBluetoothPeripheralUsageDescription</key><string>This app needs access to Bluetooth</string><key>NSCameraUsageDescription</key><string>This app needs access to the camera</string><key>NSHighResolutionCapable</key><true/><key>NSHumanReadableCopyright</key><string>Copyright © 2025 example.com</string><key>NSMainNibFile</key><string>MainMenu</string><key>NSMicrophoneUsageDescription</key><string>This app needs access to the microphone</string><key>NSPrefersDisplaySafeAreaCompatibilityMode</key><false/><key>NSPrincipalClass</key><string>AtomApplication</string><key>NSQuitAlwaysKeepsWindows</key><false/><key>NSRequiresAquaSystemAppearance</key><false/><key>NSSupportsAutomaticGraphicsSwitching</key><true/></dict></plist>