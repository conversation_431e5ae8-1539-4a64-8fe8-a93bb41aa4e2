appId: com.electron.app
productName: Observer-137
directories:
  buildResources: build
files:
  - '!**/.vscode/*'
  - '!src/*'
  - '!electron.vite.config.{js,ts,mjs,cjs}'
  - '!{.eslintignore,.eslintrc.cjs,.prettierignore,.prettierrc.yaml,dev-app-update.yml,CHANGELOG.md,README.md}'
  - '!{.env.local,.env.*.local,.npmrc,pnpm-lock.yaml}'
  - '.env'
asarUnpack:
  - resources/**
win:
  executableName: electron-app
nsis:
  artifactName: ${name}-${version}-setup.${ext}
  shortcutName: ${productName}
  uninstallDisplayName: ${productName}
  createDesktopShortcut: always
mac:
  entitlementsInherit: build/entitlements.mac.plist
  hardenedRuntime: true
  gatekeeperAssess: false
  extendInfo:
    - NSCameraUsageDescription: Application requests access to the device's camera.
    - NSMicrophoneUsageDescription: Application requests access to the device's microphone.
    - NSDocumentsFolderUsageDescription: Application requests access to the user's Documents folder to save converted images.
    - NSDownloadsFolderUsageDescription: Application requests access to the user's Downloads folder to save converted images.
    - NSNetworkVolumesUsageDescription: Application requests access to network volumes for file operations.
    - NSRemovableVolumesUsageDescription: Application requests access to removable volumes for file operations.
    - LSApplicationCategoryType: public.app-category.graphics-design
  notarize: false
dmg:
  artifactName: ${name}-${version}.${ext}
linux:
  target:
    - AppImage
    - snap
    - deb
  maintainer: electronjs.org
  category: Utility
appImage:
  artifactName: ${name}-${version}.${ext}
npmRebuild: false
publish:
  provider: generic
  url: https://example.com/auto-updates
electronDownload:
  mirror: https://npmmirror.com/mirrors/electron/
