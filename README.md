# electron-app

An Electron application with React

## Recommended IDE Setup

- [VSCode](https://code.visualstudio.com/) + [ESLint](https://marketplace.visualstudio.com/items?itemName=dbaeumer.vscode-eslint) + [Prettier](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode)

## Project Setup

### Install

```bash
$ pnpm install
```

### Development

```bash
$ pnpm dev
```

### Build

```bash
# For windows
$ pnpm build:win

# For macOS
$ pnpm build:mac

# For Linux
$ pnpm build:linux
```

# FLUX 图像转换工具

基于 Replicate FLUX-Kontext-Pro 模型的图像转换工具，支持单张和批量图片转换。

## 功能特性

- 🎨 **专业风格预设** - 5种精心调教的艺术风格 (吉卜力/新海诚/动漫/写实/油画)
- 🔧 **脚本级优化** - 完全匹配 convert_video.py 的专业参数配置
- 🎬 **视频帧连续性** - 智能种子策略确保帧间一致性，专为视频处理设计
- 📸 **单张/批量转换** - 支持单张图片或批量处理
- ⚙️ **高级参数控制** - Guidance Scale、推理步数等专业参数可调
- 🧠 **智能提示词** - 长篇专业提示词模板 + 可选 DeepSeek 优化
- 💾 **本地文件管理** - 自动保存到本地，按日期组织
- 📁 **快速访问** - 一键打开文件夹，复制路径
- 🗑️ **自动清理** - 支持清理旧文件，节省存储空间
- 📱 **响应式界面** - 适配桌面和移动设备

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 配置环境变量

在项目根目录创建 `.env` 文件：

```env
# Replicate API Token (必需)
# 从 https://replicate.com/account/api-tokens 获取
VITE_REPLICATE_API_TOKEN=your_replicate_token_here

# DeepSeek API Key (可选，用于提示词优化)
# 从 https://platform.deepseek.com/api_keys 获取
VITE_DEEPSEEK_API_KEY=your_deepseek_api_key_here
```

**重要说明**：
- ✅ **已解决CORS问题**：API调用现在通过Electron主进程进行，完全避免了跨域限制
- 🔒 **安全性**：API密钥在主进程中处理，不会暴露到前端
- ⚡ **性能**：直接调用，无需代理服务器

### 3. 启动应用

开发模式：
```bash
npm run dev
```

构建应用：
```bash
npm run build
```

## 使用说明

1. **选择图片** - 点击"选择图片"按钮，支持多选
2. **输入提示词** - 描述想要的转换效果，如："Make this a 90s cartoon"
3. **调整参数** - 根据需要调整宽高比、输出格式等参数
4. **开始转换** - 点击转换按钮开始处理
5. **查看结果** - 转换完成后自动保存到本地，可在界面查看
6. **文件管理** - 使用文件管理功能快速访问、下载或清理文件

### 文件管理功能

- **自动保存** - 转换的图片自动保存到应用数据目录
- **按日期组织** - 文件按日期分类存储，便于管理
- **快速访问** - 一键打开文件夹，直接查看所有转换结果
- **路径复制** - 复制文件路径，方便在其他应用中使用
- **清理工具** - 定期清理7天前的旧文件，节省存储空间

## 转换参数

- **提示词优化** - 使用 DeepSeek 优化中文提示词为英文
- **宽高比** - 支持多种标准比例或匹配输入图像
- **输出格式** - JPG、PNG、WebP 格式选择
- **安全容忍度** - 控制内容安全级别 (0-6)
- **随机种子** - 可选的固定种子以获得可重现结果

## 技术栈

- **Frontend**: React 18 + Vite
- **Desktop**: Electron
- **AI Model**: Replicate FLUX-Kontext-Pro
- **Optimization**: DeepSeek API
- **Styling**: CSS3 with modern features

## API 服务

### Replicate FLUX-Kontext-Pro
- 先进的图像转换模型
- 支持多种艺术风格转换
- 高质量输出结果

### DeepSeek (可选)
- 智能提示词优化
- 中文到英文转换
- 提升转换效果

## 开发

```bash
# 开发模式
npm run dev

# 代码检查
npm run lint

# 格式化代码
npm run format

# 构建不同平台
npm run build:win    # Windows
npm run build:mac    # macOS
npm run build:linux  # Linux
```

## 注意事项

1. **API 费用** - Replicate API 按使用量计费，请注意使用成本
2. **网络要求** - 需要稳定的网络连接用于 API 调用
3. **图片格式** - 支持常见的图片格式 (JPG, PNG, WebP, GIF)
4. **处理时间** - 转换时间取决于图片大小和网络状况

## 常见问题

**Q: 转换失败怎么办？**
A: 检查网络连接和 API Token 是否正确配置

**Q: 支持哪些图片格式？**
A: 支持 JPG、PNG、WebP、GIF 等常见格式

**Q: 可以转换多大的图片？**
A: 建议图片大小不超过 10MB，过大的图片可能影响处理速度

**Q: DeepSeek 优化是必需的吗？**
A: 不是必需的，但可以提升中文提示词的转换效果

## 许可证

MIT License
