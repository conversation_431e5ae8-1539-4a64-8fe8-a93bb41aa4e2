{"version": 3, "file": "CancellationToken.js", "sourceRoot": "", "sources": ["../src/CancellationToken.ts"], "names": [], "mappings": ";;;AAAA,mCAAqC;AAErC,MAAa,iBAAkB,SAAQ,qBAAY;IAIjD,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;IAC5E,CAAC;IAGD,IAAI,MAAM,CAAC,KAAwB;QACjC,IAAI,CAAC,yBAAyB,EAAE,CAAA;QAEhC,IAAI,CAAC,OAAO,GAAG,KAAK,CAAA;QACpB,IAAI,CAAC,mBAAmB,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,CAAA;QAC9C,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAA;IACjD,CAAC;IAED,qDAAqD;IACrD,YAAY,MAA0B;QACpC,KAAK,EAAE,CAAA;QAlBD,wBAAmB,GAAuB,IAAI,CAAA;QAO9C,YAAO,GAA6B,IAAI,CAAA;QAa9C,IAAI,CAAC,UAAU,GAAG,KAAK,CAAA;QACvB,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;YACnB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACtB,CAAC;IACH,CAAC;IAED,MAAM;QACJ,IAAI,CAAC,UAAU,GAAG,IAAI,CAAA;QACtB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;IACrB,CAAC;IAEO,QAAQ,CAAC,OAAkB;QACjC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,OAAO,EAAE,CAAA;QACX,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;QAC9B,CAAC;IACH,CAAC;IAED,aAAa,CACX,QAAqJ;QAErJ,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,OAAO,OAAO,CAAC,MAAM,CAAI,IAAI,iBAAiB,EAAE,CAAC,CAAA;QACnD,CAAC;QAED,MAAM,cAAc,GAAG,GAAG,EAAE;YAC1B,IAAI,aAAa,IAAI,IAAI,EAAE,CAAC;gBAC1B,IAAI,CAAC;oBACH,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAA;oBAC5C,aAAa,GAAG,IAAI,CAAA;gBACtB,CAAC;gBAAC,OAAO,OAAO,EAAE,CAAC;oBACjB,SAAS;gBACX,CAAC;YACH,CAAC;QACH,CAAC,CAAA;QAED,IAAI,aAAa,GAAwB,IAAI,CAAA;QAC7C,OAAO,IAAI,OAAO,CAAI,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACxC,IAAI,kBAAkB,GAAwB,IAAI,CAAA;YAElD,aAAa,GAAG,GAAG,EAAE;gBACnB,IAAI,CAAC;oBACH,IAAI,kBAAkB,IAAI,IAAI,EAAE,CAAC;wBAC/B,kBAAkB,EAAE,CAAA;wBACpB,kBAAkB,GAAG,IAAI,CAAA;oBAC3B,CAAC;gBACH,CAAC;wBAAS,CAAC;oBACT,MAAM,CAAC,IAAI,iBAAiB,EAAE,CAAC,CAAA;gBACjC,CAAC;YACH,CAAC,CAAA;YAED,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACnB,aAAa,EAAE,CAAA;gBACf,OAAM;YACR,CAAC;YAED,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAA;YAE5B,QAAQ,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC,QAAoB,EAAE,EAAE;gBACjD,kBAAkB,GAAG,QAAQ,CAAA;YAC/B,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC;aACC,IAAI,CAAC,EAAE,CAAC,EAAE;YACT,cAAc,EAAE,CAAA;YAChB,OAAO,EAAE,CAAA;QACX,CAAC,CAAC;aACD,KAAK,CAAC,CAAC,CAAM,EAAE,EAAE;YAChB,cAAc,EAAE,CAAA;YAChB,MAAM,CAAC,CAAA;QACT,CAAC,CAAC,CAAA;IACN,CAAC;IAEO,yBAAyB;QAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAA;QAC3B,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI,CAAC,mBAAmB,IAAI,IAAI,EAAE,CAAC;YACvD,MAAM,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAA;YACzD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAA;QACjC,CAAC;IACH,CAAC;IAED,OAAO;QACL,IAAI,CAAC;YACH,IAAI,CAAC,yBAAyB,EAAE,CAAA;QAClC,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,kBAAkB,EAAE,CAAA;YACzB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAA;QACrB,CAAC;IACH,CAAC;CACF;AA9GD,8CA8GC;AAED,MAAa,iBAAkB,SAAQ,KAAK;IAC1C;QACE,KAAK,CAAC,WAAW,CAAC,CAAA;IACpB,CAAC;CACF;AAJD,8CAIC", "sourcesContent": ["import { EventEmitter } from \"events\"\n\nexport class CancellationToken extends EventEmitter {\n  private parentCancelHandler: (() => any) | null = null\n\n  private _cancelled: boolean\n  get cancelled(): boolean {\n    return this._cancelled || (this._parent != null && this._parent.cancelled)\n  }\n\n  private _parent: CancellationToken | null = null\n  set parent(value: CancellationToken) {\n    this.removeParentCancelHandler()\n\n    this._parent = value\n    this.parentCancelHandler = () => this.cancel()\n    this._parent.onCancel(this.parentCancelHandler)\n  }\n\n  // babel cannot compile ... correctly for super calls\n  constructor(parent?: CancellationToken) {\n    super()\n\n    this._cancelled = false\n    if (parent != null) {\n      this.parent = parent\n    }\n  }\n\n  cancel() {\n    this._cancelled = true\n    this.emit(\"cancel\")\n  }\n\n  private onCancel(handler: () => any) {\n    if (this.cancelled) {\n      handler()\n    } else {\n      this.once(\"cancel\", handler)\n    }\n  }\n\n  createPromise<R>(\n    callback: (resolve: (thenableOrResult: R | PromiseLike<R>) => void, reject: (error: Error) => void, onCancel: (callback: () => void) => void) => void\n  ): Promise<R> {\n    if (this.cancelled) {\n      return Promise.reject<R>(new CancellationError())\n    }\n\n    const finallyHandler = () => {\n      if (cancelHandler != null) {\n        try {\n          this.removeListener(\"cancel\", cancelHandler)\n          cancelHandler = null\n        } catch (_ignore) {\n          // ignore\n        }\n      }\n    }\n\n    let cancelHandler: (() => void) | null = null\n    return new Promise<R>((resolve, reject) => {\n      let addedCancelHandler: (() => void) | null = null\n\n      cancelHandler = () => {\n        try {\n          if (addedCancelHandler != null) {\n            addedCancelHandler()\n            addedCancelHandler = null\n          }\n        } finally {\n          reject(new CancellationError())\n        }\n      }\n\n      if (this.cancelled) {\n        cancelHandler()\n        return\n      }\n\n      this.onCancel(cancelHandler)\n\n      callback(resolve, reject, (callback: () => void) => {\n        addedCancelHandler = callback\n      })\n    })\n      .then(it => {\n        finallyHandler()\n        return it\n      })\n      .catch((e: any) => {\n        finallyHandler()\n        throw e\n      })\n  }\n\n  private removeParentCancelHandler() {\n    const parent = this._parent\n    if (parent != null && this.parentCancelHandler != null) {\n      parent.removeListener(\"cancel\", this.parentCancelHandler)\n      this.parentCancelHandler = null\n    }\n  }\n\n  dispose() {\n    try {\n      this.removeParentCancelHandler()\n    } finally {\n      this.removeAllListeners()\n      this._parent = null\n    }\n  }\n}\n\nexport class CancellationError extends Error {\n  constructor() {\n    super(\"cancelled\")\n  }\n}\n"]}