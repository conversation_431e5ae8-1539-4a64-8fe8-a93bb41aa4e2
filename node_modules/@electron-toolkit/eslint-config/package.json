{"name": "@electron-toolkit/eslint-config", "version": "2.0.0", "description": "Basic ESLint config for Electron projects.", "main": "index.js", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts", "default": "./index.js"}}, "files": ["index.js", "index.d.ts"], "author": "<PERSON><https://github.com/alex8088>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/alex8088/electron-toolkit.git", "directory": "packages/eslint-config"}, "bugs": {"url": "https://github.com/alex8088/electron-toolkit/issues"}, "homepage": "https://github.com/alex8088/electron-toolkit/tree/master/packages/eslint-config#readme", "keywords": ["electron", "eslint"], "devDependencies": {"eslint": "^9.18.0"}, "peerDependencies": {"eslint": ">=9.0.0"}, "dependencies": {"@eslint/js": "^9.18.0", "globals": "^15.14.0"}}