{"amd": {"define": false, "require": false}, "applescript": {"$": false, "Application": false, "Automation": false, "console": false, "delay": false, "Library": false, "ObjC": false, "ObjectSpecifier": false, "Path": false, "Progress": false, "Ref": false}, "atomtest": {"advanceClock": false, "atom": false, "fakeClearInterval": false, "fakeClearTimeout": false, "fakeSetInterval": false, "fakeSetTimeout": false, "resetTimeouts": false, "waitsForPromise": false}, "browser": {"AbortController": false, "AbortSignal": false, "AbsoluteOrientationSensor": false, "AbstractRange": false, "Accelerometer": false, "addEventListener": false, "ai": false, "AI": false, "AITextSession": false, "alert": false, "AnalyserNode": false, "Animation": false, "AnimationEffect": false, "AnimationEvent": false, "AnimationPlaybackEvent": false, "AnimationTimeline": false, "atob": false, "Attr": false, "Audio": false, "AudioBuffer": false, "AudioBufferSourceNode": false, "AudioContext": false, "AudioData": false, "AudioDecoder": false, "AudioDestinationNode": false, "AudioEncoder": false, "AudioListener": false, "AudioNode": false, "AudioParam": false, "AudioParamMap": false, "AudioProcessingEvent": false, "AudioScheduledSourceNode": false, "AudioSinkInfo": false, "AudioWorklet": false, "AudioWorkletGlobalScope": false, "AudioWorkletNode": false, "AudioWorkletProcessor": false, "AuthenticatorAssertionResponse": false, "AuthenticatorAttestationResponse": false, "AuthenticatorResponse": false, "BackgroundFetchManager": false, "BackgroundFetchRecord": false, "BackgroundFetchRegistration": false, "BarcodeDetector": false, "BarProp": false, "BaseAudioContext": false, "BatteryManager": false, "BeforeUnloadEvent": false, "BiquadFilterNode": false, "Blob": false, "BlobEvent": false, "Bluetooth": false, "BluetoothCharacteristicProperties": false, "BluetoothDevice": false, "BluetoothRemoteGATTCharacteristic": false, "BluetoothRemoteGATTDescriptor": false, "BluetoothRemoteGATTServer": false, "BluetoothRemoteGATTService": false, "BluetoothUUID": false, "blur": false, "BroadcastChannel": false, "BrowserCaptureMediaStreamTrack": false, "btoa": false, "ByteLengthQueuingStrategy": false, "Cache": false, "caches": false, "CacheStorage": false, "cancelAnimationFrame": false, "cancelIdleCallback": false, "CanvasCaptureMediaStream": false, "CanvasCaptureMediaStreamTrack": false, "CanvasGradient": false, "CanvasPattern": false, "CanvasRenderingContext2D": false, "CaptureController": false, "CaretPosition": false, "CDATASection": false, "ChannelMergerNode": false, "ChannelSplitterNode": false, "ChapterInformation": false, "CharacterBoundsUpdateEvent": false, "CharacterData": false, "clearInterval": false, "clearTimeout": false, "clientInformation": false, "Clipboard": false, "ClipboardEvent": false, "ClipboardItem": false, "close": false, "closed": false, "CloseEvent": false, "CloseWatcher": false, "Comment": false, "CompositionEvent": false, "CompressionStream": false, "confirm": false, "console": false, "ConstantSourceNode": false, "ContentVisibilityAutoStateChangeEvent": false, "ConvolverNode": false, "CookieChangeEvent": false, "CookieDeprecationLabel": false, "cookieStore": false, "CookieStore": false, "CookieStoreManager": false, "CountQueuingStrategy": false, "createImageBitmap": false, "Credential": false, "credentialless": false, "CredentialsContainer": false, "CropTarget": false, "crossOriginIsolated": false, "crypto": false, "Crypto": false, "CryptoKey": false, "CSS": false, "CSSAnimation": false, "CSSConditionRule": false, "CSSContainerRule": false, "CSSCounterStyleRule": false, "CSSFontFaceRule": false, "CSSFontFeatureValuesRule": false, "CSSFontPaletteValuesRule": false, "CSSGroupingRule": false, "CSSImageValue": false, "CSSImportRule": false, "CSSKeyframeRule": false, "CSSKeyframesRule": false, "CSSKeywordValue": false, "CSSLayerBlockRule": false, "CSSLayerStatementRule": false, "CSSMarginRule": false, "CSSMathClamp": false, "CSSMathInvert": false, "CSSMathMax": false, "CSSMathMin": false, "CSSMathNegate": false, "CSSMathProduct": false, "CSSMathSum": false, "CSSMathValue": false, "CSSMatrixComponent": false, "CSSMediaRule": false, "CSSNamespaceRule": false, "CSSNestedDeclarations": false, "CSSNumericArray": false, "CSSNumericValue": false, "CSSPageDescriptors": false, "CSSPageRule": false, "CSSPerspective": false, "CSSPositionTryDescriptors": false, "CSSPositionTryRule": false, "CSSPositionValue": false, "CSSPropertyRule": false, "CSSRotate": false, "CSSRule": false, "CSSRuleList": false, "CSSScale": false, "CSSScopeRule": false, "CSSSkew": false, "CSSSkewX": false, "CSSSkewY": false, "CSSStartingStyleRule": false, "CSSStyleDeclaration": false, "CSSStyleRule": false, "CSSStyleSheet": false, "CSSStyleValue": false, "CSSSupportsRule": false, "CSSTransformComponent": false, "CSSTransformValue": false, "CSSTransition": false, "CSSTranslate": false, "CSSUnitValue": false, "CSSUnparsedValue": false, "CSSVariableReferenceValue": false, "CSSViewTransitionRule": false, "currentFrame": false, "currentTime": false, "CustomElementRegistry": false, "customElements": false, "CustomEvent": false, "CustomStateSet": false, "DataTransfer": false, "DataTransferItem": false, "DataTransferItemList": false, "DecompressionStream": false, "DelayNode": false, "DelegatedInkTrailPresenter": false, "DeviceMotionEvent": false, "DeviceMotionEventAcceleration": false, "DeviceMotionEventRotationRate": false, "DeviceOrientationEvent": false, "devicePixelRatio": false, "dispatchEvent": false, "document": false, "Document": false, "DocumentFragment": false, "documentPictureInPicture": false, "DocumentPictureInPicture": false, "DocumentPictureInPictureEvent": false, "DocumentTimeline": false, "DocumentType": false, "DOMError": false, "DOMException": false, "DOMImplementation": false, "DOMMatrix": false, "DOMMatrixReadOnly": false, "DOMParser": false, "DOMPoint": false, "DOMPointReadOnly": false, "DOMQuad": false, "DOMRect": false, "DOMRectList": false, "DOMRectReadOnly": false, "DOMStringList": false, "DOMStringMap": false, "DOMTokenList": false, "DragEvent": false, "DynamicsCompressorNode": false, "EditContext": false, "Element": false, "ElementInternals": false, "EncodedAudioChunk": false, "EncodedVideoChunk": false, "ErrorEvent": false, "event": false, "Event": false, "EventCounts": false, "EventSource": false, "EventTarget": false, "external": false, "External": false, "EyeDropper": false, "FeaturePolicy": false, "FederatedCredential": false, "fence": false, "Fence": false, "FencedFrameConfig": false, "fetch": false, "fetchLater": false, "FetchLaterResult": false, "File": false, "FileList": false, "FileReader": false, "FileSystem": false, "FileSystemDirectoryEntry": false, "FileSystemDirectoryHandle": false, "FileSystemDirectoryReader": false, "FileSystemEntry": false, "FileSystemFileEntry": false, "FileSystemFileHandle": false, "FileSystemHandle": false, "FileSystemWritableFileStream": false, "find": false, "Float16Array": false, "focus": false, "FocusEvent": false, "FontData": false, "FontFace": false, "FontFaceSet": false, "FontFaceSetLoadEvent": false, "FormData": false, "FormDataEvent": false, "FragmentDirective": false, "frameElement": false, "frames": false, "GainNode": false, "Gamepad": false, "GamepadAxisMoveEvent": false, "GamepadButton": false, "GamepadButtonEvent": false, "GamepadEvent": false, "GamepadHapticActuator": false, "GamepadPose": false, "Geolocation": false, "GeolocationCoordinates": false, "GeolocationPosition": false, "GeolocationPositionError": false, "getComputedStyle": false, "getScreenDetails": false, "getSelection": false, "GPU": false, "GPUAdapter": false, "GPUAdapterInfo": false, "GPUBindGroup": false, "GPUBindGroupLayout": false, "GPUBuffer": false, "GPUBufferUsage": false, "GPUCanvasContext": false, "GPUColorWrite": false, "GPUCommandBuffer": false, "GPUCommandEncoder": false, "GPUCompilationInfo": false, "GPUCompilationMessage": false, "GPUComputePassEncoder": false, "GPUComputePipeline": false, "GPUDevice": false, "GPUDeviceLostInfo": false, "GPUError": false, "GPUExternalTexture": false, "GPUInternalError": false, "GPUMapMode": false, "GPUOutOfMemoryError": false, "GPUPipelineError": false, "GPUPipelineLayout": false, "GPUQuerySet": false, "GPUQueue": false, "GPURenderBundle": false, "GPURenderBundleEncoder": false, "GPURenderPassEncoder": false, "GPURenderPipeline": false, "GPUSampler": false, "GPUShaderModule": false, "GPUShaderStage": false, "GPUSupportedFeatures": false, "GPUSupportedLimits": false, "GPUTexture": false, "GPUTextureUsage": false, "GPUTextureView": false, "GPUUncapturedErrorEvent": false, "GPUValidationError": false, "GravitySensor": false, "Gyroscope": false, "HashChangeEvent": false, "Headers": false, "HID": false, "HIDConnectionEvent": false, "HIDDevice": false, "HIDInputReportEvent": false, "Highlight": false, "HighlightRegistry": false, "history": false, "History": false, "HTMLAllCollection": false, "HTMLAnchorElement": false, "HTMLAreaElement": false, "HTMLAudioElement": false, "HTMLBaseElement": false, "HTMLBodyElement": false, "HTMLBRElement": false, "HTMLButtonElement": false, "HTMLCanvasElement": false, "HTMLCollection": false, "HTMLDataElement": false, "HTMLDataListElement": false, "HTMLDetailsElement": false, "HTMLDialogElement": false, "HTMLDirectoryElement": false, "HTMLDivElement": false, "HTMLDListElement": false, "HTMLDocument": false, "HTMLElement": false, "HTMLEmbedElement": false, "HTMLFencedFrameElement": false, "HTMLFieldSetElement": false, "HTMLFontElement": false, "HTMLFormControlsCollection": false, "HTMLFormElement": false, "HTMLFrameElement": false, "HTMLFrameSetElement": false, "HTMLHeadElement": false, "HTMLHeadingElement": false, "HTMLHRElement": false, "HTMLHtmlElement": false, "HTMLIFrameElement": false, "HTMLImageElement": false, "HTMLInputElement": false, "HTMLLabelElement": false, "HTMLLegendElement": false, "HTMLLIElement": false, "HTMLLinkElement": false, "HTMLMapElement": false, "HTMLMarqueeElement": false, "HTMLMediaElement": false, "HTMLMenuElement": false, "HTMLMetaElement": false, "HTMLMeterElement": false, "HTMLModElement": false, "HTMLObjectElement": false, "HTMLOListElement": false, "HTMLOptGroupElement": false, "HTMLOptionElement": false, "HTMLOptionsCollection": false, "HTMLOutputElement": false, "HTMLParagraphElement": false, "HTMLParamElement": false, "HTMLPictureElement": false, "HTMLPreElement": false, "HTMLProgressElement": false, "HTMLQuoteElement": false, "HTMLScriptElement": false, "HTMLSelectElement": false, "HTMLSlotElement": false, "HTMLSourceElement": false, "HTMLSpanElement": false, "HTMLStyleElement": false, "HTMLTableCaptionElement": false, "HTMLTableCellElement": false, "HTMLTableColElement": false, "HTMLTableElement": false, "HTMLTableRowElement": false, "HTMLTableSectionElement": false, "HTMLTemplateElement": false, "HTMLTextAreaElement": false, "HTMLTimeElement": false, "HTMLTitleElement": false, "HTMLTrackElement": false, "HTMLUListElement": false, "HTMLUnknownElement": false, "HTMLVideoElement": false, "IDBCursor": false, "IDBCursorWithValue": false, "IDBDatabase": false, "IDBFactory": false, "IDBIndex": false, "IDBKeyRange": false, "IDBObjectStore": false, "IDBOpenDBRequest": false, "IDBRequest": false, "IDBTransaction": false, "IDBVersionChangeEvent": false, "IdentityCredential": false, "IdentityCredentialError": false, "IdentityProvider": false, "IdleDeadline": false, "IdleDetector": false, "IIRFilterNode": false, "Image": false, "ImageBitmap": false, "ImageBitmapRenderingContext": false, "ImageCapture": false, "ImageData": false, "ImageDecoder": false, "ImageTrack": false, "ImageTrackList": false, "indexedDB": false, "Ink": false, "innerHeight": false, "innerWidth": false, "InputDeviceCapabilities": false, "InputDeviceInfo": false, "InputEvent": false, "IntersectionObserver": false, "IntersectionObserverEntry": false, "isSecureContext": false, "Keyboard": false, "KeyboardEvent": false, "KeyboardLayoutMap": false, "KeyframeEffect": false, "LargestContentfulPaint": false, "LaunchParams": false, "launchQueue": false, "LaunchQueue": false, "LayoutShift": false, "LayoutShiftAttribution": false, "length": false, "LinearAccelerationSensor": false, "localStorage": false, "location": true, "Location": false, "locationbar": false, "Lock": false, "LockManager": false, "matchMedia": false, "MathMLElement": false, "MediaCapabilities": false, "MediaCapabilitiesInfo": false, "MediaDeviceInfo": false, "MediaDevices": false, "MediaElementAudioSourceNode": false, "MediaEncryptedEvent": false, "MediaError": false, "MediaKeyError": false, "MediaKeyMessageEvent": false, "MediaKeys": false, "MediaKeySession": false, "MediaKeyStatusMap": false, "MediaKeySystemAccess": false, "MediaList": false, "MediaMetadata": false, "MediaQueryList": false, "MediaQueryListEvent": false, "MediaRecorder": false, "MediaRecorderErrorEvent": false, "MediaSession": false, "MediaSource": false, "MediaSourceHandle": false, "MediaStream": false, "MediaStreamAudioDestinationNode": false, "MediaStreamAudioSourceNode": false, "MediaStreamEvent": false, "MediaStreamTrack": false, "MediaStreamTrackAudioSourceNode": false, "MediaStreamTrackAudioStats": false, "MediaStreamTrackEvent": false, "MediaStreamTrackGenerator": false, "MediaStreamTrackProcessor": false, "MediaStreamTrackVideoStats": false, "menubar": false, "MessageChannel": false, "MessageEvent": false, "MessagePort": false, "MIDIAccess": false, "MIDIConnectionEvent": false, "MIDIInput": false, "MIDIInputMap": false, "MIDIMessageEvent": false, "MIDIOutput": false, "MIDIOutputMap": false, "MIDIPort": false, "MimeType": false, "MimeTypeArray": false, "model": false, "ModelGenericSession": false, "ModelManager": false, "MouseEvent": false, "moveBy": false, "moveTo": false, "MutationEvent": false, "MutationObserver": false, "MutationRecord": false, "name": false, "NamedNodeMap": false, "NavigateEvent": false, "navigation": false, "Navigation": false, "NavigationActivation": false, "NavigationCurrentEntryChangeEvent": false, "NavigationDestination": false, "NavigationHistoryEntry": false, "NavigationPreloadManager": false, "NavigationTransition": false, "navigator": false, "Navigator": false, "NavigatorLogin": false, "NavigatorManagedData": false, "NavigatorUAData": false, "NetworkInformation": false, "Node": false, "NodeFilter": false, "NodeIterator": false, "NodeList": false, "Notification": false, "NotifyPaintEvent": false, "NotRestoredReasonDetails": false, "NotRestoredReasons": false, "OfflineAudioCompletionEvent": false, "OfflineAudioContext": false, "offscreenBuffering": false, "OffscreenCanvas": false, "OffscreenCanvasRenderingContext2D": false, "onabort": true, "onafterprint": true, "onanimationcancel": true, "onanimationend": true, "onanimationiteration": true, "onanimationstart": true, "onappinstalled": true, "onauxclick": true, "onbeforeinput": true, "onbeforeinstallprompt": true, "onbeforematch": true, "onbeforeprint": true, "onbeforetoggle": true, "onbeforeunload": true, "onbeforexrselect": true, "onblur": true, "oncancel": true, "oncanplay": true, "oncanplaythrough": true, "onchange": true, "onclick": true, "onclose": true, "oncontentvisibilityautostatechange": true, "oncontextlost": true, "oncontextmenu": true, "oncontextrestored": true, "oncopy": true, "oncuechange": true, "oncut": true, "ondblclick": true, "ondevicemotion": true, "ondeviceorientation": true, "ondeviceorientationabsolute": true, "ondrag": true, "ondragend": true, "ondragenter": true, "ondragleave": true, "ondragover": true, "ondragstart": true, "ondrop": true, "ondurationchange": true, "onemptied": true, "onended": true, "onerror": true, "onfocus": true, "onformdata": true, "ongamepadconnected": true, "ongamepaddisconnected": true, "ongotpointercapture": true, "onhashchange": true, "oninput": true, "oninvalid": true, "onkeydown": true, "onkeypress": true, "onkeyup": true, "onlanguagechange": true, "onload": true, "onloadeddata": true, "onloadedmetadata": true, "onloadstart": true, "onlostpointercapture": true, "onmessage": true, "onmessageerror": true, "onmousedown": true, "onmouseenter": true, "onmouseleave": true, "onmousemove": true, "onmouseout": true, "onmouseover": true, "onmouseup": true, "onmousewheel": true, "onoffline": true, "ononline": true, "onpagehide": true, "onpagereveal": true, "onpageshow": true, "onpageswap": true, "onpaste": true, "onpause": true, "onplay": true, "onplaying": true, "onpointercancel": true, "onpointerdown": true, "onpointerenter": true, "onpointerleave": true, "onpointermove": true, "onpointerout": true, "onpointerover": true, "onpointerrawupdate": true, "onpointerup": true, "onpopstate": true, "onprogress": true, "onratechange": true, "onrejectionhandled": true, "onreset": true, "onresize": true, "onscroll": true, "onscrollend": true, "onscrollsnapchange": true, "onscrollsnapchanging": true, "onsearch": true, "onsecuritypolicyviolation": true, "onseeked": true, "onseeking": true, "onselect": true, "onselectionchange": true, "onselectstart": true, "onslotchange": true, "onstalled": true, "onstorage": true, "onsubmit": true, "onsuspend": true, "ontimeupdate": true, "ontoggle": true, "ontransitioncancel": true, "ontransitionend": true, "ontransitionrun": true, "ontransitionstart": true, "onunhandledrejection": true, "onunload": true, "onvolumechange": true, "onwaiting": true, "onwheel": true, "open": false, "opener": false, "Option": false, "OrientationSensor": false, "origin": false, "originAgentCluster": false, "OscillatorNode": false, "OTPCredential": false, "outerHeight": false, "outerWidth": false, "OverconstrainedError": false, "PageRevealEvent": false, "PageSwapEvent": false, "PageTransitionEvent": false, "pageXOffset": false, "pageYOffset": false, "PannerNode": false, "parent": false, "PasswordCredential": false, "Path2D": false, "PaymentAddress": false, "PaymentManager": false, "PaymentMethodChangeEvent": false, "PaymentRequest": false, "PaymentRequestUpdateEvent": false, "PaymentResponse": false, "performance": false, "Performance": false, "PerformanceElementTiming": false, "PerformanceEntry": false, "PerformanceEventTiming": false, "PerformanceLongAnimationFrameTiming": false, "PerformanceLongTaskTiming": false, "PerformanceMark": false, "PerformanceMeasure": false, "PerformanceNavigation": false, "PerformanceNavigationTiming": false, "PerformanceObserver": false, "PerformanceObserverEntryList": false, "PerformancePaintTiming": false, "PerformanceResourceTiming": false, "PerformanceScriptTiming": false, "PerformanceServerTiming": false, "PerformanceTiming": false, "PeriodicSyncManager": false, "PeriodicWave": false, "Permissions": false, "PermissionStatus": false, "PERSISTENT": false, "personalbar": false, "PictureInPictureEvent": false, "PictureInPictureWindow": false, "Plugin": false, "PluginArray": false, "PointerEvent": false, "PopStateEvent": false, "postMessage": false, "Presentation": false, "PresentationAvailability": false, "PresentationConnection": false, "PresentationConnectionAvailableEvent": false, "PresentationConnectionCloseEvent": false, "PresentationConnectionList": false, "PresentationReceiver": false, "PresentationRequest": false, "PressureObserver": false, "PressureRecord": false, "print": false, "ProcessingInstruction": false, "Profiler": false, "ProgressEvent": false, "PromiseRejectionEvent": false, "prompt": false, "ProtectedAudience": false, "PublicKeyCredential": false, "PushManager": false, "PushSubscription": false, "PushSubscriptionOptions": false, "queryLocalFonts": false, "queueMicrotask": false, "RadioNodeList": false, "Range": false, "ReadableByteStreamController": false, "ReadableStream": false, "ReadableStreamBYOBReader": false, "ReadableStreamBYOBRequest": false, "ReadableStreamDefaultController": false, "ReadableStreamDefaultReader": false, "registerProcessor": false, "RelativeOrientationSensor": false, "RemotePlayback": false, "removeEventListener": false, "reportError": false, "ReportingObserver": false, "Request": false, "requestAnimationFrame": false, "requestIdleCallback": false, "resizeBy": false, "ResizeObserver": false, "ResizeObserverEntry": false, "ResizeObserverSize": false, "resizeTo": false, "Response": false, "RTCCertificate": false, "RTCDataChannel": false, "RTCDataChannelEvent": false, "RTCDtlsTransport": false, "RTCDTMFSender": false, "RTCDTMFToneChangeEvent": false, "RTCEncodedAudioFrame": false, "RTCEncodedVideoFrame": false, "RTCError": false, "RTCErrorEvent": false, "RTCIceCandidate": false, "RTCIceTransport": false, "RTCPeerConnection": false, "RTCPeerConnectionIceErrorEvent": false, "RTCPeerConnectionIceEvent": false, "RTCRtpReceiver": false, "RTCRtpScriptTransform": false, "RTCRtpSender": false, "RTCRtpTransceiver": false, "RTCSctpTransport": false, "RTCSessionDescription": false, "RTCStatsReport": false, "RTCTrackEvent": false, "sampleRate": false, "scheduler": false, "Scheduler": false, "Scheduling": false, "screen": false, "Screen": false, "ScreenDetailed": false, "ScreenDetails": false, "screenLeft": false, "ScreenOrientation": false, "screenTop": false, "screenX": false, "screenY": false, "ScriptProcessorNode": false, "scroll": false, "scrollbars": false, "scrollBy": false, "ScrollTimeline": false, "scrollTo": false, "scrollX": false, "scrollY": false, "SecurityPolicyViolationEvent": false, "Selection": false, "self": false, "Sensor": false, "SensorErrorEvent": false, "Serial": false, "SerialPort": false, "ServiceWorker": false, "ServiceWorkerContainer": false, "ServiceWorkerRegistration": false, "sessionStorage": false, "setInterval": false, "setTimeout": false, "ShadowRoot": false, "sharedStorage": false, "SharedStorage": false, "SharedStorageWorklet": false, "SharedWorker": false, "showDirectoryPicker": false, "showOpenFilePicker": false, "showSaveFilePicker": false, "SnapEvent": false, "SourceBuffer": false, "SourceBufferList": false, "speechSynthesis": false, "SpeechSynthesis": false, "SpeechSynthesisErrorEvent": false, "SpeechSynthesisEvent": false, "SpeechSynthesisUtterance": false, "SpeechSynthesisVoice": false, "StaticRange": false, "status": false, "statusbar": false, "StereoPannerNode": false, "stop": false, "Storage": false, "StorageBucket": false, "StorageBucketManager": false, "StorageEvent": false, "StorageManager": false, "structuredClone": false, "styleMedia": false, "StylePropertyMap": false, "StylePropertyMapReadOnly": false, "StyleSheet": false, "StyleSheetList": false, "SubmitEvent": false, "SubtleCrypto": false, "SVGAElement": false, "SVGAngle": false, "SVGAnimatedAngle": false, "SVGAnimatedBoolean": false, "SVGAnimatedEnumeration": false, "SVGAnimatedInteger": false, "SVGAnimatedLength": false, "SVGAnimatedLengthList": false, "SVGAnimatedNumber": false, "SVGAnimatedNumberList": false, "SVGAnimatedPreserveAspectRatio": false, "SVGAnimatedRect": false, "SVGAnimatedString": false, "SVGAnimatedTransformList": false, "SVGAnimateElement": false, "SVGAnimateMotionElement": false, "SVGAnimateTransformElement": false, "SVGAnimationElement": false, "SVGCircleElement": false, "SVGClipPathElement": false, "SVGComponentTransferFunctionElement": false, "SVGDefsElement": false, "SVGDescElement": false, "SVGElement": false, "SVGEllipseElement": false, "SVGFEBlendElement": false, "SVGFEColorMatrixElement": false, "SVGFEComponentTransferElement": false, "SVGFECompositeElement": false, "SVGFEConvolveMatrixElement": false, "SVGFEDiffuseLightingElement": false, "SVGFEDisplacementMapElement": false, "SVGFEDistantLightElement": false, "SVGFEDropShadowElement": false, "SVGFEFloodElement": false, "SVGFEFuncAElement": false, "SVGFEFuncBElement": false, "SVGFEFuncGElement": false, "SVGFEFuncRElement": false, "SVGFEGaussianBlurElement": false, "SVGFEImageElement": false, "SVGFEMergeElement": false, "SVGFEMergeNodeElement": false, "SVGFEMorphologyElement": false, "SVGFEOffsetElement": false, "SVGFEPointLightElement": false, "SVGFESpecularLightingElement": false, "SVGFESpotLightElement": false, "SVGFETileElement": false, "SVGFETurbulenceElement": false, "SVGFilterElement": false, "SVGForeignObjectElement": false, "SVGGElement": false, "SVGGeometryElement": false, "SVGGradientElement": false, "SVGGraphicsElement": false, "SVGImageElement": false, "SVGLength": false, "SVGLengthList": false, "SVGLinearGradientElement": false, "SVGLineElement": false, "SVGMarkerElement": false, "SVGMaskElement": false, "SVGMatrix": false, "SVGMetadataElement": false, "SVGMPathElement": false, "SVGNumber": false, "SVGNumberList": false, "SVGPathElement": false, "SVGPatternElement": false, "SVGPoint": false, "SVGPointList": false, "SVGPolygonElement": false, "SVGPolylineElement": false, "SVGPreserveAspectRatio": false, "SVGRadialGradientElement": false, "SVGRect": false, "SVGRectElement": false, "SVGScriptElement": false, "SVGSetElement": false, "SVGStopElement": false, "SVGStringList": false, "SVGStyleElement": false, "SVGSVGElement": false, "SVGSwitchElement": false, "SVGSymbolElement": false, "SVGTextContentElement": false, "SVGTextElement": false, "SVGTextPathElement": false, "SVGTextPositioningElement": false, "SVGTitleElement": false, "SVGTransform": false, "SVGTransformList": false, "SVGTSpanElement": false, "SVGUnitTypes": false, "SVGUseElement": false, "SVGViewElement": false, "SyncManager": false, "TaskAttributionTiming": false, "TaskController": false, "TaskPriorityChangeEvent": false, "TaskSignal": false, "TEMPORARY": false, "Text": false, "TextDecoder": false, "TextDecoderStream": false, "TextEncoder": false, "TextEncoderStream": false, "TextEvent": false, "TextFormat": false, "TextFormatUpdateEvent": false, "TextMetrics": false, "TextTrack": false, "TextTrackCue": false, "TextTrackCueList": false, "TextTrackList": false, "TextUpdateEvent": false, "TimeEvent": false, "TimeRanges": false, "ToggleEvent": false, "toolbar": false, "top": false, "Touch": false, "TouchEvent": false, "TouchList": false, "TrackEvent": false, "TransformStream": false, "TransformStreamDefaultController": false, "TransitionEvent": false, "TreeWalker": false, "TrustedHTML": false, "TrustedScript": false, "TrustedScriptURL": false, "TrustedTypePolicy": false, "TrustedTypePolicyFactory": false, "trustedTypes": false, "UIEvent": false, "URL": false, "URLPattern": false, "URLSearchParams": false, "USB": false, "USBAlternateInterface": false, "USBConfiguration": false, "USBConnectionEvent": false, "USBDevice": false, "USBEndpoint": false, "USBInterface": false, "USBInTransferResult": false, "USBIsochronousInTransferPacket": false, "USBIsochronousInTransferResult": false, "USBIsochronousOutTransferPacket": false, "USBIsochronousOutTransferResult": false, "USBOutTransferResult": false, "UserActivation": false, "ValidityState": false, "VideoColorSpace": false, "VideoDecoder": false, "VideoEncoder": false, "VideoFrame": false, "VideoPlaybackQuality": false, "ViewTimeline": false, "ViewTransition": false, "ViewTransitionTypeSet": false, "VirtualKeyboard": false, "VirtualKeyboardGeometryChangeEvent": false, "VisibilityStateEntry": false, "visualViewport": false, "VisualViewport": false, "VTTCue": false, "VTTRegion": false, "WakeLock": false, "WakeLockSentinel": false, "WaveShaperNode": false, "WebAssembly": false, "WebGL2RenderingContext": false, "WebGLActiveInfo": false, "WebGLBuffer": false, "WebGLContextEvent": false, "WebGLFramebuffer": false, "WebGLObject": false, "WebGLProgram": false, "WebGLQuery": false, "WebGLRenderbuffer": false, "WebGLRenderingContext": false, "WebGLSampler": false, "WebGLShader": false, "WebGLShaderPrecisionFormat": false, "WebGLSync": false, "WebGLTexture": false, "WebGLTransformFeedback": false, "WebGLUniformLocation": false, "WebGLVertexArrayObject": false, "WebSocket": false, "WebSocketError": false, "WebSocketStream": false, "WebTransport": false, "WebTransportBidirectionalStream": false, "WebTransportDatagramDuplexStream": false, "WebTransportError": false, "WebTransportReceiveStream": false, "WebTransportSendStream": false, "WGSLLanguageFeatures": false, "WheelEvent": false, "window": false, "Window": false, "WindowControlsOverlay": false, "WindowControlsOverlayGeometryChangeEvent": false, "Worker": false, "Worklet": false, "WorkletGlobalScope": false, "WritableStream": false, "WritableStreamDefaultController": false, "WritableStreamDefaultWriter": false, "XMLDocument": false, "XMLHttpRequest": false, "XMLHttpRequestEventTarget": false, "XMLHttpRequestUpload": false, "XMLSerializer": false, "XPathEvaluator": false, "XPathExpression": false, "XPathResult": false, "XRAnchor": false, "XRAnchorSet": false, "XRBoundedReferenceSpace": false, "XRCamera": false, "XRCPUDepthInformation": false, "XRDepthInformation": false, "XRDOMOverlayState": false, "XRFrame": false, "XRHand": false, "XRHitTestResult": false, "XRHitTestSource": false, "XRInputSource": false, "XRInputSourceArray": false, "XRInputSourceEvent": false, "XRInputSourcesChangeEvent": false, "XRJointPose": false, "XRJointSpace": false, "XRLayer": false, "XRLightEstimate": false, "XRLightProbe": false, "XRPose": false, "XRRay": false, "XRReferenceSpace": false, "XRReferenceSpaceEvent": false, "XRRenderState": false, "XRRigidTransform": false, "XRSession": false, "XRSessionEvent": false, "XRSpace": false, "XRSystem": false, "XRTransientInputHitTestResult": false, "XRTransientInputHitTestSource": false, "XRView": false, "XRViewerPose": false, "XRViewport": false, "XRWebGLBinding": false, "XRWebGLDepthInformation": false, "XRWebGLLayer": false, "XSLTProcessor": false}, "builtin": {"AggregateError": false, "Array": false, "ArrayBuffer": false, "Atomics": false, "BigInt": false, "BigInt64Array": false, "BigUint64Array": false, "Boolean": false, "DataView": false, "Date": false, "decodeURI": false, "decodeURIComponent": false, "encodeURI": false, "encodeURIComponent": false, "Error": false, "escape": false, "eval": false, "EvalError": false, "FinalizationRegistry": false, "Float32Array": false, "Float64Array": false, "Function": false, "globalThis": false, "Infinity": false, "Int16Array": false, "Int32Array": false, "Int8Array": false, "Intl": false, "isFinite": false, "isNaN": false, "Iterator": false, "JSON": false, "Map": false, "Math": false, "NaN": false, "Number": false, "Object": false, "parseFloat": false, "parseInt": false, "Promise": false, "Proxy": false, "RangeError": false, "ReferenceError": false, "Reflect": false, "RegExp": false, "Set": false, "SharedArrayBuffer": false, "String": false, "Symbol": false, "SyntaxError": false, "TypeError": false, "Uint16Array": false, "Uint32Array": false, "Uint8Array": false, "Uint8ClampedArray": false, "undefined": false, "unescape": false, "URIError": false, "WeakMap": false, "WeakRef": false, "WeakSet": false}, "chai": {"assert": true, "expect": true, "should": true}, "commonjs": {"exports": true, "global": false, "module": false, "require": false}, "couch": {"emit": false, "exports": false, "getRow": false, "log": false, "module": false, "provides": false, "require": false, "respond": false, "send": false, "start": false, "sum": false}, "devtools": {"$": false, "$_": false, "$$": false, "$0": false, "$1": false, "$2": false, "$3": false, "$4": false, "$x": false, "chrome": false, "clear": false, "copy": false, "debug": false, "dir": false, "dirxml": false, "getEventListeners": false, "inspect": false, "keys": false, "monitor": false, "monitorEvents": false, "profile": false, "profileEnd": false, "queryObjects": false, "table": false, "undebug": false, "unmonitor": false, "unmonitorEvents": false, "values": false}, "embertest": {"andThen": false, "click": false, "currentPath": false, "currentRouteName": false, "currentURL": false, "fillIn": false, "find": false, "findAll": false, "findWithAssert": false, "keyEvent": false, "pauseTest": false, "resumeTest": false, "triggerEvent": false, "visit": false, "wait": false}, "es2015": {"Array": false, "ArrayBuffer": false, "Boolean": false, "DataView": false, "Date": false, "decodeURI": false, "decodeURIComponent": false, "encodeURI": false, "encodeURIComponent": false, "Error": false, "escape": false, "eval": false, "EvalError": false, "Float32Array": false, "Float64Array": false, "Function": false, "Infinity": false, "Int16Array": false, "Int32Array": false, "Int8Array": false, "Intl": false, "isFinite": false, "isNaN": false, "JSON": false, "Map": false, "Math": false, "NaN": false, "Number": false, "Object": false, "parseFloat": false, "parseInt": false, "Promise": false, "Proxy": false, "RangeError": false, "ReferenceError": false, "Reflect": false, "RegExp": false, "Set": false, "String": false, "Symbol": false, "SyntaxError": false, "TypeError": false, "Uint16Array": false, "Uint32Array": false, "Uint8Array": false, "Uint8ClampedArray": false, "undefined": false, "unescape": false, "URIError": false, "WeakMap": false, "WeakSet": false}, "es2016": {"Array": false, "ArrayBuffer": false, "Boolean": false, "DataView": false, "Date": false, "decodeURI": false, "decodeURIComponent": false, "encodeURI": false, "encodeURIComponent": false, "Error": false, "escape": false, "eval": false, "EvalError": false, "Float32Array": false, "Float64Array": false, "Function": false, "Infinity": false, "Int16Array": false, "Int32Array": false, "Int8Array": false, "Intl": false, "isFinite": false, "isNaN": false, "JSON": false, "Map": false, "Math": false, "NaN": false, "Number": false, "Object": false, "parseFloat": false, "parseInt": false, "Promise": false, "Proxy": false, "RangeError": false, "ReferenceError": false, "Reflect": false, "RegExp": false, "Set": false, "String": false, "Symbol": false, "SyntaxError": false, "TypeError": false, "Uint16Array": false, "Uint32Array": false, "Uint8Array": false, "Uint8ClampedArray": false, "undefined": false, "unescape": false, "URIError": false, "WeakMap": false, "WeakSet": false}, "es2017": {"Array": false, "ArrayBuffer": false, "Atomics": false, "Boolean": false, "DataView": false, "Date": false, "decodeURI": false, "decodeURIComponent": false, "encodeURI": false, "encodeURIComponent": false, "Error": false, "escape": false, "eval": false, "EvalError": false, "Float32Array": false, "Float64Array": false, "Function": false, "Infinity": false, "Int16Array": false, "Int32Array": false, "Int8Array": false, "Intl": false, "isFinite": false, "isNaN": false, "JSON": false, "Map": false, "Math": false, "NaN": false, "Number": false, "Object": false, "parseFloat": false, "parseInt": false, "Promise": false, "Proxy": false, "RangeError": false, "ReferenceError": false, "Reflect": false, "RegExp": false, "Set": false, "SharedArrayBuffer": false, "String": false, "Symbol": false, "SyntaxError": false, "TypeError": false, "Uint16Array": false, "Uint32Array": false, "Uint8Array": false, "Uint8ClampedArray": false, "undefined": false, "unescape": false, "URIError": false, "WeakMap": false, "WeakSet": false}, "es2018": {"Array": false, "ArrayBuffer": false, "Atomics": false, "Boolean": false, "DataView": false, "Date": false, "decodeURI": false, "decodeURIComponent": false, "encodeURI": false, "encodeURIComponent": false, "Error": false, "escape": false, "eval": false, "EvalError": false, "Float32Array": false, "Float64Array": false, "Function": false, "Infinity": false, "Int16Array": false, "Int32Array": false, "Int8Array": false, "Intl": false, "isFinite": false, "isNaN": false, "JSON": false, "Map": false, "Math": false, "NaN": false, "Number": false, "Object": false, "parseFloat": false, "parseInt": false, "Promise": false, "Proxy": false, "RangeError": false, "ReferenceError": false, "Reflect": false, "RegExp": false, "Set": false, "SharedArrayBuffer": false, "String": false, "Symbol": false, "SyntaxError": false, "TypeError": false, "Uint16Array": false, "Uint32Array": false, "Uint8Array": false, "Uint8ClampedArray": false, "undefined": false, "unescape": false, "URIError": false, "WeakMap": false, "WeakSet": false}, "es2019": {"Array": false, "ArrayBuffer": false, "Atomics": false, "Boolean": false, "DataView": false, "Date": false, "decodeURI": false, "decodeURIComponent": false, "encodeURI": false, "encodeURIComponent": false, "Error": false, "escape": false, "eval": false, "EvalError": false, "Float32Array": false, "Float64Array": false, "Function": false, "Infinity": false, "Int16Array": false, "Int32Array": false, "Int8Array": false, "Intl": false, "isFinite": false, "isNaN": false, "JSON": false, "Map": false, "Math": false, "NaN": false, "Number": false, "Object": false, "parseFloat": false, "parseInt": false, "Promise": false, "Proxy": false, "RangeError": false, "ReferenceError": false, "Reflect": false, "RegExp": false, "Set": false, "SharedArrayBuffer": false, "String": false, "Symbol": false, "SyntaxError": false, "TypeError": false, "Uint16Array": false, "Uint32Array": false, "Uint8Array": false, "Uint8ClampedArray": false, "undefined": false, "unescape": false, "URIError": false, "WeakMap": false, "WeakSet": false}, "es2020": {"Array": false, "ArrayBuffer": false, "Atomics": false, "BigInt": false, "BigInt64Array": false, "BigUint64Array": false, "Boolean": false, "DataView": false, "Date": false, "decodeURI": false, "decodeURIComponent": false, "encodeURI": false, "encodeURIComponent": false, "Error": false, "escape": false, "eval": false, "EvalError": false, "Float32Array": false, "Float64Array": false, "Function": false, "globalThis": false, "Infinity": false, "Int16Array": false, "Int32Array": false, "Int8Array": false, "Intl": false, "isFinite": false, "isNaN": false, "JSON": false, "Map": false, "Math": false, "NaN": false, "Number": false, "Object": false, "parseFloat": false, "parseInt": false, "Promise": false, "Proxy": false, "RangeError": false, "ReferenceError": false, "Reflect": false, "RegExp": false, "Set": false, "SharedArrayBuffer": false, "String": false, "Symbol": false, "SyntaxError": false, "TypeError": false, "Uint16Array": false, "Uint32Array": false, "Uint8Array": false, "Uint8ClampedArray": false, "undefined": false, "unescape": false, "URIError": false, "WeakMap": false, "WeakSet": false}, "es2021": {"AggregateError": false, "Array": false, "ArrayBuffer": false, "Atomics": false, "BigInt": false, "BigInt64Array": false, "BigUint64Array": false, "Boolean": false, "DataView": false, "Date": false, "decodeURI": false, "decodeURIComponent": false, "encodeURI": false, "encodeURIComponent": false, "Error": false, "escape": false, "eval": false, "EvalError": false, "FinalizationRegistry": false, "Float32Array": false, "Float64Array": false, "Function": false, "globalThis": false, "Infinity": false, "Int16Array": false, "Int32Array": false, "Int8Array": false, "Intl": false, "isFinite": false, "isNaN": false, "JSON": false, "Map": false, "Math": false, "NaN": false, "Number": false, "Object": false, "parseFloat": false, "parseInt": false, "Promise": false, "Proxy": false, "RangeError": false, "ReferenceError": false, "Reflect": false, "RegExp": false, "Set": false, "SharedArrayBuffer": false, "String": false, "Symbol": false, "SyntaxError": false, "TypeError": false, "Uint16Array": false, "Uint32Array": false, "Uint8Array": false, "Uint8ClampedArray": false, "undefined": false, "unescape": false, "URIError": false, "WeakMap": false, "WeakRef": false, "WeakSet": false}, "es2022": {"AggregateError": false, "Array": false, "ArrayBuffer": false, "Atomics": false, "BigInt": false, "BigInt64Array": false, "BigUint64Array": false, "Boolean": false, "DataView": false, "Date": false, "decodeURI": false, "decodeURIComponent": false, "encodeURI": false, "encodeURIComponent": false, "Error": false, "escape": false, "eval": false, "EvalError": false, "FinalizationRegistry": false, "Float32Array": false, "Float64Array": false, "Function": false, "globalThis": false, "Infinity": false, "Int16Array": false, "Int32Array": false, "Int8Array": false, "Intl": false, "isFinite": false, "isNaN": false, "JSON": false, "Map": false, "Math": false, "NaN": false, "Number": false, "Object": false, "parseFloat": false, "parseInt": false, "Promise": false, "Proxy": false, "RangeError": false, "ReferenceError": false, "Reflect": false, "RegExp": false, "Set": false, "SharedArrayBuffer": false, "String": false, "Symbol": false, "SyntaxError": false, "TypeError": false, "Uint16Array": false, "Uint32Array": false, "Uint8Array": false, "Uint8ClampedArray": false, "undefined": false, "unescape": false, "URIError": false, "WeakMap": false, "WeakRef": false, "WeakSet": false}, "es2023": {"AggregateError": false, "Array": false, "ArrayBuffer": false, "Atomics": false, "BigInt": false, "BigInt64Array": false, "BigUint64Array": false, "Boolean": false, "DataView": false, "Date": false, "decodeURI": false, "decodeURIComponent": false, "encodeURI": false, "encodeURIComponent": false, "Error": false, "escape": false, "eval": false, "EvalError": false, "FinalizationRegistry": false, "Float32Array": false, "Float64Array": false, "Function": false, "globalThis": false, "Infinity": false, "Int16Array": false, "Int32Array": false, "Int8Array": false, "Intl": false, "isFinite": false, "isNaN": false, "JSON": false, "Map": false, "Math": false, "NaN": false, "Number": false, "Object": false, "parseFloat": false, "parseInt": false, "Promise": false, "Proxy": false, "RangeError": false, "ReferenceError": false, "Reflect": false, "RegExp": false, "Set": false, "SharedArrayBuffer": false, "String": false, "Symbol": false, "SyntaxError": false, "TypeError": false, "Uint16Array": false, "Uint32Array": false, "Uint8Array": false, "Uint8ClampedArray": false, "undefined": false, "unescape": false, "URIError": false, "WeakMap": false, "WeakRef": false, "WeakSet": false}, "es2024": {"AggregateError": false, "Array": false, "ArrayBuffer": false, "Atomics": false, "BigInt": false, "BigInt64Array": false, "BigUint64Array": false, "Boolean": false, "DataView": false, "Date": false, "decodeURI": false, "decodeURIComponent": false, "encodeURI": false, "encodeURIComponent": false, "Error": false, "escape": false, "eval": false, "EvalError": false, "FinalizationRegistry": false, "Float32Array": false, "Float64Array": false, "Function": false, "globalThis": false, "Infinity": false, "Int16Array": false, "Int32Array": false, "Int8Array": false, "Intl": false, "isFinite": false, "isNaN": false, "JSON": false, "Map": false, "Math": false, "NaN": false, "Number": false, "Object": false, "parseFloat": false, "parseInt": false, "Promise": false, "Proxy": false, "RangeError": false, "ReferenceError": false, "Reflect": false, "RegExp": false, "Set": false, "SharedArrayBuffer": false, "String": false, "Symbol": false, "SyntaxError": false, "TypeError": false, "Uint16Array": false, "Uint32Array": false, "Uint8Array": false, "Uint8ClampedArray": false, "undefined": false, "unescape": false, "URIError": false, "WeakMap": false, "WeakRef": false, "WeakSet": false}, "es2025": {"AggregateError": false, "Array": false, "ArrayBuffer": false, "Atomics": false, "BigInt": false, "BigInt64Array": false, "BigUint64Array": false, "Boolean": false, "DataView": false, "Date": false, "decodeURI": false, "decodeURIComponent": false, "encodeURI": false, "encodeURIComponent": false, "Error": false, "escape": false, "eval": false, "EvalError": false, "FinalizationRegistry": false, "Float32Array": false, "Float64Array": false, "Function": false, "globalThis": false, "Infinity": false, "Int16Array": false, "Int32Array": false, "Int8Array": false, "Intl": false, "isFinite": false, "isNaN": false, "Iterator": false, "JSON": false, "Map": false, "Math": false, "NaN": false, "Number": false, "Object": false, "parseFloat": false, "parseInt": false, "Promise": false, "Proxy": false, "RangeError": false, "ReferenceError": false, "Reflect": false, "RegExp": false, "Set": false, "SharedArrayBuffer": false, "String": false, "Symbol": false, "SyntaxError": false, "TypeError": false, "Uint16Array": false, "Uint32Array": false, "Uint8Array": false, "Uint8ClampedArray": false, "undefined": false, "unescape": false, "URIError": false, "WeakMap": false, "WeakRef": false, "WeakSet": false}, "es3": {"Array": false, "Boolean": false, "Date": false, "decodeURI": false, "decodeURIComponent": false, "encodeURI": false, "encodeURIComponent": false, "Error": false, "escape": false, "eval": false, "EvalError": false, "Function": false, "Infinity": false, "isFinite": false, "isNaN": false, "Math": false, "NaN": false, "Number": false, "Object": false, "parseFloat": false, "parseInt": false, "RangeError": false, "ReferenceError": false, "RegExp": false, "String": false, "SyntaxError": false, "TypeError": false, "undefined": false, "unescape": false, "URIError": false}, "es5": {"Array": false, "Boolean": false, "Date": false, "decodeURI": false, "decodeURIComponent": false, "encodeURI": false, "encodeURIComponent": false, "Error": false, "escape": false, "eval": false, "EvalError": false, "Function": false, "Infinity": false, "isFinite": false, "isNaN": false, "JSON": false, "Math": false, "NaN": false, "Number": false, "Object": false, "parseFloat": false, "parseInt": false, "RangeError": false, "ReferenceError": false, "RegExp": false, "String": false, "SyntaxError": false, "TypeError": false, "undefined": false, "unescape": false, "URIError": false}, "greasemonkey": {"cloneInto": false, "createObjectIn": false, "exportFunction": false, "GM": false, "GM_addElement": false, "GM_addStyle": false, "GM_addValueChangeListener": false, "GM_deleteValue": false, "GM_download": false, "GM_getResourceText": false, "GM_getResourceURL": false, "GM_getTab": false, "GM_getTabs": false, "GM_getValue": false, "GM_info": false, "GM_listValues": false, "GM_log": false, "GM_notification": false, "GM_openInTab": false, "GM_registerMenuCommand": false, "GM_removeValueChangeListener": false, "GM_saveTab": false, "GM_setClipboard": false, "GM_setValue": false, "GM_unregisterMenuCommand": false, "GM_xmlhttpRequest": false, "unsafeWindow": false}, "jasmine": {"afterAll": false, "afterEach": false, "beforeAll": false, "beforeEach": false, "describe": false, "expect": false, "expectAsync": false, "fail": false, "fdescribe": false, "fit": false, "it": false, "jasmine": false, "pending": false, "runs": false, "spyOn": false, "spyOnAllFunctions": false, "spyOnProperty": false, "waits": false, "waitsFor": false, "xdescribe": false, "xit": false}, "jest": {"afterAll": false, "afterEach": false, "beforeAll": false, "beforeEach": false, "describe": false, "expect": false, "fit": false, "it": false, "jest": false, "test": false, "xdescribe": false, "xit": false, "xtest": false}, "jquery": {"$": false, "jQuery": false}, "meteor": {"$": false, "Accounts": false, "AccountsClient": false, "AccountsCommon": false, "AccountsServer": false, "App": false, "Assets": false, "Blaze": false, "check": false, "Cordova": false, "DDP": false, "DDPRateLimiter": false, "DDPServer": false, "Deps": false, "EJSON": false, "Email": false, "HTTP": false, "Log": false, "Match": false, "Meteor": false, "Mongo": false, "MongoInternals": false, "Npm": false, "Package": false, "Plugin": false, "process": false, "Random": false, "ReactiveDict": false, "ReactiveVar": false, "Router": false, "ServiceConfiguration": false, "Session": false, "share": false, "Spacebars": false, "Template": false, "Tinytest": false, "Tracker": false, "UI": false, "Utils": false, "WebApp": false, "WebAppInternals": false}, "mocha": {"after": false, "afterEach": false, "before": false, "beforeEach": false, "context": false, "describe": false, "it": false, "mocha": false, "run": false, "setup": false, "specify": false, "suite": false, "suiteSetup": false, "suiteTeardown": false, "teardown": false, "test": false, "xcontext": false, "xdescribe": false, "xit": false, "xspecify": false}, "mongo": {"_isWindows": false, "_rand": false, "BulkWriteResult": false, "cat": false, "cd": false, "connect": false, "db": false, "getHostName": false, "getMemInfo": false, "hostname": false, "ISODate": false, "listFiles": false, "load": false, "ls": false, "md5sumFile": false, "mkdir": false, "Mongo": false, "NumberInt": false, "NumberLong": false, "ObjectId": false, "PlanCache": false, "print": false, "printjson": false, "pwd": false, "quit": false, "removeFile": false, "rs": false, "sh": false, "UUID": false, "version": false, "WriteResult": false}, "nashorn": {"__DIR__": false, "__FILE__": false, "__LINE__": false, "com": false, "edu": false, "exit": false, "java": false, "Java": false, "javafx": false, "JavaImporter": false, "javax": false, "JSAdapter": false, "load": false, "loadWithNewGlobal": false, "org": false, "Packages": false, "print": false, "quit": false}, "node": {"__dirname": false, "__filename": false, "AbortController": false, "AbortSignal": false, "atob": false, "Blob": false, "BroadcastChannel": false, "btoa": false, "Buffer": false, "ByteLengthQueuingStrategy": false, "clearImmediate": false, "clearInterval": false, "clearTimeout": false, "CloseEvent": false, "CompressionStream": false, "console": false, "CountQueuingStrategy": false, "crypto": false, "Crypto": false, "CryptoKey": false, "CustomEvent": false, "DecompressionStream": false, "DOMException": false, "Event": false, "EventTarget": false, "exports": true, "fetch": false, "File": false, "FormData": false, "global": false, "Headers": false, "MessageChannel": false, "MessageEvent": false, "MessagePort": false, "module": false, "navigator": false, "Navigator": false, "performance": false, "Performance": false, "PerformanceEntry": false, "PerformanceMark": false, "PerformanceMeasure": false, "PerformanceObserver": false, "PerformanceObserverEntryList": false, "PerformanceResourceTiming": false, "process": false, "queueMicrotask": false, "ReadableByteStreamController": false, "ReadableStream": false, "ReadableStreamBYOBReader": false, "ReadableStreamBYOBRequest": false, "ReadableStreamDefaultController": false, "ReadableStreamDefaultReader": false, "Request": false, "require": false, "Response": false, "setImmediate": false, "setInterval": false, "setTimeout": false, "structuredClone": false, "SubtleCrypto": false, "TextDecoder": false, "TextDecoderStream": false, "TextEncoder": false, "TextEncoderStream": false, "TransformStream": false, "TransformStreamDefaultController": false, "URL": false, "URLSearchParams": false, "WebAssembly": false, "WebSocket": false, "WritableStream": false, "WritableStreamDefaultController": false, "WritableStreamDefaultWriter": false}, "nodeBuiltin": {"AbortController": false, "AbortSignal": false, "atob": false, "Blob": false, "BroadcastChannel": false, "btoa": false, "Buffer": false, "ByteLengthQueuingStrategy": false, "clearImmediate": false, "clearInterval": false, "clearTimeout": false, "CloseEvent": false, "CompressionStream": false, "console": false, "CountQueuingStrategy": false, "crypto": false, "Crypto": false, "CryptoKey": false, "CustomEvent": false, "DecompressionStream": false, "DOMException": false, "Event": false, "EventTarget": false, "fetch": false, "File": false, "FormData": false, "global": false, "Headers": false, "MessageChannel": false, "MessageEvent": false, "MessagePort": false, "navigator": false, "Navigator": false, "performance": false, "Performance": false, "PerformanceEntry": false, "PerformanceMark": false, "PerformanceMeasure": false, "PerformanceObserver": false, "PerformanceObserverEntryList": false, "PerformanceResourceTiming": false, "process": false, "queueMicrotask": false, "ReadableByteStreamController": false, "ReadableStream": false, "ReadableStreamBYOBReader": false, "ReadableStreamBYOBRequest": false, "ReadableStreamDefaultController": false, "ReadableStreamDefaultReader": false, "Request": false, "Response": false, "setImmediate": false, "setInterval": false, "setTimeout": false, "structuredClone": false, "SubtleCrypto": false, "TextDecoder": false, "TextDecoderStream": false, "TextEncoder": false, "TextEncoderStream": false, "TransformStream": false, "TransformStreamDefaultController": false, "URL": false, "URLSearchParams": false, "WebAssembly": false, "WebSocket": false, "WritableStream": false, "WritableStreamDefaultController": false, "WritableStreamDefaultWriter": false}, "phantomjs": {"console": true, "exports": true, "phantom": true, "require": true, "WebPage": true}, "prototypejs": {"$": false, "$$": false, "$A": false, "$break": false, "$continue": false, "$F": false, "$H": false, "$R": false, "$w": false, "Abstract": false, "Ajax": false, "Autocompleter": false, "Builder": false, "Class": false, "Control": false, "Draggable": false, "Draggables": false, "Droppables": false, "Effect": false, "Element": false, "Enumerable": false, "Event": false, "Field": false, "Form": false, "Hash": false, "Insertion": false, "ObjectRange": false, "PeriodicalExecuter": false, "Position": false, "Prototype": false, "Scriptaculous": false, "Selector": false, "Sortable": false, "SortableObserver": false, "Sound": false, "Template": false, "Toggle": false, "Try": false}, "protractor": {"$": false, "$$": false, "browser": false, "by": false, "By": false, "DartObject": false, "element": false, "protractor": false}, "qunit": {"asyncTest": false, "deepEqual": false, "equal": false, "expect": false, "module": false, "notDeepEqual": false, "notEqual": false, "notOk": false, "notPropEqual": false, "notStrictEqual": false, "ok": false, "propEqual": false, "QUnit": false, "raises": false, "start": false, "stop": false, "strictEqual": false, "test": false, "throws": false}, "rhino": {"defineClass": false, "deserialize": false, "gc": false, "help": false, "importClass": false, "importPackage": false, "java": false, "load": false, "loadClass": false, "Packages": false, "print": false, "quit": false, "readFile": false, "readUrl": false, "runCommand": false, "seal": false, "serialize": false, "spawn": false, "sync": false, "toint32": false, "version": false}, "serviceworker": {"addEventListener": false, "applicationCache": false, "atob": false, "Blob": false, "BroadcastChannel": false, "btoa": false, "ByteLengthQueuingStrategy": false, "Cache": false, "caches": false, "CacheStorage": false, "clearInterval": false, "clearTimeout": false, "Client": false, "clients": false, "Clients": false, "close": true, "CompressionStream": false, "console": false, "CountQueuingStrategy": false, "crypto": false, "Crypto": false, "CryptoKey": false, "CustomEvent": false, "DecompressionStream": false, "ErrorEvent": false, "Event": false, "ExtendableEvent": false, "ExtendableMessageEvent": false, "fetch": false, "FetchEvent": false, "File": false, "FileReaderSync": false, "FormData": false, "Headers": false, "IDBCursor": false, "IDBCursorWithValue": false, "IDBDatabase": false, "IDBFactory": false, "IDBIndex": false, "IDBKeyRange": false, "IDBObjectStore": false, "IDBOpenDBRequest": false, "IDBRequest": false, "IDBTransaction": false, "IDBVersionChangeEvent": false, "ImageData": false, "importScripts": false, "indexedDB": false, "location": false, "MessageChannel": false, "MessageEvent": false, "MessagePort": false, "name": false, "navigator": false, "Notification": false, "onclose": true, "onconnect": true, "onerror": true, "onfetch": true, "oninstall": true, "onlanguagechange": true, "onmessage": true, "onmessageerror": true, "onnotificationclick": true, "onnotificationclose": true, "onoffline": true, "ononline": true, "onpush": true, "onpushsubscriptionchange": true, "onrejectionhandled": true, "onsync": true, "onunhandledrejection": true, "performance": false, "Performance": false, "PerformanceEntry": false, "PerformanceMark": false, "PerformanceMeasure": false, "PerformanceNavigation": false, "PerformanceObserver": false, "PerformanceObserverEntryList": false, "PerformanceResourceTiming": false, "PerformanceTiming": false, "postMessage": true, "queueMicrotask": false, "ReadableByteStreamController": false, "ReadableStream": false, "ReadableStreamBYOBReader": false, "ReadableStreamBYOBRequest": false, "ReadableStreamDefaultController": false, "ReadableStreamDefaultReader": false, "registration": false, "removeEventListener": false, "Request": false, "Response": false, "self": false, "ServiceWorker": false, "ServiceWorkerContainer": false, "ServiceWorkerGlobalScope": false, "ServiceWorkerMessageEvent": false, "ServiceWorkerRegistration": false, "setInterval": false, "setTimeout": false, "skipWaiting": false, "SubtleCrypto": false, "TextDecoder": false, "TextDecoderStream": false, "TextEncoder": false, "TextEncoderStream": false, "TransformStream": false, "TransformStreamDefaultController": false, "URL": false, "URLSearchParams": false, "WebAssembly": false, "WebSocket": false, "WindowClient": false, "Worker": false, "WorkerGlobalScope": false, "WritableStream": false, "WritableStreamDefaultController": false, "WritableStreamDefaultWriter": false, "XMLHttpRequest": false}, "shared-node-browser": {"AbortController": false, "AbortSignal": false, "atob": false, "Blob": false, "BroadcastChannel": false, "btoa": false, "ByteLengthQueuingStrategy": false, "clearInterval": false, "clearTimeout": false, "CloseEvent": false, "CompressionStream": false, "console": false, "CountQueuingStrategy": false, "crypto": false, "Crypto": false, "CryptoKey": false, "CustomEvent": false, "DecompressionStream": false, "DOMException": false, "Event": false, "EventTarget": false, "fetch": false, "File": false, "FormData": false, "Headers": false, "MessageChannel": false, "MessageEvent": false, "MessagePort": false, "navigator": false, "Navigator": false, "performance": false, "Performance": false, "PerformanceEntry": false, "PerformanceMark": false, "PerformanceMeasure": false, "PerformanceObserver": false, "PerformanceObserverEntryList": false, "PerformanceResourceTiming": false, "queueMicrotask": false, "ReadableByteStreamController": false, "ReadableStream": false, "ReadableStreamBYOBReader": false, "ReadableStreamBYOBRequest": false, "ReadableStreamDefaultController": false, "ReadableStreamDefaultReader": false, "Request": false, "Response": false, "setInterval": false, "setTimeout": false, "structuredClone": false, "SubtleCrypto": false, "TextDecoder": false, "TextDecoderStream": false, "TextEncoder": false, "TextEncoderStream": false, "TransformStream": false, "TransformStreamDefaultController": false, "URL": false, "URLSearchParams": false, "WebAssembly": false, "WebSocket": false, "WritableStream": false, "WritableStreamDefaultController": false, "WritableStreamDefaultWriter": false}, "shelljs": {"cat": false, "cd": false, "chmod": false, "config": false, "cp": false, "dirs": false, "echo": false, "env": false, "error": false, "exec": false, "exit": false, "find": false, "grep": false, "head": false, "ln": false, "ls": false, "mkdir": false, "mv": false, "popd": false, "pushd": false, "pwd": false, "rm": false, "sed": false, "set": false, "ShellString": false, "sort": false, "tail": false, "tempdir": false, "test": false, "touch": false, "uniq": false, "which": false}, "vitest": {"afterAll": false, "afterEach": false, "assert": false, "assertType": false, "beforeAll": false, "beforeEach": false, "chai": false, "describe": false, "expect": false, "expectTypeOf": false, "it": false, "onTestFailed": false, "onTestFinished": false, "suite": false, "test": false, "vi": false, "vitest": false}, "webextensions": {"browser": false, "chrome": false, "opr": false}, "worker": {"AbortController": false, "AbortSignal": false, "addEventListener": false, "ai": false, "atob": false, "AudioData": false, "AudioDecoder": false, "AudioEncoder": false, "BackgroundFetchManager": false, "BackgroundFetchRecord": false, "BackgroundFetchRegistration": false, "BarcodeDetector": false, "Blob": false, "BroadcastChannel": false, "btoa": false, "ByteLengthQueuingStrategy": false, "Cache": false, "caches": false, "CacheStorage": false, "cancelAnimationFrame": false, "CanvasGradient": false, "CanvasPattern": false, "clearInterval": false, "clearTimeout": false, "close": false, "CloseEvent": false, "CompressionStream": false, "console": false, "CountQueuingStrategy": false, "createImageBitmap": false, "CropTarget": false, "crossOriginIsolated": false, "crypto": false, "Crypto": false, "CryptoKey": false, "CSSSkewX": false, "CSSSkewY": false, "CustomEvent": false, "DecompressionStream": false, "DedicatedWorkerGlobalScope": false, "dispatchEvent": false, "DOMException": false, "DOMMatrix": false, "DOMMatrixReadOnly": false, "DOMPoint": false, "DOMPointReadOnly": false, "DOMQuad": false, "DOMRect": false, "DOMRectReadOnly": false, "DOMStringList": false, "EncodedAudioChunk": false, "EncodedVideoChunk": false, "ErrorEvent": false, "Event": false, "EventSource": false, "EventTarget": false, "fetch": false, "File": false, "FileList": false, "FileReader": false, "FileReaderSync": false, "FileSystemDirectoryHandle": false, "FileSystemFileHandle": false, "FileSystemHandle": false, "FileSystemSyncAccessHandle": false, "FileSystemWritableFileStream": false, "FontFace": false, "fonts": false, "FormData": false, "GPU": false, "GPUAdapter": false, "GPUAdapterInfo": false, "GPUBindGroup": false, "GPUBindGroupLayout": false, "GPUBuffer": false, "GPUBufferUsage": false, "GPUCanvasContext": false, "GPUColorWrite": false, "GPUCommandBuffer": false, "GPUCommandEncoder": false, "GPUCompilationInfo": false, "GPUCompilationMessage": false, "GPUComputePassEncoder": false, "GPUComputePipeline": false, "GPUDevice": false, "GPUDeviceLostInfo": false, "GPUError": false, "GPUExternalTexture": false, "GPUInternalError": false, "GPUMapMode": false, "GPUOutOfMemoryError": false, "GPUPipelineError": false, "GPUPipelineLayout": false, "GPUQuerySet": false, "GPUQueue": false, "GPURenderBundle": false, "GPURenderBundleEncoder": false, "GPURenderPassEncoder": false, "GPURenderPipeline": false, "GPUSampler": false, "GPUShaderModule": false, "GPUShaderStage": false, "GPUSupportedFeatures": false, "GPUSupportedLimits": false, "GPUTexture": false, "GPUTextureUsage": false, "GPUTextureView": false, "GPUUncapturedErrorEvent": false, "GPUValidationError": false, "Headers": false, "HID": false, "HIDConnectionEvent": false, "HIDDevice": false, "HIDInputReportEvent": false, "IDBCursor": false, "IDBCursorWithValue": false, "IDBDatabase": false, "IDBFactory": false, "IDBIndex": false, "IDBKeyRange": false, "IDBObjectStore": false, "IDBOpenDBRequest": false, "IDBRequest": false, "IDBTransaction": false, "IDBVersionChangeEvent": false, "IdleDetector": false, "ImageBitmap": false, "ImageBitmapRenderingContext": false, "ImageData": false, "ImageDecoder": false, "ImageTrack": false, "ImageTrackList": false, "importScripts": false, "indexedDB": false, "isSecureContext": false, "location": false, "Lock": false, "LockManager": false, "MediaCapabilities": false, "MediaSource": false, "MediaSourceHandle": false, "MessageChannel": false, "MessageEvent": false, "MessagePort": false, "name": false, "NavigationPreloadManager": false, "navigator": false, "NavigatorUAData": false, "NetworkInformation": false, "Notification": false, "OffscreenCanvas": false, "OffscreenCanvasRenderingContext2D": false, "onerror": true, "onlanguagechange": true, "onmessage": true, "onmessageerror": true, "onrejectionhandled": true, "onunhandledrejection": true, "origin": false, "Path2D": false, "performance": false, "Performance": false, "PerformanceEntry": false, "PerformanceMark": false, "PerformanceMeasure": false, "PerformanceObserver": false, "PerformanceObserverEntryList": false, "PerformanceResourceTiming": false, "PerformanceServerTiming": false, "PeriodicSyncManager": false, "Permissions": false, "PermissionStatus": false, "PERSISTENT": false, "postMessage": false, "PressureObserver": false, "PressureRecord": false, "ProgressEvent": false, "PromiseRejectionEvent": false, "PushManager": false, "PushSubscription": false, "PushSubscriptionOptions": false, "queueMicrotask": false, "ReadableByteStreamController": false, "ReadableStream": false, "ReadableStreamBYOBReader": false, "ReadableStreamBYOBRequest": false, "ReadableStreamDefaultController": false, "ReadableStreamDefaultReader": false, "removeEventListener": false, "reportError": false, "ReportingObserver": false, "Request": false, "requestAnimationFrame": false, "Response": false, "RTCDataChannel": false, "RTCEncodedAudioFrame": false, "RTCEncodedVideoFrame": false, "scheduler": false, "Scheduler": false, "SecurityPolicyViolationEvent": false, "self": false, "Serial": false, "SerialPort": false, "ServiceWorkerRegistration": false, "setInterval": false, "setTimeout": false, "SourceBuffer": false, "SourceBufferList": false, "StorageBucket": false, "StorageBucketManager": false, "StorageManager": false, "structuredClone": false, "SubtleCrypto": false, "SyncManager": false, "TaskController": false, "TaskPriorityChangeEvent": false, "TaskSignal": false, "TEMPORARY": false, "TextDecoder": false, "TextDecoderStream": false, "TextEncoder": false, "TextEncoderStream": false, "TextMetrics": false, "TransformStream": false, "TransformStreamDefaultController": false, "TrustedHTML": false, "TrustedScript": false, "TrustedScriptURL": false, "TrustedTypePolicy": false, "TrustedTypePolicyFactory": false, "trustedTypes": false, "URL": false, "URLPattern": false, "URLSearchParams": false, "USB": false, "USBAlternateInterface": false, "USBConfiguration": false, "USBConnectionEvent": false, "USBDevice": false, "USBEndpoint": false, "USBInterface": false, "USBInTransferResult": false, "USBIsochronousInTransferPacket": false, "USBIsochronousInTransferResult": false, "USBIsochronousOutTransferPacket": false, "USBIsochronousOutTransferResult": false, "USBOutTransferResult": false, "UserActivation": false, "VideoColorSpace": false, "VideoDecoder": false, "VideoEncoder": false, "VideoFrame": false, "WebAssembly": false, "WebGL2RenderingContext": false, "WebGLActiveInfo": false, "WebGLBuffer": false, "WebGLContextEvent": false, "WebGLFramebuffer": false, "WebGLObject": false, "WebGLProgram": false, "WebGLQuery": false, "WebGLRenderbuffer": false, "WebGLRenderingContext": false, "WebGLSampler": false, "WebGLShader": false, "WebGLShaderPrecisionFormat": false, "WebGLSync": false, "WebGLTexture": false, "WebGLTransformFeedback": false, "WebGLUniformLocation": false, "WebGLVertexArrayObject": false, "webkitRequestFileSystem": false, "webkitRequestFileSystemSync": false, "webkitResolveLocalFileSystemSyncURL": false, "webkitResolveLocalFileSystemURL": false, "WebSocket": false, "WebSocketError": false, "WebSocketStream": false, "WebTransport": false, "WebTransportBidirectionalStream": false, "WebTransportDatagramDuplexStream": false, "WebTransportError": false, "WGSLLanguageFeatures": false, "Worker": false, "WorkerGlobalScope": false, "WorkerLocation": false, "WorkerNavigator": false, "WritableStream": false, "WritableStreamDefaultController": false, "WritableStreamDefaultWriter": false, "XMLHttpRequest": false, "XMLHttpRequestEventTarget": false, "XMLHttpRequestUpload": false}, "wsh": {"ActiveXObject": false, "CollectGarbage": false, "Debug": false, "Enumerator": false, "GetObject": false, "RuntimeObject": false, "ScriptEngine": false, "ScriptEngineBuildVersion": false, "ScriptEngineMajorVersion": false, "ScriptEngineMinorVersion": false, "VBArray": false, "WScript": false, "WSH": false}, "yui": {"YAHOO": false, "YAHOO_config": false, "YUI": false, "YUI_config": false}}