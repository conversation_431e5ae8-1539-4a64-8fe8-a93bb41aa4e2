{"version": 3, "file": "remote-rebuild.js", "sourceRoot": "", "sources": ["../../../src/util/rebuild/remote-rebuild.ts"], "names": [], "mappings": ";;AAAA,+CAA2D;AAE3D,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;IAClB,OAAO,CAAC,KAAK,CAAC,gEAAgE,CAAC,CAAA;IAC/E,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;AACjB,CAAC;AAED,MAAM,OAAO,GAAmB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;AAE3D,MAAM,SAAS,GAAG,IAAA,iBAAO,EAAC,OAAO,CAAC,CAAA;AAElC,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC,UAAkB,EAAE,EAAE,WAAC,OAAA,MAAA,OAAO,CAAC,IAAI,wDAAG,EAAE,GAAG,EAAE,cAAc,EAAE,UAAU,EAAE,CAAC,CAAA,EAAA,CAAC,CAAA;AACnH,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC,UAAkB,EAAE,EAAE,WAAC,OAAA,MAAA,OAAO,CAAC,IAAI,wDAAG,EAAE,GAAG,EAAE,aAAa,EAAE,UAAU,EAAE,CAAC,CAAA,EAAA,CAAC,CAAA;AACjH,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC,UAAkB,EAAE,EAAE,WAAC,OAAA,MAAA,OAAO,CAAC,IAAI,wDAAG,EAAE,GAAG,EAAE,aAAa,EAAE,UAAU,EAAE,CAAC,CAAA,EAAA,CAAC,CAAA;AAEjH,SAAS;KACN,IAAI,CAAC,GAAG,EAAE;;IACT,MAAA,OAAO,CAAC,IAAI,wDAAG,EAAE,GAAG,EAAE,cAAc,EAAE,CAAC,CAAA;IACvC,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;AACxB,CAAC,CAAC;KACD,KAAK,CAAC,GAAG,CAAC,EAAE;;IACX,MAAA,OAAO,CAAC,IAAI,wDAAG;QACb,GAAG,EAAE,eAAe;QACpB,GAAG,EAAE;YACH,OAAO,EAAE,GAAG,CAAC,OAAO;YACpB,KAAK,EAAE,GAAG,CAAC,KAAK;SACjB;KACF,CAAC,CAAA;IACF,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;AACjB,CAAC,CAAC,CAAA", "sourcesContent": ["import { rebuild, RebuildOptions } from \"@electron/rebuild\"\n\nif (!process.send) {\n  console.error(\"The remote rebuilder expects to be spawned with an IPC channel\")\n  process.exit(1)\n}\n\nconst options: RebuildOptions = JSON.parse(process.argv[2])\n\nconst rebuilder = rebuild(options)\n\nrebuilder.lifecycle.on(\"module-found\", (moduleName: string) => process.send?.({ msg: \"module-found\", moduleName }))\nrebuilder.lifecycle.on(\"module-done\", (moduleName: string) => process.send?.({ msg: \"module-done\", moduleName }))\nrebuilder.lifecycle.on(\"module-skip\", (moduleName: string) => process.send?.({ msg: \"module-skip\", moduleName }))\n\nrebuilder\n  .then(() => {\n    process.send?.({ msg: \"rebuild-done\" })\n    return process.exit(0)\n  })\n  .catch(err => {\n    process.send?.({\n      msg: \"rebuild-error\",\n      err: {\n        message: err.message,\n        stack: err.stack,\n      },\n    })\n    process.exit(0)\n  })\n"]}