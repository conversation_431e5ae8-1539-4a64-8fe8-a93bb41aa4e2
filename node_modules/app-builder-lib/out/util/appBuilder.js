"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.executeAppBuilderAsJson = executeAppBuilderAsJson;
exports.executeAppBuilderAndWriteJson = executeAppBuilderAndWriteJson;
exports.objectToArgs = objectToArgs;
const builder_util_1 = require("builder-util");
function executeAppBuilderAsJson(args) {
    return (0, builder_util_1.executeAppBuilder)(args).then(rawResult => {
        if (rawResult === "") {
            return Object.create(null);
        }
        try {
            return JSON.parse(rawResult);
        }
        catch (e) {
            throw new Error(`Cannot parse result: ${e.message}: "${rawResult}"`);
        }
    });
}
function executeAppBuilderAndWriteJson(args, data, extraOptions = {}) {
    return (0, builder_util_1.executeAppBuilder)(args, childProcess => {
        childProcess.stdin.end(JSON.stringify(data));
    }, {
        ...extraOptions,
        stdio: ["pipe", "pipe", process.stdout],
    });
}
function objectToArgs(to, argNameToValue) {
    for (const name of Object.keys(argNameToValue)) {
        const value = argNameToValue[name];
        if (value != null) {
            to.push(`--${name}`, value);
        }
    }
}
//# sourceMappingURL=appBuilder.js.map