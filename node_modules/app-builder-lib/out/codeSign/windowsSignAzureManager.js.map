{"version": 3, "file": "windowsSignAzureManager.js", "sourceRoot": "", "sources": ["../../src/codeSign/windowsSignAzureManager.ts"], "names": [], "mappings": ";;;AAAA,+CAA6D;AAG7D,uDAAgE;AAEhE,MAAa,uBAAuB;IAClC,YAA6B,QAAqB;QAArB,aAAQ,GAAR,QAAQ,CAAa;IAAG,CAAC;IAEtD,KAAK,CAAC,yBAAyB;QAC7B,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAA;QACvC,MAAM,EAAE,GAAG,MAAM,IAAA,0BAAQ,EAAC,EAAE,CAAC,CAAA;QAE7B,kBAAG,CAAC,IAAI,CAAC,IAAI,EAAE,oEAAoE,CAAC,CAAA;QACpF,IAAI,CAAC;YACH,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,YAAY,EAAE,iBAAiB,EAAE,UAAU,EAAE,yFAAyF,CAAC,CAAC,CAAA;QAC7J,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,mEAAmE;YACnE,gKAAgK;YAChK,kBAAG,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,KAAK,EAAE,EAAE,kHAAkH,CAAC,CAAA;QAC1K,CAAC;QACD,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,YAAY,EAAE,iBAAiB,EAAE,UAAU,EAAE,4GAA4G,CAAC,CAAC,CAAA;QAE9K,yDAAyD;QACzD,0HAA0H;QAC1H,kBAAG,CAAC,IAAI,CAAC,IAAI,EAAE,6DAA6D,CAAC,CAAA;QAC7E,IAAI,CAAC,qBAAqB,EAAE,CAAA;QAC5B,IAAI,CAAC,CAAC,IAAI,CAAC,wBAAwB,EAAE,IAAI,IAAI,CAAC,6BAA6B,EAAE,IAAI,IAAI,CAAC,yBAAyB,EAAE,CAAC,EAAE,CAAC;YACnH,MAAM,IAAI,wCAAyB,CACjC,0PAA0P,CAC3P,CAAA;QACH,CAAC;IACH,CAAC;IAED,qBAAqB;QACnB,CAAC;QAAA,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACtD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;gBACxB,MAAM,IAAI,wCAAyB,CACjC,wCAAwC,KAAK,+IAA+I,CAC7L,CAAA;YACH,CAAC;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,wBAAwB;QACtB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,CAAC;YACrC,kBAAG,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,qBAAqB,EAAE,EAAE,0DAA0D,CAAC,CAAA;YACxG,OAAO,KAAK,CAAA;QACd,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,6BAA6B;QAC3B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,CAAC;YAC/C,kBAAG,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,+BAA+B,EAAE,EAAE,gFAAgF,CAAC,CAAA;YACxI,OAAO,KAAK,CAAA;QACd,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,iCAAiC,EAAE,CAAC;YACnD,kBAAG,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,mCAAmC,EAAE,EAAE,iEAAiE,CAAC,CAAA;QAC/H,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,mCAAmC,EAAE,CAAC;YACrD,kBAAG,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,qCAAqC,EAAE,EAAE,wCAAwC,CAAC,CAAA;QACxG,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,yBAAyB;QACvB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,CAAC;YAChC,kBAAG,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,gBAAgB,EAAE,EAAE,4DAA4D,CAAC,CAAA;YACrG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,CAAC;gBAChC,kBAAG,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,gBAAgB,EAAE,EAAE,4DAA4D,CAAC,CAAA;YACvG,CAAC;YACD,OAAO,KAAK,CAAA;QACd,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,mFAAmF;IACnF,KAAK,CAAC,4BAA4B,CAAC,OAA2B;QAC5D,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAA;QACvC,MAAM,EAAE,GAAG,MAAM,IAAA,0BAAQ,EAAC,EAAE,CAAC,CAAA;QAE7B,MAAM,EAAE,QAAQ,EAAE,sBAAsB,EAAE,sBAAsB,EAAE,GAAG,gBAAgB,EAAE,GAAqC,OAAO,CAAC,OAAO,CAAC,gBAAiB,CAAA;QAC7J,MAAM,MAAM,GAAG;YACb,UAAU,EAAE,QAAQ;YACpB,GAAG,gBAAgB,EAAE,qDAAqD;YAC1E,QAAQ,EAAE,QAAQ;YAClB,sBAAsB,EAAE,sBAAsB;YAC9C,sBAAsB,EAAE,sBAAsB;YAC9C,KAAK,EAAE,OAAO,CAAC,IAAI;SACpB,CAAA;QACD,MAAM,YAAY,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;aACxC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,EAAE;YAC9B,OAAO,CAAC,GAAG,GAAG,EAAE,IAAI,KAAK,EAAE,EAAE,KAAK,CAAC,CAAA;QACrC,CAAC,EAAE,EAAc,CAAC;aACjB,IAAI,CAAC,GAAG,CAAC,CAAA;QACZ,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,YAAY,EAAE,iBAAiB,EAAE,UAAU,EAAE,yBAAyB,YAAY,EAAE,CAAC,CAAC,CAAA;QAEzG,OAAO,IAAI,CAAA;IACb,CAAC;CACF;AA9FD,0DA8FC", "sourcesContent": ["import { InvalidConfigurationError, log } from \"builder-util\"\nimport { WindowsAzureSigningConfiguration } from \"../options/winOptions\"\nimport { WinPackager } from \"../winPackager\"\nimport { getPSCmd, WindowsSignOptions } from \"./windowsCodeSign\"\n\nexport class WindowsSignAzureManager {\n  constructor(private readonly packager: WinPackager) {}\n\n  async initializeProviderModules() {\n    const vm = await this.packager.vm.value\n    const ps = await getPSCmd(vm)\n\n    log.info(null, \"installing required module (TrustedSigning) with scope CurrentUser\")\n    try {\n      await vm.exec(ps, [\"-NoProfile\", \"-NonInteractive\", \"-Command\", \"Install-PackageProvider -Name NuGet -MinimumVersion 2.8.5.201 -Force -Scope CurrentUser\"])\n    } catch (error: any) {\n      // Might not be needed, seems GH runners already have NuGet set up.\n      // Logging to debug just in case users run into this. If NuGet isn't present, Install-Module -Name TrustedSigning will fail, so we'll get the logs at that point\n      log.debug({ message: error.message || error.stack }, \"unable to install PackageProvider Nuget. Might be a false alarm though as some systems already have it installed\")\n    }\n    await vm.exec(ps, [\"-NoProfile\", \"-NonInteractive\", \"-Command\", \"Install-Module -Name TrustedSigning -RequiredVersion 0.4.1 -Force -Repository PSGallery -Scope CurrentUser\"])\n\n    // Preemptively check env vars once during initialization\n    // Options: https://learn.microsoft.com/en-us/dotnet/api/azure.identity.environmentcredential?view=azure-dotnet#definition\n    log.info(null, \"verifying env vars for authenticating to Microsoft Entra ID\")\n    this.verifyRequiredEnvVars()\n    if (!(this.verifyPrincipleSecretEnv() || this.verifyPrincipleCertificateEnv() || this.verifyUsernamePasswordEnv())) {\n      throw new InvalidConfigurationError(\n        `Unable to find valid azure env configuration for signing. Missing field(s) can be debugged via \"DEBUG=electron-builder\". Please refer to: https://learn.microsoft.com/en-us/dotnet/api/azure.identity.environmentcredential?view=azure-dotnet#definition`\n      )\n    }\n  }\n\n  verifyRequiredEnvVars() {\n    ;[\"AZURE_TENANT_ID\", \"AZURE_CLIENT_ID\"].forEach(field => {\n      if (!process.env[field]) {\n        throw new InvalidConfigurationError(\n          `Unable to find valid azure env field ${field} for signing. Please refer to: https://learn.microsoft.com/en-us/dotnet/api/azure.identity.environmentcredential?view=azure-dotnet#definition`\n        )\n      }\n    })\n  }\n\n  verifyPrincipleSecretEnv() {\n    if (!process.env.AZURE_CLIENT_SECRET) {\n      log.debug({ envVar: \"AZURE_CLIENT_SECRET\" }, \"no secret found for authenticating to Microsoft Entra ID\")\n      return false\n    }\n    return true\n  }\n\n  verifyPrincipleCertificateEnv() {\n    if (!process.env.AZURE_CLIENT_CERTIFICATE_PATH) {\n      log.debug({ envVar: \"AZURE_CLIENT_CERTIFICATE_PATH\" }, \"no path found for signing certificate for authenticating to Microsoft Entra ID\")\n      return false\n    }\n    if (!process.env.AZURE_CLIENT_CERTIFICATE_PASSWORD) {\n      log.debug({ envVar: \"AZURE_CLIENT_CERTIFICATE_PASSWORD\" }, \"(optional) certificate password not found, assuming no password\")\n    }\n    if (!process.env.AZURE_CLIENT_SEND_CERTIFICATE_CHAIN) {\n      log.debug({ envVar: \"AZURE_CLIENT_SEND_CERTIFICATE_CHAIN\" }, \"(optional) certificate chain not found\")\n    }\n    return true\n  }\n\n  verifyUsernamePasswordEnv() {\n    if (!process.env.AZURE_USERNAME) {\n      log.debug({ envVar: \"AZURE_USERNAME\" }, \"no username found for authenticating to Microsoft Entra ID\")\n      if (!process.env.AZURE_PASSWORD) {\n        log.debug({ envVar: \"AZURE_PASSWORD\" }, \"no password found for authenticating to Microsoft Entra ID\")\n      }\n      return false\n    }\n    return true\n  }\n\n  // prerequisite: requires `initializeProviderModules` to already have been executed\n  async signUsingAzureTrustedSigning(options: WindowsSignOptions): Promise<boolean> {\n    const vm = await this.packager.vm.value\n    const ps = await getPSCmd(vm)\n\n    const { endpoint, certificateProfileName, codeSigningAccountName, ...extraSigningArgs }: WindowsAzureSigningConfiguration = options.options.azureSignOptions!\n    const params = {\n      FileDigest: \"SHA256\",\n      ...extraSigningArgs, // allows overriding FileDigest if provided in config\n      Endpoint: endpoint,\n      CertificateProfileName: certificateProfileName,\n      CodeSigningAccountName: codeSigningAccountName,\n      Files: options.path,\n    }\n    const paramsString = Object.entries(params)\n      .reduce((res, [field, value]) => {\n        return [...res, `-${field}`, value]\n      }, [] as string[])\n      .join(\" \")\n    await vm.exec(ps, [\"-NoProfile\", \"-NonInteractive\", \"-Command\", `Invoke-TrustedSigning ${paramsString}`])\n\n    return true\n  }\n}\n"]}