{"version": 3, "file": "windowsCodeSign.js", "sourceRoot": "", "sources": ["../../src/codeSign/windowsCodeSign.ts"], "names": [], "mappings": ";;AAUA,kCA2BC;AAED,4BAWC;AAlDD,+CAAkC;AAU3B,KAAK,UAAU,WAAW,CAAC,OAA2B,EAAE,QAAqB;IAClF,IAAI,OAAO,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC;QACrC,kBAAG,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,kBAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,2CAA2C,CAAC,CAAA;QAC3F,OAAO,CAAC,MAAM,QAAQ,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,4BAA4B,CAAC,OAAO,CAAC,CAAA;IACtF,CAAC;IAED,kBAAG,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,kBAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,2BAA2B,CAAC,CAAA;IAC3E,MAAM,gBAAgB,GAAG;QACvB,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,IAAI;QAC1B,QAAQ,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ;QAClC,qBAAqB,EAAE,OAAO,CAAC,OAAO,CAAC,qBAAqB;QAC5D,eAAe,EAAE,OAAO,CAAC,OAAO,CAAC,eAAe;QAChD,mBAAmB,EAAE,OAAO,CAAC,OAAO,CAAC,mBAAmB;QACxD,eAAe,EAAE,OAAO,CAAC,OAAO,CAAC,eAAe;QAChD,sBAAsB,EAAE,OAAO,CAAC,OAAO,CAAC,sBAAsB;QAC9D,yBAAyB,EAAE,OAAO,CAAC,OAAO,CAAC,yBAAyB;QACpE,sBAAsB,EAAE,OAAO,CAAC,OAAO,CAAC,sBAAsB;QAC9D,eAAe,EAAE,OAAO,CAAC,OAAO,CAAC,eAAe;QAChD,aAAa,EAAE,OAAO,CAAC,OAAO,CAAC,aAAa;KAC7C,CAAA;IACD,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC;SAC5C,MAAM,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;SAC9B,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,CAAA;IAC1B,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;QAClB,kBAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,iDAAiD,EAAE,EAAE,kBAAkB,CAAC,CAAA;IACrG,CAAC;IACD,OAAO,CAAC,MAAM,QAAQ,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAA;AAC1E,CAAC;AAEM,KAAK,UAAU,QAAQ,CAAC,EAAa;IAC1C,OAAO,MAAM,EAAE;SACZ,IAAI,CAAC,gBAAgB,EAAE,CAAC,YAAY,EAAE,iBAAiB,EAAE,UAAU,EAAE,sBAAsB,CAAC,CAAC;SAC7F,IAAI,CAAC,GAAG,EAAE;QACT,kBAAG,CAAC,KAAK,CAAC,IAAI,EAAE,gDAAgD,CAAC,CAAA;QACjE,OAAO,UAAU,CAAA;IACnB,CAAC,CAAC;SACD,KAAK,CAAC,GAAG,EAAE;QACV,kBAAG,CAAC,KAAK,CAAC,IAAI,EAAE,yDAAyD,CAAC,CAAA;QAC1E,OAAO,gBAAgB,CAAA;IACzB,CAAC,CAAC,CAAA;AACN,CAAC", "sourcesContent": ["import { log } from \"builder-util\"\nimport { WindowsConfiguration } from \"../options/winOptions\"\nimport { VmManager } from \"../vm/vm\"\nimport { WinPackager } from \"../winPackager\"\n\nexport interface WindowsSignOptions {\n  readonly path: string\n  readonly options: WindowsConfiguration\n}\n\nexport async function signWindows(options: WindowsSignOptions, packager: WinPackager): Promise<boolean> {\n  if (options.options.azureSignOptions) {\n    log.info({ path: log.filePath(options.path) }, \"signing with Azure Trusted Signing (beta)\")\n    return (await packager.azureSignManager.value).signUsingAzureTrustedSigning(options)\n  }\n\n  log.info({ path: log.filePath(options.path) }, \"signing with signtool.exe\")\n  const deprecatedFields = {\n    sign: options.options.sign,\n    signDlls: options.options.signDlls,\n    signingHashAlgorithms: options.options.signingHashAlgorithms,\n    certificateFile: options.options.certificateFile,\n    certificatePassword: options.options.certificatePassword,\n    certificateSha1: options.options.certificateSha1,\n    certificateSubjectName: options.options.certificateSubjectName,\n    additionalCertificateFile: options.options.additionalCertificateFile,\n    rfc3161TimeStampServer: options.options.rfc3161TimeStampServer,\n    timeStampServer: options.options.timeStampServer,\n    publisherName: options.options.publisherName,\n  }\n  const fields = Object.entries(deprecatedFields)\n    .filter(([, value]) => !!value)\n    .map(([field]) => field)\n  if (fields.length) {\n    log.warn({ fields, reason: \"please move to win.signtoolOptions.<field_name>\" }, `deprecated field`)\n  }\n  return (await packager.signtoolManager.value).signUsingSigntool(options)\n}\n\nexport async function getPSCmd(vm: VmManager): Promise<string> {\n  return await vm\n    .exec(\"powershell.exe\", [\"-NoProfile\", \"-NonInteractive\", \"-Command\", `Get-Command pwsh.exe`])\n    .then(() => {\n      log.debug(null, \"identified pwsh.exe for executing code signing\")\n      return \"pwsh.exe\"\n    })\n    .catch(() => {\n      log.debug(null, \"unable to find pwsh.exe, falling back to powershell.exe\")\n      return \"powershell.exe\"\n    })\n}\n"]}