{"version": 3, "file": "FileAssociation.js", "sourceRoot": "", "sources": ["../../src/options/FileAssociation.ts"], "names": [], "mappings": "", "sourcesContent": ["/**\n * File associations.\n *\n * macOS (corresponds to [CFBundleDocumentTypes](https://developer.apple.com/library/content/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html#//apple_ref/doc/uid/20001431-101685)), NSIS, and MSI only.\n *\n * On Windows (NSIS) works only if [nsis.perMachine](https://electron.build./configuration.md#NsisOptions-perMachine) is set to `true`.\n */\nexport interface FileAssociation {\n  /**\n   * The extension (minus the leading period). e.g. `png`.\n   */\n  readonly ext: string | Array<string>\n\n  /**\n   * The name. e.g. `PNG`. Defaults to `ext`.\n   */\n  readonly name?: string | null\n\n  /**\n   * *windows-only.* The description.\n   */\n  readonly description?: string | null\n\n  /**\n   * *linux-only.* The mime-type.\n   */\n  readonly mimeType?: string | null\n\n  /**\n   * The path to icon (`.icns` for MacOS and `.ico` for Windows), relative to `build` (build resources directory). Defaults to `${firstExt}.icns`/`${firstExt}.ico` (if several extensions specified, first is used) or to application icon.\n   *\n   * Not supported on Linux, file issue if need (default icon will be `x-office-document`). Not supported on MSI.\n   */\n  readonly icon?: string | null\n\n  /**\n   * *macOS-only* The app’s role with respect to the type. The value can be `Editor`, `Viewer`, `Shell`, or `None`. Corresponds to `CFBundleTypeRole`.\n   * @default Editor\n   */\n  readonly role?: string\n\n  /**\n   * *macOS-only* Whether the document is distributed as a bundle. If set to true, the bundle directory is treated as a file. Corresponds to `LSTypeIsPackage`.\n   */\n  readonly isPackage?: boolean\n\n  /**\n   * *macOS-only* The app’s rank with respect to the type. The value can be `Owner`, `Default`, `Alternate`, or `None`. Corresponds to `LSHandlerRank`.\n   * @default Default\n   */\n  readonly rank?: string\n}\n"]}