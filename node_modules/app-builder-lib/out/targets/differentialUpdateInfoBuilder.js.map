{"version": 3, "file": "differentialUpdateInfoBuilder.js", "sourceRoot": "", "sources": ["../../src/targets/differentialUpdateInfoBuilder.ts"], "names": [], "mappings": ";;;AAUA,kFAsBC;AAED,4FA4BC;AAED,wCAGC;AAED,wCAaC;AAlFD,+CAAkC;AAElC,6BAA4B;AAG5B,mDAA4D;AAG/C,QAAA,qBAAqB,GAAG,WAAW,CAAA;AAEhD,SAAgB,mCAAmC,CAAC,YAAoB,EAAE,YAAiD;IACzH,IAAI,YAAY,IAAI,IAAI,EAAE,CAAC;QACzB,OAAO,IAAI,CAAA;IACb,CAAC;IAED,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;IACtC,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;QACrB,OAAO,IAAI,CAAA;IACb,CAAC;IAED,MAAM,QAAQ,GAAwC,EAAE,CAAA;IACxD,KAAK,MAAM,IAAI,IAAI,IAAI,EAAE,CAAC;QACxB,MAAM,eAAe,GAAG,YAAY,CAAC,IAAI,CAAC,CAAA;QAC1C,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;QAChD,QAAQ,CAAC,IAAI,CAAC,GAAG;YACf,GAAG,eAAe;YAClB,IAAI,EAAE,IAAI;YACV,oEAAoE;YACpE,IAAI;SACE,CAAA;IACV,CAAC;IACD,OAAO,EAAE,QAAQ,EAAE,CAAA;AACrB,CAAC;AAED,SAAgB,wCAAwC,CAAC,cAA8B;IACrF;;;;;;;;;;;;;;;;;;;;OAoBG;IACH,cAAc,CAAC,QAAQ,GAAG,CAAC,CAAA;IAC3B,qDAAqD;IACrD,cAAc,CAAC,KAAK,GAAG,KAAK,CAAA;IAC5B,uEAAuE;IACvE,cAAc,CAAC,WAAW,GAAG,QAAQ,CAAA;IACrC,OAAO,cAAc,CAAA;AACvB,CAAC;AAEM,KAAK,UAAU,cAAc,CAAC,IAAY;IAC/C,kBAAG,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,kBAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,6BAA6B,CAAC,CAAA;IACrE,OAAO,MAAM,IAAA,oCAAuB,EAAqB,CAAC,UAAU,EAAE,SAAS,EAAE,IAAI,EAAE,eAAe,EAAE,SAAS,CAAC,CAAC,CAAA;AACrH,CAAC;AAEM,KAAK,UAAU,cAAc,CAAC,IAAY,EAAE,MAAc,EAAE,QAA+B,EAAE,gBAA+B;IACjI,MAAM,YAAY,GAAG,GAAG,IAAI,GAAG,6BAAqB,EAAE,CAAA;IACtD,kBAAG,CAAC,IAAI,CAAC,EAAE,YAAY,EAAE,kBAAG,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,EAAE,oBAAoB,CAAC,CAAA;IAC5E,MAAM,UAAU,GAAG,MAAM,IAAA,oCAAuB,EAAqB,CAAC,UAAU,EAAE,SAAS,EAAE,IAAI,EAAE,UAAU,EAAE,YAAY,CAAC,CAAC,CAAA;IAC7H,MAAM,QAAQ,CAAC,IAAI,CAAC,0BAA0B,CAAC;QAC7C,IAAI,EAAE,YAAY;QAClB,gBAAgB,EAAE,gBAAgB,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,gBAAgB,GAAG,6BAAqB,EAAE;QACjG,MAAM;QACN,IAAI,EAAE,IAAI;QACV,QAAQ;QACR,UAAU;KACX,CAAC,CAAA;IACF,OAAO,UAAU,CAAA;AACnB,CAAC", "sourcesContent": ["import { log } from \"builder-util\"\nimport { BlockMapDataHolder, PackageFileInfo } from \"builder-util-runtime\"\nimport * as path from \"path\"\nimport { Target } from \"../core\"\nimport { PlatformPackager } from \"../platformPackager\"\nimport { executeAppBuilderAsJson } from \"../util/appBuilder\"\nimport { ArchiveOptions } from \"./archive\"\n\nexport const BLOCK_MAP_FILE_SUFFIX = \".blockmap\"\n\nexport function createNsisWebDifferentialUpdateInfo(artifactPath: string, packageFiles: { [arch: string]: PackageFileInfo }) {\n  if (packageFiles == null) {\n    return null\n  }\n\n  const keys = Object.keys(packageFiles)\n  if (keys.length <= 0) {\n    return null\n  }\n\n  const packages: { [arch: string]: PackageFileInfo } = {}\n  for (const arch of keys) {\n    const packageFileInfo = packageFiles[arch]\n    const file = path.basename(packageFileInfo.path)\n    packages[arch] = {\n      ...packageFileInfo,\n      path: file,\n      // https://github.com/electron-userland/electron-builder/issues/2583\n      file,\n    } as any\n  }\n  return { packages }\n}\n\nexport function configureDifferentialAwareArchiveOptions(archiveOptions: ArchiveOptions): ArchiveOptions {\n  /*\n   * dict size 64 MB: Full: 33,744.88 KB, To download: 17,630.3 KB (52%)\n   * dict size 16 MB: Full: 33,936.84 KB, To download: 16,175.9 KB (48%)\n   * dict size  8 MB: Full: 34,187.59 KB, To download:  8,229.9 KB (24%)\n   * dict size  4 MB: Full: 34,628.73 KB, To download: 3,782.97 KB (11%)\n\n   as we can see, if file changed in one place, all block is invalidated (and update size approximately equals to dict size)\n\n   1 MB is used:\n\n   1MB:\n\n   2018/01/11 11:54:41:0045 File has 59 changed blocks\n   2018/01/11 11:54:41:0050 Full: 71,588.59 KB, To download: 1,243.39 KB (2%)\n\n   4MB:\n\n   2018/01/11 11:31:43:0440 Full: 70,303.55 KB, To download: 4,843.27 KB (7%)\n   2018/01/11 11:31:43:0435 File has 234 changed blocks\n\n   */\n  archiveOptions.dictSize = 1\n  // solid compression leads to a lot of changed blocks\n  archiveOptions.solid = false\n  // do not allow to change compression level to avoid different packages\n  archiveOptions.compression = \"normal\"\n  return archiveOptions\n}\n\nexport async function appendBlockmap(file: string): Promise<BlockMapDataHolder> {\n  log.info({ file: log.filePath(file) }, \"building embedded block map\")\n  return await executeAppBuilderAsJson<BlockMapDataHolder>([\"blockmap\", \"--input\", file, \"--compression\", \"deflate\"])\n}\n\nexport async function createBlockmap(file: string, target: Target, packager: PlatformPackager<any>, safeArtifactName: string | null): Promise<BlockMapDataHolder> {\n  const blockMapFile = `${file}${BLOCK_MAP_FILE_SUFFIX}`\n  log.info({ blockMapFile: log.filePath(blockMapFile) }, \"building block map\")\n  const updateInfo = await executeAppBuilderAsJson<BlockMapDataHolder>([\"blockmap\", \"--input\", file, \"--output\", blockMapFile])\n  await packager.info.callArtifactBuildCompleted({\n    file: blockMapFile,\n    safeArtifactName: safeArtifactName == null ? null : `${safeArtifactName}${BLOCK_MAP_FILE_SUFFIX}`,\n    target,\n    arch: null,\n    packager,\n    updateInfo,\n  })\n  return updateInfo\n}\n"]}