{"version": 3, "file": "Defines.js", "sourceRoot": "", "sources": ["../../../src/targets/nsis/Defines.ts"], "names": [], "mappings": "", "sourcesContent": ["import { PortableOptions } from \"./nsisOptions\"\nimport { PathLike } from \"fs\"\n/**\n * Parameters declared as environment variables in NSIS scripts.\n * The documentation vaguely explains \"All other electron-builder specific flags (e.g. ONE_CLICK) are still defined.\"\n * Parameters with null values in TypeScript can be treated as Boolean values using \"!Ifdef\" in NSIS Script.\n */\nexport type Defines = {\n  APP_ID: string\n  APP_GUID: unknown\n  UNINSTALL_APP_KEY: unknown\n  PRODUCT_NAME: string\n  PRODUCT_FILENAME: string\n  APP_FILENAME: string\n  APP_DESCRIPTION: string\n  VERSION: string\n\n  PROJECT_DIR: string\n  BUILD_RESOURCES_DIR: string\n\n  APP_PACKAGE_NAME: string\n\n  ENABLE_LOGGING_ELECTRON_BUILDER?: null\n  UNINSTALL_REGISTRY_KEY_2?: string\n\n  MUI_ICON?: unknown\n  MUI_UNICON?: unknown\n\n  APP_DIR_64?: string\n  APP_DIR_ARM64?: string\n  APP_DIR_32?: string\n\n  APP_BUILD_DIR?: string\n\n  APP_64?: string\n  APP_ARM64?: string\n  APP_32?: string\n\n  APP_64_NAME?: string\n  APP_ARM64_NAME?: string\n  APP_32_NAME?: string\n\n  APP_64_HASH?: string\n  APP_ARM64_HASH?: string\n  APP_32_HASH?: string\n\n  APP_64_UNPACKED_SIZE?: string\n  APP_ARM64_UNPACKED_SIZE?: string\n  APP_32_UNPACKED_SIZE?: string\n\n  REQUEST_EXECUTION_LEVEL?: PortableOptions[\"requestExecutionLevel\"]\n\n  UNPACK_DIR_NAME?: string | false\n\n  SPLASH_IMAGE?: unknown\n\n  ESTIMATED_SIZE?: number\n\n  COMPRESS?: \"auto\"\n\n  BUILD_UNINSTALLER?: null\n  UNINSTALLER_OUT_FILE?: PathLike\n\n  ONE_CLICK?: null\n  RUN_AFTER_FINISH?: null\n  HEADER_ICO?: string\n  HIDE_RUN_AFTER_FINISH?: null\n\n  MUI_HEADERIMAGE?: null\n  MUI_HEADERIMAGE_RIGHT?: null\n  MUI_HEADERIMAGE_BITMAP?: string\n\n  MUI_WELCOMEFINISHPAGE_BITMAP?: string\n  MUI_UNWELCOMEFINISHPAGE_BITMAP?: string\n\n  MULTIUSER_INSTALLMODE_ALLOW_ELEVATION?: null\n\n  INSTALL_MODE_PER_ALL_USERS?: null\n  INSTALL_MODE_PER_ALL_USERS_DEFAULT?: null\n  INSTALL_MODE_PER_ALL_USERS_REQUIRED?: null\n\n  allowToChangeInstallationDirectory?: null\n\n  removeDefaultUninstallWelcomePage?: null\n\n  MENU_FILENAME?: string\n\n  SHORTCUT_NAME?: string\n\n  DELETE_APP_DATA_ON_UNINSTALL?: null\n\n  UNINSTALLER_ICON?: string\n  UNINSTALL_DISPLAY_NAME?: string\n\n  RECREATE_DESKTOP_SHORTCUT?: null\n\n  DO_NOT_CREATE_DESKTOP_SHORTCUT?: null\n\n  DO_NOT_CREATE_START_MENU_SHORTCUT?: null\n\n  DISPLAY_LANG_SELECTOR?: null\n\n  COMPANY_NAME?: string\n\n  APP_PRODUCT_FILENAME?: string\n\n  APP_PACKAGE_STORE_FILE?: string\n\n  APP_INSTALLER_STORE_FILE?: string\n\n  ZIP_COMPRESSION?: null\n\n  COMPRESSION_METHOD?: \"zip\" | \"7z\"\n}\n"]}