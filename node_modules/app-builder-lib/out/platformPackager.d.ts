import { Arch, AsyncTaskManager, DebugLogger } from "builder-util";
import { FileTransformer } from "builder-util";
import { AppInfo } from "./appInfo";
import { GetFileMatchersOptions } from "./fileMatcher";
import { AfterPackContext, CompressionLevel, Configuration, ElectronPlatformName, FileAssociation, Packager, PackagerOptions, Platform, PlatformSpecificBuildOptions, Target, TargetSpecificOptions } from "./index";
export declare abstract class PlatformPackager<DC extends PlatformSpecificBuildOptions> {
    readonly info: Packager;
    readonly platform: Platform;
    get packagerOptions(): PackagerOptions;
    get buildResourcesDir(): string;
    get projectDir(): string;
    get config(): Configuration;
    readonly platformSpecificBuildOptions: DC;
    get resourceList(): Promise<Array<string>>;
    private readonly _resourceList;
    readonly appInfo: AppInfo;
    protected constructor(info: Packager, platform: Platform);
    get compression(): CompressionLevel;
    get debugLogger(): DebugLogger;
    abstract get defaultTarget(): Array<string>;
    protected prepareAppInfo(appInfo: AppInfo): AppInfo;
    private static normalizePlatformSpecificBuildOptions;
    abstract createTargets(targets: Array<string>, mapper: (name: string, factory: (outDir: string) => Target) => void): void;
    getCscPassword(): string;
    getCscLink(extraEnvName?: string | null): string | null | undefined;
    doGetCscPassword(): string | null | undefined;
    protected computeAppOutDir(outDir: string, arch: Arch): string;
    dispatchArtifactCreated(file: string, target: Target | null, arch: Arch | null, safeArtifactName?: string | null): Promise<void>;
    pack(outDir: string, arch: Arch, targets: Array<Target>, taskManager: AsyncTaskManager): Promise<any>;
    protected packageInDistributableFormat(appOutDir: string, arch: Arch, targets: Array<Target>, taskManager: AsyncTaskManager): void;
    private static buildAsyncTargets;
    private getExtraFileMatchers;
    createGetFileMatchersOptions(outDir: string, arch: Arch, customBuildOptions: PlatformSpecificBuildOptions): GetFileMatchersOptions;
    protected doPack(outDir: string, appOutDir: string, platformName: ElectronPlatformName, arch: Arch, platformSpecificBuildOptions: DC, targets: Array<Target>, sign?: boolean, disableAsarIntegrity?: boolean): Promise<void>;
    protected doSignAfterPack(outDir: string, appOutDir: string, platformName: ElectronPlatformName, arch: Arch, platformSpecificBuildOptions: DC, targets: Array<Target>): Promise<void>;
    protected createTransformerForExtraFiles(packContext: AfterPackContext): FileTransformer | null;
    private copyAppFiles;
    protected signApp(packContext: AfterPackContext, isAsar: boolean): Promise<boolean>;
    getIconPath(): Promise<string | null>;
    private computeAsarOptions;
    getElectronSrcDir(dist: string): string;
    getElectronDestinationDir(appOutDir: string): string;
    getResourcesDir(appOutDir: string): string;
    getMacOsResourcesDir(appOutDir: string): string;
    private checkFileInPackage;
    private sanityCheckPackage;
    computeSafeArtifactName(suggestedName: string | null, ext: string, arch?: Arch | null, skipDefaultArch?: boolean, defaultArch?: string, safePattern?: string): string | null;
    expandArtifactNamePattern(targetSpecificOptions: TargetSpecificOptions | null | undefined, ext: string, arch?: Arch | null, defaultPattern?: string, skipDefaultArch?: boolean, defaultArch?: string): string;
    artifactPatternConfig(targetSpecificOptions: TargetSpecificOptions | null | undefined, defaultPattern: string | undefined): {
        isUserForced: boolean;
        pattern: string;
    };
    expandArtifactBeautyNamePattern(targetSpecificOptions: TargetSpecificOptions | null | undefined, ext: string, arch?: Arch | null): string;
    private computeArtifactName;
    expandMacro(pattern: string, arch?: string | null, extra?: any, isProductNameSanitized?: boolean): string;
    generateName2(ext: string | null, classifier: string | null | undefined, deployment: boolean): string;
    getTempFile(suffix: string): Promise<string>;
    get fileAssociations(): Array<FileAssociation>;
    getResource(custom: string | null | undefined, ...names: Array<string>): Promise<string | null>;
    get forceCodeSigning(): boolean;
    protected getOrConvertIcon(format: IconFormat): Promise<string | null>;
    getDefaultFrameworkIcon(): string | null;
    resolveIcon(sources: Array<string>, fallbackSources: Array<string>, outputFormat: IconFormat): Promise<Array<IconInfo>>;
}
export interface IconInfo {
    file: string;
    size: number;
}
export type IconFormat = "icns" | "ico" | "set";
export declare function isSafeGithubName(name: string): boolean;
export declare function computeSafeArtifactNameIfNeeded(suggestedName: string | null, safeNameProducer: () => string): string | null;
export declare function normalizeExt(ext: string): string;
export declare function chooseNotNull<T>(v1: T | null | undefined, v2: T | null | undefined): T | null | undefined;
