# decode-named-character-reference

[![Build Status][build-badge]][build]
[![Coverage Status][coverage-badge]][coverage]
[![Downloads][downloads-badge]][downloads]
[![Size][size-badge]][size]

Decode named character references.

## Contents

* [What is this?](#what-is-this)
* [When should I use this?](#when-should-i-use-this)
* [Install](#install)
* [Use](#use)
* [API](#api)
  * [`decodeNamedCharacterReference(value)`](#decodenamedcharacterreferencevalue)
* [Types](#types)
* [Compatibility](#compatibility)
* [Security](#security)
* [Related](#related)
* [Contribute](#contribute)
* [License](#license)

## What is this?

A workaround for webpack.

## When should I use this?

Never use this.
Use [`parse-entities`][parse-entities].
It uses this.

## Install

This package is [ESM only][esm].
In Node.js (version 14.14+, 16.0+), install with [npm][]:

```sh
npm install decode-named-character-reference
```

In Deno with [`esm.sh`][esmsh]:

```js
import {decodeNamedCharacterReference} from 'https://esm.sh/decode-named-character-reference@1'
```

In browsers with [`esm.sh`][esmsh]:

```html
<script type="module">
  import {decodeNamedCharacterReference} from 'https://esm.sh/decode-named-character-reference@1?bundle'
</script>
```

## Use

```js
import {decodeNamedCharacterReference} from 'decode-named-character-reference'

decodeNamedCharacterReference('amp') //=> '&'
```

## API

This package exports the identifier `decodeNamedCharacterReference`.
There is no default export.

### `decodeNamedCharacterReference(value)`

Again, use [`parse-entities`][parse-entities].

## Types

This package is fully typed with [TypeScript][].
It exports no additional types.

## Compatibility

This package is at least compatible with all maintained versions of Node.js.
As of now, that is Node.js 14.14+ and 16.0+.
It also works in Deno and modern browsers.

## Security

This package is safe.

## Related

* [`parse-entities`][parse-entities]
  — parse (decode) HTML character references

## Contribute

Yes please!
See [How to Contribute to Open Source][contribute].

## License

[MIT][license] © [Titus Wormer][author]

<!-- Definitions -->

[author]: https://wooorm.com

[build]: https://github.com/wooorm/decode-named-character-reference/actions

[build-badge]: https://github.com/wooorm/decode-named-character-reference/workflows/main/badge.svg

[contribute]: https://opensource.guide/how-to-contribute/

[coverage]: https://codecov.io/github/wooorm/decode-named-character-reference

[coverage-badge]: https://img.shields.io/codecov/c/github/wooorm/decode-named-character-reference.svg

[downloads]: https://www.npmjs.com/package/decode-named-character-reference

[downloads-badge]: https://img.shields.io/npm/dm/decode-named-character-reference.svg

[esm]: https://gist.github.com/sindresorhus/a39789f98801d908bbc7ff3ecc99d99c

[esmsh]: https://esm.sh

[license]: license

[npm]: https://docs.npmjs.com/cli/install

[parse-entities]: https://github.com/wooorm/parse-entities

[size]: https://bundlephobia.com/result?p=decode-named-character-reference

[size-badge]: https://img.shields.io/bundlephobia/minzip/decode-named-character-reference.svg

[typescript]: https://www.typescriptlang.org
