import { type TransferListItem } from 'node:worker_threads';
import type { AnyFn, GlobalShim, Syncify, ValueOf } from './types.js';
export * from './types.js';
export declare const TsRunner: {
    readonly Node: "node";
    readonly Bun: "bun";
    readonly TsNode: "ts-node";
    readonly EsbuildRegister: "esbuild-register";
    readonly EsbuildRunner: "esbuild-runner";
    readonly SWC: "swc";
    readonly TSX: "tsx";
};
export type TsRunner = ValueOf<typeof TsRunner>;
export declare const MTS_SUPPORTED_NODE_VERSION = "16";
export declare const LOADER_SUPPORTED_NODE_VERSION = "20";
export declare const STRIP_TYPES_NODE_VERSION = "22.6";
export declare const TRANSFORM_TYPES_NODE_VERSION = "22.7";
export declare const FEATURE_TYPESCRIPT_NODE_VERSION = "22.10";
export declare const DEFAULT_TYPES_NODE_VERSION = "23.6";
export declare const STRIP_TYPES_FLAG = "--experimental-strip-types";
export declare const TRANSFORM_TYPES_FLAG = "--experimental-transform-types";
export declare const NO_STRIP_TYPES_FLAG = "--no-experimental-strip-types";
export declare const compareVersion: (version1: string, version2: string) => 1 | -1 | 0;
export declare const NODE_VERSION: string;
export declare const DEFAULT_TIMEOUT: number | undefined;
export declare const DEFAULT_EXEC_ARGV: string[];
export declare const DEFAULT_TS_RUNNER: TsRunner | undefined;
export declare const DEFAULT_GLOBAL_SHIMS: boolean;
export declare const DEFAULT_GLOBAL_SHIMS_PRESET: GlobalShim[];
export interface SynckitOptions {
    execArgv?: string[];
    globalShims?: GlobalShim[] | boolean;
    timeout?: number;
    transferList?: TransferListItem[];
    tsRunner?: TsRunner;
}
export declare function extractProperties<T extends object>(object: T): T;
export declare function extractProperties<T>(object?: T): T | undefined;
export declare function createSyncFn<T extends AnyFn>(workerPath: URL | string, timeoutOrOptions?: SynckitOptions | number): Syncify<T>;
export declare const isFile: (path: string) => boolean;
export declare const encodeImportModule: (moduleNameOrGlobalShim: GlobalShim | string, type?: "import" | "require") => string;
export declare const generateGlobals: (workerPath: string, globalShims: GlobalShim[], type?: "import" | "require") => string;
export declare function runAsWorker<T extends AnyFn<Promise<R> | R>, R = ReturnType<T>>(fn: T): void;
