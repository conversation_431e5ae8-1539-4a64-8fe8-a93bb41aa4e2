{"version": 3, "file": "defaultFileCompare.js", "sourceRoot": "", "sources": ["../../../../src/FileCompareHandler/default/defaultFileCompare.ts"], "names": [], "mappings": ";;;;;;AAAA,4CAAmB;AACnB,8EAA0E;AAC1E,4DAAoE;AAGpE,4DAAwD;AACxD,0DAAsD;AAEtD,MAAM,2BAA2B,GAAG,CAAC,CAAA;AACrC,MAAM,QAAQ,GAAG,OAAO,CAAA;AACxB,MAAM,OAAO,GAAG,IAAI,yCAAmB,CAAC,2BAA2B,GAAG,CAAC,CAAC,CAAA;AACxE,MAAM,eAAe,GAAG,IAAI,uBAAU,CAAC,QAAQ,EAAE,2BAA2B,CAAC,CAAA,CAAE,+HAA+H;AAC9M,MAAM,cAAc,GAAG,IAAI,uBAAU,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAA;AAErC,QAAA,kBAAkB,GAAuB;IAClD,WAAW,EAAE,YAAY;CAC5B,CAAA;AAED;;GAEG;AACH,SAAS,WAAW,CAAC,KAAa,EAAE,KAAe,EAAE,KAAa,EAAE,KAAe,EAAE,OAAgB;IACjG,IAAI,GAAuB,CAAA;IAC3B,IAAI,GAAuB,CAAA;IAC3B,IAAI,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,EAAE;QAC3B,OAAO,KAAK,CAAA;KACf;IACD,MAAM,UAAU,GAAG,cAAc,CAAC,eAAe,EAAE,CAAA;IACnD,IAAI;QACA,GAAG,GAAG,YAAE,CAAC,QAAQ,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;QAC7B,GAAG,GAAG,YAAE,CAAC,QAAQ,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;QAC7B,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAA;QAC5B,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAA;QAC5B,SAAU;YACN,MAAM,KAAK,GAAG,YAAE,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;YACvD,MAAM,KAAK,GAAG,YAAE,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;YACvD,IAAI,KAAK,KAAK,KAAK,EAAE;gBACjB,OAAO,KAAK,CAAA;aACf;iBAAM,IAAI,KAAK,KAAK,CAAC,EAAE;gBACpB,sBAAsB;gBACtB,OAAO,IAAI,CAAA;aACd;iBAAM,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,EAAE;gBAC3C,OAAO,KAAK,CAAA;aACf;SACJ;KACJ;YAAS;QACN,uBAAU,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;QACnC,cAAc,CAAC,WAAW,CAAC,UAAU,CAAC,CAAA;KACzC;AACL,CAAC;AAED;;GAEG;AACH,SAAS,YAAY,CAAC,KAAa,EAAE,KAAe,EAAE,KAAa,EAAE,KAAe,EAAE,OAAgB;IAClG,IAAI,GAAuB,CAAA;IAC3B,IAAI,GAAuB,CAAA;IAC3B,IAAI,UAAkC,CAAA;IACtC,IAAI,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,EAAE;QAC3B,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;KAChC;IACD,IAAI,KAAK,CAAC,IAAI,GAAG,QAAQ,IAAI,CAAC,OAAO,CAAC,wBAAwB,EAAE;QAC5D,OAAO,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,CAAA;KAC3E;IACD,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,OAAO,CAAC,WAAW,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC;SACjF,IAAI,CAAC,GAAG,CAAC,EAAE;QACR,UAAU,GAAG,eAAe,CAAC,eAAe,EAAE,CAAA;QAC9C,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAA;QACZ,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAA;QACZ,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAA;QAC5B,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAA;QAC5B,MAAM,oBAAoB,GAAG,GAAG,EAAE;YAC9B,OAAO,OAAO,CAAC,GAAG,CAAC;gBACf,qBAAS,CAAC,IAAI,CAAC,GAAa,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC;gBACtD,qBAAS,CAAC,IAAI,CAAC,GAAa,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC;aACzD,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,EAAE;gBACpB,MAAM,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,CAAA;gBAC5B,MAAM,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,CAAA;gBAC5B,IAAI,KAAK,KAAK,KAAK,EAAE;oBACjB,OAAO,KAAK,CAAA;iBACf;qBAAM,IAAI,KAAK,KAAK,CAAC,EAAE;oBACpB,sBAAsB;oBACtB,OAAO,IAAI,CAAA;iBACd;qBAAM,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,EAAE;oBAC3C,OAAO,KAAK,CAAA;iBACf;qBAAM;oBACH,OAAO,oBAAoB,EAAE,CAAA;iBAChC;YACL,CAAC,CAAC,CAAA;QACN,CAAC,CAAA;QACD,OAAO,oBAAoB,EAAE,CAAA;IACjC,CAAC,CAAC;SACD,IAAI;IACD,0CAA0C;IAC1C,GAAG,CAAC,EAAE,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAC1D,GAAG,CAAC,EAAE,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,GAAG,CAAA,CAAC,CAAC,CAAC,CACvE,CAAA;AACT,CAAC;AAED,SAAS,cAAc,CAAC,IAAY,EAAE,IAAY,EAAE,WAAmB;IACnE,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAA;AACxE,CAAC;AAED,SAAS,aAAa,CAAC,GAAY,EAAE,GAAY,EAAE,UAAuB;IACtE,IAAI,UAAU,EAAE;QACZ,eAAe,CAAC,WAAW,CAAC,UAAU,CAAC,CAAA;KAC1C;IACD,OAAO,uBAAU,CAAC,eAAe,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,CAAA;AACxD,CAAC"}