{"name": "replicate", "version": "1.0.1", "description": "JavaScript client for Replicate", "repository": "github:replicate/replicate-javascript", "homepage": "https://github.com/replicate/replicate-javascript#readme", "bugs": "https://github.com/replicate/replicate-javascript/issues", "license": "Apache-2.0", "main": "index.js", "type": "commonjs", "types": "index.d.ts", "files": ["CONTRIBUTING.md", "LICENSE", "README.md", "index.d.ts", "index.js", "lib/**/*.js", "vendor/**/*", "package.json"], "engines": {"node": ">=18.0.0", "npm": ">=7.19.0", "git": ">=2.11.0", "yarn": ">=1.7.0"}, "scripts": {"check": "tsc", "format": "biome format . --write", "lint-biome": "biome lint .", "lint-publint": "publint", "lint": "npm run lint-biome && npm run lint-publint", "test": "jest"}, "optionalDependencies": {"readable-stream": ">=4.0.0"}, "devDependencies": {"@biomejs/biome": "^1.4.1", "@types/jest": "^29.5.3", "@typescript-eslint/eslint-plugin": "^5.56.0", "cross-fetch": "^3.1.5", "jest": "^29.7.0", "nock": "^14.0.0-beta.6", "publint": "^0.2.7", "ts-jest": "^29.1.0", "typescript": "^5.0.2"}}