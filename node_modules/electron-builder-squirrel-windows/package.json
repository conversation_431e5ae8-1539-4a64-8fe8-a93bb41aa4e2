{"name": "electron-builder-squirrel-windows", "version": "25.1.8", "main": "out/SquirrelWindowsTarget.js", "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/electron-userland/electron-builder.git", "directory": "packages/electron-builder-squirrel-windows"}, "bugs": "https://github.com/electron-userland/electron-builder/issues", "homepage": "https://github.com/electron-userland/electron-builder", "files": ["out"], "dependencies": {"archiver": "^5.3.1", "fs-extra": "^10.1.0", "app-builder-lib": "25.1.8", "builder-util": "25.1.7"}, "devDependencies": {"@types/archiver": "5.3.1", "@types/fs-extra": "9.0.13"}, "types": "./out/SquirrelWindowsTarget.d.ts"}