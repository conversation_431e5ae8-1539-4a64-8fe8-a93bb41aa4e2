{"version": 3, "file": "squirrelPack.js", "sourceRoot": "", "sources": ["../src/squirrelPack.ts"], "names": [], "mappings": ";;;AASA,wCAQC;AAjBD,+CAAyG;AACzG,iEAA2E;AAC3E,mDAAgG;AAEhG,uCAAqE;AACrE,6BAA4B;AAC5B,qCAAoC;AACpC,kCAAiC;AAEjC,SAAgB,cAAc,CAAC,OAAe;IAC5C,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;IAChC,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;IACjC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACrB,OAAO,CAAC,WAAW,EAAE,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IACpE,CAAC;SAAM,CAAC;QACN,OAAO,WAAY,CAAA;IACrB,CAAC;AACH,CAAC;AAED,SAAS,YAAY,CAAC,eAAuB,EAAE,OAAwB;IACrE,kBAAG,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAA;IACnD,MAAM,IAAI,GAAG,IAAA,mCAAW,EAAC,CAAC,IAAI,EAAE,OAAO,CAAC,cAAe,EAAE,IAAI,EAAE,eAAe,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,kBAAkB,CAAC,CAAC,CAAA;IACnI,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;QACxB,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,WAAW,CAAC,CAAA;IACtC,CAAC;IACD,OAAO,IAAA,oBAAK,EAAC,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,kBAAkB,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;AAC/G,CAAC;AA0BD,MAAa,eAAe;IAC1B,YACmB,OAAwB,EACxB,eAAuB,EACvB,QAAqB;QAFrB,YAAO,GAAP,OAAO,CAAiB;QACxB,oBAAe,GAAf,eAAe,CAAQ;QACvB,aAAQ,GAAR,QAAQ,CAAa;IACrC,CAAC;IAEJ,KAAK,CAAC,cAAc,CAAC,YAA0B,EAAE,SAAiB,EAAE,MAAc,EAAE,IAAU;QAC5F,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC9B,MAAM,YAAY,GAAG,MAAM,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,EAAE,MAAM,EAAE,kBAAkB,EAAE,CAAC,CAAA;QACrG,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,CAAA;QAC5C,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;QAC5B,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,YAAY,CAAC,CAAA;QACvD,MAAM,OAAO,CAAC,GAAG,CAAC;YAChB,IAAA,uBAAQ,EAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,YAAY,CAAC,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACrG,OAAO,CAAC,GAAG,CAAC;gBACV,EAAE,CAAC,EAAE,CAAC,GAAG,eAAe,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,eAAe,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;gBAC9F,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,UAAU,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;aAChF,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,eAAe,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;SAC9D,CAAC,CAAA;QAEF,IAAI,IAAA,8BAAe,EAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;YACzC,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAA;QAC3C,CAAC;QAED,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;YAC3B,MAAM,YAAY,CAAC,eAAe,EAAE,OAAO,CAAC,CAAA;QAC9C,CAAC;QAED,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QAC/C,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,YAAY,CAAC,WAAW,CAAC,CAAA;QACtE,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,YAAY,CAAC,SAAS,CAAC,CAAA;QAEpE,MAAM,OAAO,CAAC,GAAG,CAAM;YACrB,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,CAAC;YACjE,IAAA,uBAAQ,EAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,SAAS,CAAC;YAC/D,IAAA,uBAAQ,EACN,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,qBAAqB,CAAC,EACjI,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAC1C;SACF,CAAC,CAAA;QAEF,+IAA+I;QAC/I,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,YAAY,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,IAAA,oBAAS,EAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,UAAU,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;QAExH,MAAM,mBAAmB,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,SAAS,EAAE,YAAY,CAAC,CAAA;QAEzF,MAAM,IAAA,eAAQ,EAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,qBAAqB,CAAC,EAAE,IAAI,EAAE,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC,CAAA;QAE5G,MAAM,QAAQ,CAAC,oBAAoB,CAAC,SAAS,EAAE,IAAI,EAAE,MAAM,CAAC,CAAA;QAC5D,IAAI,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;YAChD,MAAM,OAAO,GAAG,YAAY,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;YAC9D,MAAM,GAAG,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,eAAe,EAAE,OAAO,CAAC,CAAA;YAClE,sCAAsC;YACtC,MAAM,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC,CAAA;QAC1D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,SAAS,CAAC,SAAiB,EAAE,WAAmB;QAC5D,MAAM,IAAI,GAAG,CAAC,aAAa,EAAE,SAAS,EAAE,cAAc,EAAE,IAAI,CAAC,eAAe,CAAC,CAAA;QAC7E,MAAM,GAAG,GAAG,CAAC,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAA;QACrD,IAAI,oBAAK,CAAC,OAAO,EAAE,CAAC;YAClB,IAAA,oBAAK,EAAC,oBAAoB,GAAG,EAAE,CAAC,CAAA;QAClC,CAAC;QAED,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;QAC7B,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC3C,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;YACrB,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC/B,OAAO,IAAI,CAAC,IAAI,EAAE,CAAA;YACpB,CAAC;QACH,CAAC;QAED,MAAM,IAAI,KAAK,CAAC,2DAA2D,GAAG,EAAE,CAAC,CAAA;IACnF,CAAC;IAEO,KAAK,CAAC,yBAAyB,CAAC,SAAiB,EAAE,YAAoB;QAC7E,MAAM,mBAAmB,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,WAAW,CAAC,CAAA;QACxE,MAAM,OAAO,GAAG,MAAM,IAAA,yBAAU,GAAE,CAAA;QAClC,MAAM,IAAA,mBAAI,EACR,OAAO,EACP,IAAA,+BAAqB,EAAC,KAAK,EAAE;YAC3B,aAAa,EAAE,IAAI;YACnB,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,WAAW;SACvC,CAAC,CAAC,MAAM,CAAC,mBAAmB,EAAE,GAAG,CAAC,EACnC;YACE,GAAG,EAAE,YAAY;SAClB,CACF,CAAA;QACD,MAAM,IAAA,mBAAI,EACR,OAAO,EACP,IAAA,+BAAqB,EAAC,KAAK,EAAE;YAC3B,aAAa,EAAE,IAAI;YACnB,WAAW,EAAE,OAAO,CAAC,iCAAiC;SACvD,CAAC,CAAC,MAAM,CAAC,mBAAmB,EAAE,SAAS,CAAC,CAC1C,CAAA;QACD,OAAO,mBAAmB,CAAA;IAC5B,CAAC;CACF;AAlGD,0CAkGC;AAED,KAAK,UAAU,IAAI,CAAC,OAAwB,EAAE,SAAiB,EAAE,UAAkB,EAAE,OAAe,EAAE,OAAe,EAAE,QAAqB;IAC1I,8HAA8H;IAC9H,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,uBAAuB,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,uBAAuB,CAAC,EAAE,EAAE,CAAC,CAAA;IAChJ,MAAM,UAAU,GAAG,IAAA,4BAAiB,EAAC,OAAO,CAAC,CAAA;IAC7C,MAAM,cAAc,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrD,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;QAC3B,UAAU,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;QAC9B,UAAU,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;IACjC,CAAC,CAAC,CAAA;IACF,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;IAExB,MAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAA;IAC9B,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,eAAe,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,IAAI,MAAM,EAAE,CAAA;IAC1F,MAAM,aAAa,GAAG;;;UAGd,OAAO,CAAC,KAAK;eACR,OAAO;aACT,OAAO,CAAC,WAAW;eACjB,MAAM;eACN,OAAO,CAAC,OAAO;;mBAEX,OAAO,CAAC,WAAW;iBACrB,SAAS,eAAe,OAAO,CAAC,kBAAkB,IAAI,EAAE;;WAE9D,CAAA;IACT,IAAA,oBAAK,EAAC,yBAAyB,aAAa,EAAE,CAAC,CAAA;IAC/C,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC,IAAI,SAAS,EAAE,CAAC,CAAA;IAEvF,sCAAsC;IACtC,OAAO,CAAC,MAAM,CACZ;;0FAEsF,OAAO,CAAC,IAAI;;iBAErF,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,EACnC,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,CACnC,CAAA;IAED,sCAAsC;IACtC,OAAO,CAAC,MAAM,CACZ;;;;;;;;;;;;;;;;SAgBK,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,EAC3B,EAAE,IAAI,EAAE,qBAAqB,EAAE,CAChC,CAAA;IAED,OAAO,CAAC,MAAM,CACZ;;;gBAGY,MAAM;oBACF,OAAO,CAAC,WAAW;mBACpB,OAAO,CAAC,KAAK;aACnB,OAAO;;cAEN,OAAO,CAAC,WAAW;;kBAEf,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,EACpC,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,2CAA2C,EAAE,CAC1E,CAAA;IAED,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC,CAAA;IACrE,MAAM,UAAU,CAAC,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAA;IAC/E,MAAM,cAAc,CAAA;AACtB,CAAC;AAED,KAAK,UAAU,MAAM,CAAC,OAAwB,EAAE,IAAmB;IACjE,OAAO,IAAA,mBAAI,EAAC,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,IAAA,mCAAW,EAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,iBAAiB,CAAC,CAAC,EAAE;QACpK,GAAG,EAAE;YACH,GAAG,OAAO,CAAC,GAAG;YACd,QAAQ,EAAE,MAAM,IAAA,yBAAU,GAAE;SAC7B;KACF,CAAC,CAAA;AACJ,CAAC;AAED,KAAK,UAAU,GAAG,CAAC,OAAwB,EAAE,SAAiB,EAAE,SAAiB,EAAE,eAAuB,EAAE,OAAe;IACzH,MAAM,IAAI,GAAG,CAAC,aAAa,EAAE,SAAS,EAAE,mBAAmB,EAAE,SAAS,CAAC,CAAA;IACvE,MAAM,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;IAC3B,sCAAsC;IACtC,MAAM,IAAA,mBAAI,EAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,YAAY,CAAC,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,cAAc,EAAE,WAAW,CAAC,EAAE;QACrI,GAAG,EAAE,eAAe;KACrB,CAAC,CAAA;IACF,sCAAsC;IACtC,MAAM,IAAA,mBAAI,EAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,CAAC,MAAM,EAAE,mBAAmB,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,cAAc,CAAC,EAAE;QAC9H,GAAG,EAAE,eAAe;KACrB,CAAC,CAAA;IAEF,sCAAsC;IACtC,MAAM,OAAO,CAAC,GAAG,CAAC;QAChB,IAAA,iBAAM,EAAC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC;QAC/C,IAAA,iBAAM,EAAC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC;QAClD,IAAA,iBAAM,EAAC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,IAAA,oBAAK,EAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;KAC9G,CAAC,CAAA;AACJ,CAAC;AAED,KAAK,UAAU,UAAU,CAAC,OAAY,EAAE,GAAW,EAAE,MAAc,EAAE,UAAkB,EAAE,QAAqB;IAC5G,MAAM,IAAA,mBAAI,EAAC,GAAG,EAAE,IAAI,EAAE;QACpB,YAAY,EAAE,IAAI;QAClB,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;YAC7B,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC;gBACxB,OAAM;YACR,CAAC;YAED,MAAM,oBAAoB,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;YAC/E,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE;gBACpB,IAAI,EAAE,oBAAoB;gBAC1B,MAAM;gBACN,KAAK;aACN,CAAC,CAAA;YAEF,6BAA6B;YAC7B,qHAAqH;YACrH,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBACnG,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,WAAW,CAAC,UAAU,CAAC,CAAA;gBACvD,MAAM,IAAA,uBAAQ,EAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,oBAAoB,CAAC,EAAE,QAAQ,CAAC,CAAA;gBACrE,MAAM,IAAA,eAAQ,EAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,qBAAqB,CAAC,EAAE,IAAI,EAAE,CAAC,uBAAuB,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAA;gBAC7G,MAAM,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;gBAE7B,OAAO,CAAC,OAAO,CAAC,QAAQ,EAAE;oBACxB,IAAI,EAAE,oBAAoB,CAAC,SAAS,CAAC,CAAC,EAAE,oBAAoB,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,oBAAoB;oBAC/F,MAAM;oBACN,KAAK,EAAE,MAAM,IAAA,eAAI,EAAC,QAAQ,CAAC;iBAC5B,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;KACF,CAAC,CAAA;IACF,OAAO,CAAC,QAAQ,EAAE,CAAA;AACpB,CAAC", "sourcesContent": ["import { Arch, debug, exec, log, spawn, isEmptyOrSpaces, getPath7za, copyFile, walk } from \"builder-util\"\nimport { compute7zCompressArgs } from \"app-builder-lib/out/targets/archive\"\nimport { execWine, prepareWindowsExecutableArgs as prepareArgs } from \"app-builder-lib/out/wine\"\nimport { WinPackager } from \"app-builder-lib/out/winPackager\"\nimport { createWriteStream, stat, unlink, writeFile } from \"fs-extra\"\nimport * as path from \"path\"\nimport * as archiver from \"archiver\"\nimport * as fs from \"fs/promises\"\n\nexport function convertVersion(version: string): string {\n  const parts = version.split(\"-\")\n  const mainVersion = parts.shift()\n  if (parts.length > 0) {\n    return [mainVersion, parts.join(\"-\").replace(/\\./g, \"\")].join(\"-\")\n  } else {\n    return mainVersion!\n  }\n}\n\nfunction syncReleases(outputDirectory: string, options: SquirrelOptions) {\n  log.info(\"syncing releases to build delta package\")\n  const args = prepareArgs([\"-u\", options.remoteReleases!, \"-r\", outputDirectory], path.join(options.vendorPath, \"SyncReleases.exe\"))\n  if (options.remoteToken) {\n    args.push(\"-t\", options.remoteToken)\n  }\n  return spawn(process.platform === \"win32\" ? path.join(options.vendorPath, \"SyncReleases.exe\") : \"mono\", args)\n}\n\nexport interface SquirrelOptions {\n  vendorPath: string\n  remoteReleases?: string\n  remoteToken?: string\n  loadingGif?: string\n  productName: string\n  appId?: string\n  name: string\n  packageCompressionLevel?: number\n  version: string\n  msi?: any\n\n  description?: string\n  iconUrl?: string\n  authors?: string\n  extraMetadataSpecs?: string\n  copyright?: string\n}\n\nexport interface OutFileNames {\n  setupFile: string\n  packageFile: string\n}\n\nexport class SquirrelBuilder {\n  constructor(\n    private readonly options: SquirrelOptions,\n    private readonly outputDirectory: string,\n    private readonly packager: WinPackager\n  ) {}\n\n  async buildInstaller(outFileNames: OutFileNames, appOutDir: string, outDir: string, arch: Arch) {\n    const packager = this.packager\n    const dirToArchive = await packager.info.tempDirManager.createTempDir({ prefix: \"squirrel-windows\" })\n    const outputDirectory = this.outputDirectory\n    const options = this.options\n    const appUpdate = path.join(dirToArchive, \"Update.exe\")\n    await Promise.all([\n      copyFile(path.join(options.vendorPath, \"Update.exe\"), appUpdate).then(() => packager.sign(appUpdate)),\n      Promise.all([\n        fs.rm(`${outputDirectory.replace(/\\\\/g, \"/\")}/*-full.nupkg`, { recursive: true, force: true }),\n        fs.rm(path.join(outputDirectory, \"RELEASES\"), { recursive: true, force: true }),\n      ]).then(() => fs.mkdir(outputDirectory, { recursive: true })),\n    ])\n\n    if (isEmptyOrSpaces(options.description)) {\n      options.description = options.productName\n    }\n\n    if (options.remoteReleases) {\n      await syncReleases(outputDirectory, options)\n    }\n\n    const version = convertVersion(options.version)\n    const nupkgPath = path.join(outputDirectory, outFileNames.packageFile)\n    const setupPath = path.join(outputDirectory, outFileNames.setupFile)\n\n    await Promise.all<any>([\n      pack(options, appOutDir, appUpdate, nupkgPath, version, packager),\n      copyFile(path.join(options.vendorPath, \"Setup.exe\"), setupPath),\n      copyFile(\n        options.loadingGif ? path.resolve(packager.projectDir, options.loadingGif) : path.join(options.vendorPath, \"install-spinner.gif\"),\n        path.join(dirToArchive, \"background.gif\")\n      ),\n    ])\n\n    // releasify can be called only after pack nupkg and nupkg must be in the final output directory (where other old version nupkg can be located)\n    await this.releasify(nupkgPath, outFileNames.packageFile).then(it => writeFile(path.join(dirToArchive, \"RELEASES\"), it))\n\n    const embeddedArchiveFile = await this.createEmbeddedArchiveFile(nupkgPath, dirToArchive)\n\n    await execWine(path.join(options.vendorPath, \"WriteZipToSetup.exe\"), null, [setupPath, embeddedArchiveFile])\n\n    await packager.signAndEditResources(setupPath, arch, outDir)\n    if (options.msi && process.platform === \"win32\") {\n      const outFile = outFileNames.setupFile.replace(\".exe\", \".msi\")\n      await msi(options, nupkgPath, setupPath, outputDirectory, outFile)\n      // rcedit can only edit .exe resources\n      await packager.sign(path.join(outputDirectory, outFile))\n    }\n  }\n\n  private async releasify(nupkgPath: string, packageName: string) {\n    const args = [\"--releasify\", nupkgPath, \"--releaseDir\", this.outputDirectory]\n    const out = (await execSw(this.options, args)).trim()\n    if (debug.enabled) {\n      debug(`Squirrel output: ${out}`)\n    }\n\n    const lines = out.split(\"\\n\")\n    for (let i = lines.length - 1; i > -1; i--) {\n      const line = lines[i]\n      if (line.includes(packageName)) {\n        return line.trim()\n      }\n    }\n\n    throw new Error(`Invalid output, cannot find last release entry, output: ${out}`)\n  }\n\n  private async createEmbeddedArchiveFile(nupkgPath: string, dirToArchive: string) {\n    const embeddedArchiveFile = await this.packager.getTempFile(\"setup.zip\")\n    const path7za = await getPath7za()\n    await exec(\n      path7za,\n      compute7zCompressArgs(\"zip\", {\n        isRegularFile: true,\n        compression: this.packager.compression,\n      }).concat(embeddedArchiveFile, \".\"),\n      {\n        cwd: dirToArchive,\n      }\n    )\n    await exec(\n      path7za,\n      compute7zCompressArgs(\"zip\", {\n        isRegularFile: true,\n        compression: \"store\" /* nupkg is already compressed */,\n      }).concat(embeddedArchiveFile, nupkgPath)\n    )\n    return embeddedArchiveFile\n  }\n}\n\nasync function pack(options: SquirrelOptions, directory: string, updateFile: string, outFile: string, version: string, packager: WinPackager) {\n  // SW now doesn't support 0-level nupkg compressed files. It means that we are forced to use level 1 if store level requested.\n  const archive = archiver(\"zip\", { zlib: { level: Math.max(1, options.packageCompressionLevel == null ? 9 : options.packageCompressionLevel) } })\n  const archiveOut = createWriteStream(outFile)\n  const archivePromise = new Promise((resolve, reject) => {\n    archive.on(\"error\", reject)\n    archiveOut.on(\"error\", reject)\n    archiveOut.on(\"close\", resolve)\n  })\n  archive.pipe(archiveOut)\n\n  const author = options.authors\n  const copyright = options.copyright || `Copyright © ${new Date().getFullYear()} ${author}`\n  const nuspecContent = `<?xml version=\"1.0\"?>\n<package xmlns=\"http://schemas.microsoft.com/packaging/2011/08/nuspec.xsd\">\n  <metadata>\n    <id>${options.appId}</id>\n    <version>${version}</version>\n    <title>${options.productName}</title>\n    <authors>${author}</authors>\n    <iconUrl>${options.iconUrl}</iconUrl>\n    <requireLicenseAcceptance>false</requireLicenseAcceptance>\n    <description>${options.description}</description>\n    <copyright>${copyright}</copyright>${options.extraMetadataSpecs || \"\"}\n  </metadata>\n</package>`\n  debug(`Created NuSpec file:\\n${nuspecContent}`)\n  archive.append(nuspecContent.replace(/\\n/, \"\\r\\n\"), { name: `${options.name}.nuspec` })\n\n  //noinspection SpellCheckingInspection\n  archive.append(\n    `<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<Relationships xmlns=\"http://schemas.openxmlformats.org/package/2006/relationships\">\n  <Relationship Type=\"http://schemas.microsoft.com/packaging/2010/07/manifest\" Target=\"/${options.name}.nuspec\" Id=\"Re0\" />\n  <Relationship Type=\"http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties\" Target=\"/package/services/metadata/core-properties/1.psmdcp\" Id=\"Re1\" />\n</Relationships>`.replace(/\\n/, \"\\r\\n\"),\n    { name: \".rels\", prefix: \"_rels\" }\n  )\n\n  //noinspection SpellCheckingInspection\n  archive.append(\n    `<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<Types xmlns=\"http://schemas.openxmlformats.org/package/2006/content-types\">\n  <Default Extension=\"rels\" ContentType=\"application/vnd.openxmlformats-package.relationships+xml\" />\n  <Default Extension=\"nuspec\" ContentType=\"application/octet\" />\n  <Default Extension=\"pak\" ContentType=\"application/octet\" />\n  <Default Extension=\"asar\" ContentType=\"application/octet\" />\n  <Default Extension=\"bin\" ContentType=\"application/octet\" />\n  <Default Extension=\"dll\" ContentType=\"application/octet\" />\n  <Default Extension=\"exe\" ContentType=\"application/octet\" />\n  <Default Extension=\"dat\" ContentType=\"application/octet\" />\n  <Default Extension=\"psmdcp\" ContentType=\"application/vnd.openxmlformats-package.core-properties+xml\" />\n  <Default Extension=\"diff\" ContentType=\"application/octet\" />\n  <Default Extension=\"bsdiff\" ContentType=\"application/octet\" />\n  <Default Extension=\"shasum\" ContentType=\"text/plain\" />\n  <Default Extension=\"mp3\" ContentType=\"audio/mpeg\" />\n  <Default Extension=\"node\" ContentType=\"application/octet\" />\n</Types>`.replace(/\\n/, \"\\r\\n\"),\n    { name: \"[Content_Types].xml\" }\n  )\n\n  archive.append(\n    `<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<coreProperties xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:dcterms=\"http://purl.org/dc/terms/\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"\n                xmlns=\"http://schemas.openxmlformats.org/package/2006/metadata/core-properties\">\n  <dc:creator>${author}</dc:creator>\n  <dc:description>${options.description}</dc:description>\n  <dc:identifier>${options.appId}</dc:identifier>\n  <version>${version}</version>\n  <keywords/>\n  <dc:title>${options.productName}</dc:title>\n  <lastModifiedBy>NuGet, Version=2.8.50926.602, Culture=neutral, PublicKeyToken=null;Microsoft Windows NT 6.2.9200.0;.NET Framework 4</lastModifiedBy>\n</coreProperties>`.replace(/\\n/, \"\\r\\n\"),\n    { name: \"1.psmdcp\", prefix: \"package/services/metadata/core-properties\" }\n  )\n\n  archive.file(updateFile, { name: \"Update.exe\", prefix: \"lib/net45\" })\n  await encodedZip(archive, directory, \"lib/net45\", options.vendorPath, packager)\n  await archivePromise\n}\n\nasync function execSw(options: SquirrelOptions, args: Array<string>) {\n  return exec(process.platform === \"win32\" ? path.join(options.vendorPath, \"Update.com\") : \"mono\", prepareArgs(args, path.join(options.vendorPath, \"Update-Mono.exe\")), {\n    env: {\n      ...process.env,\n      SZA_PATH: await getPath7za(),\n    },\n  })\n}\n\nasync function msi(options: SquirrelOptions, nupkgPath: string, setupPath: string, outputDirectory: string, outFile: string) {\n  const args = [\"--createMsi\", nupkgPath, \"--bootstrapperExe\", setupPath]\n  await execSw(options, args)\n  //noinspection SpellCheckingInspection\n  await exec(path.join(options.vendorPath, \"candle.exe\"), [\"-nologo\", \"-ext\", \"WixNetFxExtension\", \"-out\", \"Setup.wixobj\", \"Setup.wxs\"], {\n    cwd: outputDirectory,\n  })\n  //noinspection SpellCheckingInspection\n  await exec(path.join(options.vendorPath, \"light.exe\"), [\"-ext\", \"WixNetFxExtension\", \"-sval\", \"-out\", outFile, \"Setup.wixobj\"], {\n    cwd: outputDirectory,\n  })\n\n  //noinspection SpellCheckingInspection\n  await Promise.all([\n    unlink(path.join(outputDirectory, \"Setup.wxs\")),\n    unlink(path.join(outputDirectory, \"Setup.wixobj\")),\n    unlink(path.join(outputDirectory, outFile.replace(\".msi\", \".wixpdb\"))).catch((e: any) => debug(e.toString())),\n  ])\n}\n\nasync function encodedZip(archive: any, dir: string, prefix: string, vendorPath: string, packager: WinPackager) {\n  await walk(dir, null, {\n    isIncludeDir: true,\n    consume: async (file, stats) => {\n      if (stats.isDirectory()) {\n        return\n      }\n\n      const relativeSafeFilePath = file.substring(dir.length + 1).replace(/\\\\/g, \"/\")\n      archive._append(file, {\n        name: relativeSafeFilePath,\n        prefix,\n        stats,\n      })\n\n      // createExecutableStubForExe\n      // https://github.com/Squirrel/Squirrel.Windows/pull/1051 Only generate execution stubs for the top-level executables\n      if (file.endsWith(\".exe\") && !file.includes(\"squirrel.exe\") && !relativeSafeFilePath.includes(\"/\")) {\n        const tempFile = await packager.getTempFile(\"stub.exe\")\n        await copyFile(path.join(vendorPath, \"StubExecutable.exe\"), tempFile)\n        await execWine(path.join(vendorPath, \"WriteZipToSetup.exe\"), null, [\"--copy-stub-resources\", file, tempFile])\n        await packager.sign(tempFile)\n\n        archive._append(tempFile, {\n          name: relativeSafeFilePath.substring(0, relativeSafeFilePath.length - 4) + \"_ExecutionStub.exe\",\n          prefix,\n          stats: await stat(tempFile),\n        })\n      }\n    },\n  })\n  archive.finalize()\n}\n"]}