{"name": "electron-to-chromium", "version": "1.5.129", "description": "Provides a list of electron-to-chromium version mappings", "main": "index.js", "files": ["versions.js", "full-versions.js", "chromium-versions.js", "full-chromium-versions.js", "versions.json", "full-versions.json", "chromium-versions.json", "full-chromium-versions.json", "LICENSE"], "scripts": {"build": "node build.mjs", "update": "node automated-update.js", "test": "nyc ava --verbose", "report": "nyc report --reporter=text-lcov > coverage.lcov && codecov"}, "repository": {"type": "git", "url": "https://github.com/kilian/electron-to-chromium/"}, "keywords": ["electron", "chrome", "chromium", "browserslist", "browserlist"], "author": "<PERSON><PERSON>", "license": "ISC", "devDependencies": {"ava": "^5.1.1", "codecov": "^3.8.2", "compare-versions": "^6.0.0-rc.1", "node-fetch": "^3.3.0", "nyc": "^15.1.0", "shelljs": "^0.8.5"}}