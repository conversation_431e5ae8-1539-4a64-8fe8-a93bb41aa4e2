{"name": "character-reference-invalid", "version": "2.0.1", "description": "Map of invalid numeric character references to their replacements, according to HTML", "license": "MIT", "keywords": ["html", "entity", "numeric", "character", "reference", "replacement", "invalid", "name"], "repository": "wooorm/character-reference-invalid", "bugs": "https://github.com/wooorm/character-reference-invalid/issues", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "sideEffects": false, "type": "module", "main": "index.js", "types": "index.d.ts", "files": ["index.d.ts", "index.js"], "devDependencies": {"@types/tape": "^4.0.0", "bail": "^2.0.0", "c8": "^7.0.0", "concat-stream": "^2.0.0", "hast-util-select": "^5.0.0", "hast-util-to-string": "^2.0.0", "prettier": "^2.0.0", "rehype-parse": "^8.0.0", "remark-cli": "^10.0.0", "remark-preset-wooorm": "^9.0.0", "rimraf": "^3.0.0", "tape": "^5.0.0", "type-coverage": "^2.0.0", "typescript": "^4.0.0", "unified": "^10.0.0", "xo": "^0.45.0"}, "scripts": {"prepublishOnly": "npm run build && npm run format", "generate": "node build", "build": "rimraf \"*.d.ts\" && tsc && type-coverage", "format": "remark . -qfo && prettier . -w --loglevel warn && xo --fix", "test-api": "node --conditions development test.js", "test-coverage": "c8 --check-coverage --branches 100 --functions 100 --lines 100 --statements 100 --reporter lcov npm run test-api", "test": "npm run generate && npm run build && npm run format && npm run test-coverage"}, "prettier": {"tabWidth": 2, "useTabs": false, "singleQuote": true, "bracketSpacing": false, "semi": false, "trailingComma": "none"}, "xo": {"prettier": true}, "remarkConfig": {"plugins": ["preset-wooorm"]}, "typeCoverage": {"atLeast": 100, "detail": true, "strict": true, "ignoreCatch": true}}