{"version": 3, "file": "publisher.js", "sourceRoot": "", "sources": ["../src/publisher.ts"], "names": [], "mappings": ";;;AAqIA,4BAWC;AAhJD,+CAAiD;AACjD,+DAAoG;AACpG,+BAA8B;AAC9B,uCAAwD;AAExD,+BAA+B;AAM/B,uCAA6C;AAApC,4GAAA,gBAAgB,OAAA;AAWzB,MAAM,kBAAkB,GAAG;IACzB,UAAU,EAAE,GAAG;IACf,KAAK,EAAE,EAAE;CACV,CAAA;AAWD,MAAsB,SAAS;IAC7B,YAAyC,OAAuB;QAAvB,YAAO,GAAP,OAAO,CAAgB;IAAG,CAAC;IAM1D,iBAAiB,CAAC,QAAgB,EAAE,IAAY;QACxD,kBAAG,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC,YAAY,EAAE,EAAE,WAAW,CAAC,CAAA;QACtE,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,IAAI,EAAE,CAAC;YACvD,OAAO,IAAI,CAAA;QACb,CAAC;QACD,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,sBAAO,GAAG,CAAC,CAAC,2BAA2B,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,IAAI,CAAC,YAAY,EAAE,EAAE;YAC3I,KAAK,EAAE,IAAI;YACX,GAAG,kBAAkB;SACtB,CAAC,CAAA;IACJ,CAAC;IAES,8BAA8B,CAAC,IAAY,EAAE,QAAe,EAAE,WAA+B,EAAE,MAA8B;QACrI,MAAM,eAAe,GAAG,IAAA,2BAAgB,EAAC,IAAI,CAAC,CAAA;QAC9C,eAAe,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;QAEnC,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;YACxB,OAAO,eAAe,CAAA;QACxB,CAAC;aAAM,CAAC;YACN,MAAM,cAAc,GAAG,IAAI,gDAAyB,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAA;YACrI,cAAc,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;YAClC,OAAO,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;QAC7C,CAAC;IACH,CAAC;CAGF;AAhCD,8BAgCC;AAED,MAAsB,aAAc,SAAQ,SAAS;IACnD,YACqB,OAAuB,EACzB,sBAAsB,KAAK;QAE5C,KAAK,CAAC,OAAO,CAAC,CAAA;QAHK,YAAO,GAAP,OAAO,CAAgB;QACzB,wBAAmB,GAAnB,mBAAmB,CAAQ;IAG9C,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,IAAgB;QAC3B,MAAM,QAAQ,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,IAAA,eAAQ,EAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAEjG,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,EAAE,CAAC;YAC7B,MAAM,IAAI,CAAC,QAAQ,CACjB,QAAQ,EACR,IAAI,CAAC,IAAI,IAAI,mBAAI,CAAC,GAAG,EACrB,IAAI,CAAC,WAAW,CAAC,MAAM,EACvB,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBAClB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;oBACjB,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE;wBACpC,OAAO,CAAC,OAAO,EAAE,CAAA;wBACjB,MAAM,CAAC,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAAA;oBACxC,CAAC,CAAC,CAAA;gBACJ,CAAC;gBACD,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YACtC,CAAC,EACD,IAAI,CAAC,IAAI,CACV,CAAA;YACD,OAAM;QACR,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,IAAA,eAAI,EAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAEtC,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAA;QACnE,OAAO,IAAI,CAAC,QAAQ,CAClB,QAAQ,EACR,IAAI,CAAC,IAAI,IAAI,mBAAI,CAAC,GAAG,EACrB,QAAQ,CAAC,IAAI,EACb,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAClB,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;gBACxB,gEAAgE;gBAChE,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;YACvB,CAAC;YACD,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACjB,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE;oBACpC,OAAO,CAAC,OAAO,EAAE,CAAA;oBACjB,MAAM,CAAC,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAAA;gBACxC,CAAC,CAAC,CAAA;YACJ,CAAC;YACD,OAAO,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QACpG,CAAC,EACD,IAAI,CAAC,IAAI,CACV,CAAA;IACH,CAAC;CASF;AA7DD,sCA6DC;AAED,SAAgB,QAAQ;IACtB,MAAM,GAAG,GACP,OAAO,CAAC,GAAG,CAAC,UAAU;QACtB,OAAO,CAAC,GAAG,CAAC,sBAAsB;QAClC,OAAO,CAAC,GAAG,CAAC,UAAU;QACtB,OAAO,CAAC,GAAG,CAAC,eAAe;QAC3B,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,kDAAkD;QAC9E,OAAO,CAAC,GAAG,CAAC,aAAa;QACzB,OAAO,CAAC,GAAG,CAAC,aAAa;QACzB,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,KAAK,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;IAC9E,OAAO,GAAG,IAAI,IAAI,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAA;AACnD,CAAC", "sourcesContent": ["import { Arch, log, PADDING } from \"builder-util\"\nimport { CancellationToken, ProgressCallbackTransform, PublishProvider } from \"builder-util-runtime\"\nimport * as chalk from \"chalk\"\nimport { createReadStream, stat, Stats } from \"fs-extra\"\nimport { ClientRequest } from \"http\"\nimport { basename } from \"path\"\nimport { MultiProgress } from \"./multiProgress\"\nimport { ProgressBar } from \"./progress\"\n\nexport type PublishPolicy = \"onTag\" | \"onTagOrDraft\" | \"always\" | \"never\"\n\nexport { ProgressCallback } from \"./progress\"\n\nexport interface PublishOptions {\n  publish?: PublishPolicy | null\n}\n\nexport interface PublishContext {\n  readonly cancellationToken: CancellationToken\n  readonly progress: MultiProgress | null\n}\n\nconst progressBarOptions = {\n  incomplete: \" \",\n  width: 20,\n}\n\nexport interface UploadTask {\n  file: string\n  fileContent?: Buffer | null\n\n  arch: Arch | null\n  safeArtifactName?: string | null\n  timeout?: number | null\n}\n\nexport abstract class Publisher {\n  protected constructor(protected readonly context: PublishContext) {}\n\n  abstract get providerName(): PublishProvider\n\n  abstract upload(task: UploadTask): Promise<any>\n\n  protected createProgressBar(fileName: string, size: number): ProgressBar | null {\n    log.info({ file: fileName, provider: this.providerName }, \"uploading\")\n    if (this.context.progress == null || size < 512 * 1024) {\n      return null\n    }\n    return this.context.progress.createBar(`${\" \".repeat(PADDING + 2)}[:bar] :percent :etas | ${chalk.green(fileName)} to ${this.providerName}`, {\n      total: size,\n      ...progressBarOptions,\n    })\n  }\n\n  protected createReadStreamAndProgressBar(file: string, fileStat: Stats, progressBar: ProgressBar | null, reject: (error: Error) => void): NodeJS.ReadableStream {\n    const fileInputStream = createReadStream(file)\n    fileInputStream.on(\"error\", reject)\n\n    if (progressBar == null) {\n      return fileInputStream\n    } else {\n      const progressStream = new ProgressCallbackTransform(fileStat.size, this.context.cancellationToken, it => progressBar.tick(it.delta))\n      progressStream.on(\"error\", reject)\n      return fileInputStream.pipe(progressStream)\n    }\n  }\n\n  abstract toString(): string\n}\n\nexport abstract class HttpPublisher extends Publisher {\n  protected constructor(\n    protected readonly context: PublishContext,\n    private readonly useSafeArtifactName = false\n  ) {\n    super(context)\n  }\n\n  async upload(task: UploadTask): Promise<any> {\n    const fileName = (this.useSafeArtifactName ? task.safeArtifactName : null) || basename(task.file)\n\n    if (task.fileContent != null) {\n      await this.doUpload(\n        fileName,\n        task.arch || Arch.x64,\n        task.fileContent.length,\n        (request, reject) => {\n          if (task.timeout) {\n            request.setTimeout(task.timeout, () => {\n              request.destroy()\n              reject(new Error(\"Request timed out\"))\n            })\n          }\n          return request.end(task.fileContent)\n        },\n        task.file\n      )\n      return\n    }\n\n    const fileStat = await stat(task.file)\n\n    const progressBar = this.createProgressBar(fileName, fileStat.size)\n    return this.doUpload(\n      fileName,\n      task.arch || Arch.x64,\n      fileStat.size,\n      (request, reject) => {\n        if (progressBar != null) {\n          // reset (because can be called several times (several attempts)\n          progressBar.update(0)\n        }\n        if (task.timeout) {\n          request.setTimeout(task.timeout, () => {\n            request.destroy()\n            reject(new Error(\"Request timed out\"))\n          })\n        }\n        return this.createReadStreamAndProgressBar(task.file, fileStat, progressBar, reject).pipe(request)\n      },\n      task.file\n    )\n  }\n\n  protected abstract doUpload(\n    fileName: string,\n    arch: Arch,\n    dataLength: number,\n    requestProcessor: (request: ClientRequest, reject: (error: Error) => void) => void,\n    file: string\n  ): Promise<any>\n}\n\nexport function getCiTag() {\n  const tag =\n    process.env.TRAVIS_TAG ||\n    process.env.APPVEYOR_REPO_TAG_NAME ||\n    process.env.CIRCLE_TAG ||\n    process.env.BITRISE_GIT_TAG ||\n    process.env.CI_BUILD_TAG || // deprecated, GitLab uses `CI_COMMIT_TAG` instead\n    process.env.CI_COMMIT_TAG ||\n    process.env.BITBUCKET_TAG ||\n    (process.env.GITHUB_REF_TYPE === \"tag\" ? process.env.GITHUB_REF_NAME : null)\n  return tag != null && tag.length > 0 ? tag : null\n}\n"]}