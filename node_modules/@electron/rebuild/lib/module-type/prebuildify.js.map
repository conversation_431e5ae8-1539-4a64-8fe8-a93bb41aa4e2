{"version": 3, "file": "prebuildify.js", "sourceRoot": "", "sources": ["../../src/module-type/prebuildify.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA0B;AAC1B,wDAA0B;AAC1B,gDAAwB;AAExB,kCAAuD;AACvD,wBAAiC;AAEjC,MAAM,CAAC,GAAG,IAAA,eAAK,EAAC,kBAAkB,CAAC,CAAC;AAEpC,SAAgB,2BAA2B,CAAC,IAAY;IACtD,IAAI,IAAI,KAAK,QAAQ,EAAE;QACrB,OAAO,KAAK,CAAC;KACd;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAND,kEAMC;AAED;;;GAGG;AACH,SAAgB,gCAAgC,CAAC,IAAY;IAC3D,QAAQ,IAAI,EAAE;QACZ,KAAK,OAAO;YACV,OAAO,YAAY,CAAC;QACtB,KAAK,QAAQ;YACX,OAAO,YAAY,CAAC;KACvB;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AATD,4EASC;AAED,MAAa,WAAY,SAAQ,eAAY;IAC3C,KAAK,CAAC,QAAQ;QACZ,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC;QACtF,iDAAiD;QACjD,OAAO,eAAe,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;IACvD,CAAC;IAED,KAAK,CAAC,kBAAkB;QACtB,MAAM,QAAQ,GAAG,IAAA,kBAAW,EAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,SAA4B,CAAC,CAAC;QAE/F,CAAC,CAAC,+BAA+B,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QAErD,MAAM,YAAY,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;QAC7D,IAAI,CAAC,CAAC,MAAM,kBAAE,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,EAAE;YACxC,CAAC,CAAC,8CAA8C,YAAY,GAAG,CAAC,CAAC;YACjE,OAAO,KAAK,CAAC;SACd;QAED,MAAM,iBAAiB,GAAG,cAAI,CAAC,IAAI,CAAC,YAAY,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,IAAI,2BAA2B,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QACzH,MAAM,SAAS,GAAG,gCAAgC,CAAC,QAAQ,CAAC,CAAC;QAC7D,MAAM,0BAA0B,GAAG,cAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,iBAAiB,SAAS,EAAE,CAAC,CAAC;QAC9F,MAAM,wBAAwB,GAAG,cAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,aAAa,SAAS,EAAE,CAAC,CAAC;QACxF,MAAM,iBAAiB,GAAG,cAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,eAAe,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,SAAS,EAAE,CAAC,CAAC;QAEzG,IAAI,MAAM,kBAAE,CAAC,UAAU,CAAC,0BAA0B,CAAC,IAAI,MAAM,kBAAE,CAAC,UAAU,CAAC,wBAAwB,CAAC,EAAE;YACpG,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC;YACrC,CAAC,CAAC,qCAAqC,iBAAiB,GAAG,CAAC,CAAC;SAC9D;aAAM,IAAI,MAAM,kBAAE,CAAC,UAAU,CAAC,iBAAiB,CAAC,EAAE;YACjD,CAAC,CAAC,2BAA2B,iBAAiB,GAAG,CAAC,CAAC;SACpD;aAAM;YACL,CAAC,CAAC,qBAAqB,0BAA0B,OAAO,wBAAwB,UAAU,iBAAiB,GAAG,CAAC,CAAC;YAChH,OAAO,KAAK,CAAC;SACd;QAED,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AApCD,kCAoCC"}