{"name": "eslint-config-prettier", "version": "10.1.1", "type": "commonjs", "description": "Turns off all rules that are unnecessary or might conflict with <PERSON><PERSON><PERSON>.", "repository": "prettier/eslint-config-prettier", "author": "<PERSON>", "license": "MIT", "bin": "bin/cli.js", "main": "index.js", "exports": {".": {"types": "./index.d.ts", "default": "./index.js"}, "./flat": {"types": "./flat.d.ts", "default": "./flat.js"}, "./prettier": {"types": "./prettier.d.ts", "default": "./prettier.js"}, "./package.json": "./package.json"}, "types": "index.d.ts", "files": ["bin", "flat.d.ts", "flat.js", "index.d.ts", "index.js", "prettier.d.ts", "prettier.js"], "keywords": ["eslint", "eslintconfig", "eslint-config", "eslint-prettier", "prettier"], "peerDependencies": {"eslint": ">=7.0.0"}}