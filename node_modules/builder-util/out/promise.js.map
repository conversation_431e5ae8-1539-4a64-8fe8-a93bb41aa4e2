{"version": 3, "file": "promise.js", "sourceRoot": "", "sources": ["../src/promise.ts"], "names": [], "mappings": ";;;AAEA,8CAGC;AAGD,wCAgBC;AAcD,oDAEC;AAED,4CAOC;AAjDD,+BAA8B;AAE9B,SAAgB,iBAAiB,CAAC,KAAY;IAC5C,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAA;IAC3D,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;AACjB,CAAC;AAED,6FAA6F;AACtF,KAAK,UAAU,cAAc,CAAI,OAAmB,EAAE,IAAgD;IAC3G,IAAI,MAAM,GAAa,IAAI,CAAA;IAC3B,IAAI,CAAC;QACH,MAAM,GAAG,MAAM,OAAO,CAAA;IACxB,CAAC;IAAC,OAAO,aAAkB,EAAE,CAAC;QAC5B,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,IAAI,CAAC,CAAA;QAClB,CAAC;QAAC,OAAO,SAAc,EAAE,CAAC;YACxB,MAAM,IAAI,WAAW,CAAC,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC,CAAA;QACnD,CAAC;QAED,MAAM,aAAa,CAAA;IACrB,CAAC;IAED,MAAM,IAAI,CAAC,KAAK,CAAC,CAAA;IACjB,OAAO,MAAM,CAAA;AACf,CAAC;AAED,MAAa,WAAY,SAAQ,KAAK;IACpC,YAAY,MAAoB,EAAE,OAAO,GAAG,kBAAkB;QAC5D,IAAI,CAAC,GAAG,OAAO,CAAA;QACf,IAAI,CAAC,GAAG,CAAC,CAAA;QACT,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,MAAM,MAAM,GAAG,UAAU,CAAC,EAAE,GAAG,CAAA;YAC/B,CAAC,IAAI,OAAO,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,KAAK,CAAC,KAAK,EAAE,CAAA;QACvD,CAAC;QACD,KAAK,CAAC,CAAC,CAAC,CAAA;IACV,CAAC;CACF;AAVD,kCAUC;AAED,SAAgB,oBAAoB,CAAI,OAAmB;IACzD,OAAO,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;AACxC,CAAC;AAED,SAAgB,gBAAgB,CAAI,OAAmB,EAAE,aAAgB;IACvE,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC,CAAM,EAAE,EAAE;QAC9B,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YAChD,OAAO,aAAa,CAAA;QACtB,CAAC;QACD,MAAM,CAAC,CAAA;IACT,CAAC,CAAC,CAAA;AACJ,CAAC", "sourcesContent": ["import * as chalk from \"chalk\"\n\nexport function printErrorAndExit(error: Error) {\n  console.error(chalk.red((error.stack || error).toString()))\n  process.exit(1)\n}\n\n// you don't need to handle error in your task - it is passed only indicate status of promise\nexport async function executeFinally<T>(promise: Promise<T>, task: (isErrorOccurred: boolean) => Promise<any>): Promise<T> {\n  let result: T | null = null\n  try {\n    result = await promise\n  } catch (originalError: any) {\n    try {\n      await task(true)\n    } catch (taskError: any) {\n      throw new NestedError([originalError, taskError])\n    }\n\n    throw originalError\n  }\n\n  await task(false)\n  return result\n}\n\nexport class NestedError extends Error {\n  constructor(errors: Array<Error>, message = \"Compound error: \") {\n    let m = message\n    let i = 1\n    for (const error of errors) {\n      const prefix = `Error #${i++} `\n      m += `\\n\\n${prefix}${\"-\".repeat(80)}\\n${error.stack}`\n    }\n    super(m)\n  }\n}\n\nexport function orNullIfFileNotExist<T>(promise: Promise<T>): Promise<T | null> {\n  return orIfFileNotExist(promise, null)\n}\n\nexport function orIfFileNotExist<T>(promise: Promise<T>, fallbackValue: T): Promise<T> {\n  return promise.catch((e: any) => {\n    if (e.code === \"ENOENT\" || e.code === \"ENOTDIR\") {\n      return fallbackValue\n    }\n    throw e\n  })\n}\n"]}