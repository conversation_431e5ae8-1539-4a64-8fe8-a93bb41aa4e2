"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getPath7za = getPath7za;
exports.getPath7x = getPath7x;
const _7zip_bin_1 = require("7zip-bin");
const fs_extra_1 = require("fs-extra");
const fs = require("fs");
async function getPath7za() {
    if (fs.existsSync(_7zip_bin_1.path7za)) {
        await (0, fs_extra_1.chmod)(_7zip_bin_1.path7za, 0o755);
    }
    return _7zip_bin_1.path7za;
}
async function getPath7x() {
    await (0, fs_extra_1.chmod)(_7zip_bin_1.path7x, 0o755);
    return _7zip_bin_1.path7x;
}
//# sourceMappingURL=7za.js.map