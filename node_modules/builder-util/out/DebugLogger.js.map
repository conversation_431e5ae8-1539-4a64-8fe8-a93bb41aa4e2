{"version": 3, "file": "DebugLogger.js", "sourceRoot": "", "sources": ["../src/DebugLogger.ts"], "names": [], "mappings": ";;;AAAA,uCAAqC;AACrC,iCAAwC;AAExC,MAAa,WAAW;IAGtB,YAAqB,YAAY,IAAI;QAAhB,cAAS,GAAT,SAAS,CAAO;QAF5B,SAAI,GAAQ,EAAE,CAAA;IAEiB,CAAC;IAEzC,GAAG,CAAC,GAAW,EAAE,KAAU;QACzB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,OAAM;QACR,CAAC;QAED,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAC/B,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAA;QACjB,IAAI,QAAQ,GAAkB,IAAI,CAAA;QAClC,KAAK,MAAM,CAAC,IAAI,QAAQ,EAAE,CAAC;YACzB,IAAI,CAAC,KAAK,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC;gBACxC,QAAQ,GAAG,CAAC,CAAA;gBACZ,MAAK;YACP,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;oBACjB,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;gBAC5B,CAAC;qBAAM,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE,CAAC;oBACpC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;gBACf,CAAC;gBACD,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;YACV,CAAC;QACH,CAAC;QAED,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,QAAS,CAAC,CAAC,EAAE,CAAC;YAChC,CAAC,CAAC,QAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAS,CAAC,EAAE,KAAK,CAAC,CAAA;QACzC,CAAC;aAAM,CAAC;YACN,CAAC,CAAC,QAAS,CAAC,GAAG,KAAK,CAAA;QACtB,CAAC;IACH,CAAC;IAED,IAAI,CAAC,IAAY;QACf,uEAAuE;QACvE,IAAI,IAAI,CAAC,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxD,OAAO,IAAA,qBAAU,EAAC,IAAI,EAAE,IAAA,sBAAe,EAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;QACrD,CAAC;aAAM,CAAC;YACN,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;QAC1B,CAAC;IACH,CAAC;CACF;AA1CD,kCA0CC", "sourcesContent": ["import { outputFile } from \"fs-extra\"\nimport { serializeToYaml } from \"./util\"\n\nexport class DebugLogger {\n  readonly data: any = {}\n\n  constructor(readonly isEnabled = true) {}\n\n  add(key: string, value: any) {\n    if (!this.isEnabled) {\n      return\n    }\n\n    const dataPath = key.split(\".\")\n    let o = this.data\n    let lastName: string | null = null\n    for (const p of dataPath) {\n      if (p === dataPath[dataPath.length - 1]) {\n        lastName = p\n        break\n      } else {\n        if (o[p] == null) {\n          o[p] = Object.create(null)\n        } else if (typeof o[p] === \"string\") {\n          o[p] = [o[p]]\n        }\n        o = o[p]\n      }\n    }\n\n    if (Array.isArray(o[lastName!])) {\n      o[lastName!] = [...o[lastName!], value]\n    } else {\n      o[lastName!] = value\n    }\n  }\n\n  save(file: string) {\n    // toml and json doesn't correctly output multiline string as multiline\n    if (this.isEnabled && Object.keys(this.data).length > 0) {\n      return outputFile(file, serializeToYaml(this.data))\n    } else {\n      return Promise.resolve()\n    }\n  }\n}\n"]}