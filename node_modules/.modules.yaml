hoistPattern:
  - '*'
hoistedLocations:
  7zip-bin@5.2.0:
    - node_modules/7zip-bin
  '@ampproject/remapping@2.3.0':
    - node_modules/@ampproject/remapping
  '@babel/code-frame@7.26.2':
    - node_modules/@babel/code-frame
  '@babel/compat-data@7.26.8':
    - node_modules/@babel/compat-data
  '@babel/core@7.26.10':
    - node_modules/@babel/core
  '@babel/generator@7.27.0':
    - node_modules/@babel/generator
  '@babel/helper-compilation-targets@7.27.0':
    - node_modules/@babel/helper-compilation-targets
  '@babel/helper-module-imports@7.25.9':
    - node_modules/@babel/helper-module-imports
  '@babel/helper-module-transforms@7.26.0(@babel/core@7.26.10)':
    - node_modules/@babel/helper-module-transforms
  '@babel/helper-plugin-utils@7.26.5':
    - node_modules/@babel/helper-plugin-utils
  '@babel/helper-string-parser@7.25.9':
    - node_modules/@babel/helper-string-parser
  '@babel/helper-validator-identifier@7.25.9':
    - node_modules/@babel/helper-validator-identifier
  '@babel/helper-validator-option@7.25.9':
    - node_modules/@babel/helper-validator-option
  '@babel/helpers@7.27.0':
    - node_modules/@babel/helpers
  '@babel/parser@7.27.0':
    - node_modules/@babel/parser
  '@babel/plugin-transform-arrow-functions@7.25.9(@babel/core@7.26.10)':
    - node_modules/@babel/plugin-transform-arrow-functions
  '@babel/plugin-transform-react-jsx-self@7.25.9(@babel/core@7.26.10)':
    - node_modules/@babel/plugin-transform-react-jsx-self
  '@babel/plugin-transform-react-jsx-source@7.25.9(@babel/core@7.26.10)':
    - node_modules/@babel/plugin-transform-react-jsx-source
  '@babel/template@7.27.0':
    - node_modules/@babel/template
  '@babel/traverse@7.27.0':
    - node_modules/@babel/traverse
  '@babel/types@7.27.0':
    - node_modules/@babel/types
  '@develar/schema-utils@2.6.5':
    - node_modules/@develar/schema-utils
  '@electron-toolkit/eslint-config-prettier@3.0.0(eslint@9.23.0)(prettier@3.5.3)':
    - node_modules/@electron-toolkit/eslint-config-prettier
  '@electron-toolkit/eslint-config@2.0.0(eslint@9.23.0)':
    - node_modules/@electron-toolkit/eslint-config
  '@electron-toolkit/preload@3.0.1(electron@35.1.2)':
    - node_modules/@electron-toolkit/preload
  '@electron-toolkit/utils@4.0.0(electron@35.1.2)':
    - node_modules/@electron-toolkit/utils
  '@electron/asar@3.4.0':
    - node_modules/@electron/asar
  '@electron/get@2.0.3':
    - node_modules/@electron/get
  '@electron/notarize@2.5.0':
    - node_modules/@electron/notarize
  '@electron/osx-sign@1.3.1':
    - node_modules/@electron/osx-sign
  '@electron/rebuild@3.6.1':
    - node_modules/@electron/rebuild
  '@electron/universal@2.0.1':
    - node_modules/@electron/universal
  '@esbuild/darwin-arm64@0.25.2':
    - node_modules/@esbuild/darwin-arm64
  '@eslint-community/eslint-utils@4.5.1(eslint@9.23.0)':
    - node_modules/@eslint-community/eslint-utils
  '@eslint-community/regexpp@4.12.1':
    - node_modules/@eslint-community/regexpp
  '@eslint/config-array@0.19.2':
    - node_modules/@eslint/config-array
  '@eslint/config-helpers@0.2.1':
    - node_modules/@eslint/config-helpers
  '@eslint/core@0.12.0':
    - node_modules/@eslint/core
  '@eslint/core@0.13.0':
    - node_modules/@eslint/plugin-kit/node_modules/@eslint/core
  '@eslint/eslintrc@3.3.1':
    - node_modules/@eslint/eslintrc
  '@eslint/js@9.23.0':
    - node_modules/@eslint/js
  '@eslint/object-schema@2.1.6':
    - node_modules/@eslint/object-schema
  '@eslint/plugin-kit@0.2.8':
    - node_modules/@eslint/plugin-kit
  '@gar/promisify@1.1.3':
    - node_modules/@gar/promisify
  '@humanfs/core@0.19.1':
    - node_modules/@humanfs/core
  '@humanfs/node@0.16.6':
    - node_modules/@humanfs/node
  '@humanwhocodes/module-importer@1.0.1':
    - node_modules/@humanwhocodes/module-importer
  '@humanwhocodes/retry@0.3.1':
    - node_modules/@humanwhocodes/retry
  '@humanwhocodes/retry@0.4.2':
    - node_modules/eslint/node_modules/@humanwhocodes/retry
  '@isaacs/cliui@8.0.2':
    - node_modules/@isaacs/cliui
  '@jridgewell/gen-mapping@0.3.8':
    - node_modules/@jridgewell/gen-mapping
  '@jridgewell/resolve-uri@3.1.2':
    - node_modules/@jridgewell/resolve-uri
  '@jridgewell/set-array@1.2.1':
    - node_modules/@jridgewell/set-array
  '@jridgewell/sourcemap-codec@1.5.0':
    - node_modules/@jridgewell/sourcemap-codec
  '@jridgewell/trace-mapping@0.3.25':
    - node_modules/@jridgewell/trace-mapping
  '@malept/cross-spawn-promise@2.0.0':
    - node_modules/@malept/cross-spawn-promise
  '@malept/flatpak-bundler@0.4.0':
    - node_modules/@malept/flatpak-bundler
  '@npmcli/fs@2.1.2':
    - node_modules/@npmcli/fs
  '@npmcli/move-file@2.0.1':
    - node_modules/@npmcli/move-file
  '@pkgjs/parseargs@0.11.0':
    - node_modules/@pkgjs/parseargs
  '@pkgr/core@0.2.0':
    - node_modules/@pkgr/core
  '@rollup/rollup-darwin-arm64@4.39.0':
    - node_modules/@rollup/rollup-darwin-arm64
  '@sindresorhus/is@4.6.0':
    - node_modules/@sindresorhus/is
  '@szmarczak/http-timer@4.0.6':
    - node_modules/@szmarczak/http-timer
  '@tootallnate/once@2.0.0':
    - node_modules/@tootallnate/once
  '@types/babel__core@7.20.5':
    - node_modules/@types/babel__core
  '@types/babel__generator@7.6.8':
    - node_modules/@types/babel__generator
  '@types/babel__template@7.4.4':
    - node_modules/@types/babel__template
  '@types/babel__traverse@7.20.7':
    - node_modules/@types/babel__traverse
  '@types/cacheable-request@6.0.3':
    - node_modules/@types/cacheable-request
  '@types/debug@4.1.12':
    - node_modules/@types/debug
  '@types/estree-jsx@1.0.5':
    - node_modules/@types/estree-jsx
  '@types/estree@1.0.7':
    - node_modules/@types/estree
  '@types/fs-extra@9.0.13':
    - node_modules/@types/fs-extra
  '@types/hast@3.0.4':
    - node_modules/@types/hast
  '@types/http-cache-semantics@4.0.4':
    - node_modules/@types/http-cache-semantics
  '@types/json-schema@7.0.15':
    - node_modules/@types/json-schema
  '@types/keyv@3.1.4':
    - node_modules/@types/keyv
  '@types/mdast@4.0.4':
    - node_modules/@types/mdast
  '@types/ms@2.1.0':
    - node_modules/@types/ms
  '@types/node@22.13.17':
    - node_modules/@types/node
  '@types/plist@3.0.5':
    - node_modules/@types/plist
  '@types/react@19.1.0':
    - node_modules/@types/react
  '@types/responselike@1.0.3':
    - node_modules/@types/responselike
  '@types/unist@2.0.11':
    - node_modules/parse-entities/node_modules/@types/unist
  '@types/unist@3.0.3':
    - node_modules/@types/unist
  '@types/verror@1.10.11':
    - node_modules/@types/verror
  '@types/yauzl@2.10.3':
    - node_modules/@types/yauzl
  '@ungap/structured-clone@1.3.0':
    - node_modules/@ungap/structured-clone
  '@vitejs/plugin-react@4.3.4(vite@6.2.4(@types/node@22.13.17))':
    - node_modules/@vitejs/plugin-react
  '@xmldom/xmldom@0.8.10':
    - node_modules/@xmldom/xmldom
  abbrev@1.1.1:
    - node_modules/abbrev
  acorn-jsx@5.3.2(acorn@8.14.1):
    - node_modules/acorn-jsx
  acorn@8.14.1:
    - node_modules/acorn
  agent-base@6.0.2:
    - node_modules/agent-base
  agent-base@7.1.3:
    - node_modules/https-proxy-agent/node_modules/agent-base
    - node_modules/http-proxy-agent/node_modules/agent-base
  agentkeepalive@4.6.0:
    - node_modules/agentkeepalive
  aggregate-error@3.1.0:
    - node_modules/aggregate-error
  ajv-keywords@3.5.2(ajv@6.12.6):
    - node_modules/ajv-keywords
  ajv@6.12.6:
    - node_modules/ajv
  ansi-regex@5.0.1:
    - node_modules/ansi-regex
  ansi-regex@6.1.0:
    - node_modules/@isaacs/cliui/node_modules/ansi-regex
  ansi-styles@4.3.0:
    - node_modules/ansi-styles
  ansi-styles@6.2.1:
    - node_modules/@isaacs/cliui/node_modules/ansi-styles
  app-builder-bin@5.0.0-alpha.10:
    - node_modules/app-builder-bin
  app-builder-lib@25.1.8(dmg-builder@25.1.8)(electron-builder-squirrel-windows@25.1.8):
    - node_modules/app-builder-lib
  aproba@2.0.0:
    - node_modules/aproba
  archiver-utils@2.1.0:
    - node_modules/archiver-utils
  archiver-utils@3.0.4:
    - node_modules/zip-stream/node_modules/archiver-utils
  archiver@5.3.2:
    - node_modules/archiver
  are-we-there-yet@3.0.1:
    - node_modules/are-we-there-yet
  argparse@2.0.1:
    - node_modules/argparse
  array-buffer-byte-length@1.0.2:
    - node_modules/array-buffer-byte-length
  array-includes@3.1.8:
    - node_modules/array-includes
  array.prototype.findlast@1.2.5:
    - node_modules/array.prototype.findlast
  array.prototype.flat@1.3.3:
    - node_modules/array.prototype.flat
  array.prototype.flatmap@1.3.3:
    - node_modules/array.prototype.flatmap
  array.prototype.tosorted@1.1.4:
    - node_modules/array.prototype.tosorted
  arraybuffer.prototype.slice@1.0.4:
    - node_modules/arraybuffer.prototype.slice
  assert-plus@1.0.0:
    - node_modules/assert-plus
  astral-regex@2.0.0:
    - node_modules/astral-regex
  async-exit-hook@2.0.1:
    - node_modules/async-exit-hook
  async-function@1.0.0:
    - node_modules/async-function
  async@3.2.6:
    - node_modules/async
  asynckit@0.4.0:
    - node_modules/asynckit
  at-least-node@1.0.0:
    - node_modules/at-least-node
  available-typed-arrays@1.0.7:
    - node_modules/available-typed-arrays
  bail@2.0.2:
    - node_modules/bail
  balanced-match@1.0.2:
    - node_modules/balanced-match
  base64-js@1.5.1:
    - node_modules/base64-js
  bl@4.1.0:
    - node_modules/bl
  bluebird-lst@1.0.9:
    - node_modules/bluebird-lst
  bluebird@3.7.2:
    - node_modules/bluebird
  boolean@3.2.0:
    - node_modules/boolean
  brace-expansion@1.1.11:
    - node_modules/minimatch/node_modules/brace-expansion
  brace-expansion@2.0.1:
    - node_modules/brace-expansion
  browserslist@4.24.4:
    - node_modules/browserslist
  buffer-crc32@0.2.13:
    - node_modules/buffer-crc32
  buffer-from@1.1.2:
    - node_modules/buffer-from
  buffer@5.7.1:
    - node_modules/buffer
  builder-util-runtime@9.2.10:
    - node_modules/builder-util-runtime
  builder-util-runtime@9.3.1:
    - node_modules/electron-updater/node_modules/builder-util-runtime
  builder-util@25.1.7:
    - node_modules/builder-util
  cac@6.7.14:
    - node_modules/cac
  cacache@16.1.3:
    - node_modules/cacache
  cacheable-lookup@5.0.4:
    - node_modules/cacheable-lookup
  cacheable-request@7.0.4:
    - node_modules/cacheable-request
  call-bind-apply-helpers@1.0.2:
    - node_modules/call-bind-apply-helpers
  call-bind@1.0.8:
    - node_modules/call-bind
  call-bound@1.0.4:
    - node_modules/call-bound
  callsites@3.1.0:
    - node_modules/callsites
  caniuse-lite@1.0.30001707:
    - node_modules/caniuse-lite
  ccount@2.0.1:
    - node_modules/ccount
  chalk@4.1.2:
    - node_modules/chalk
  character-entities-html4@2.1.0:
    - node_modules/character-entities-html4
  character-entities-legacy@3.0.0:
    - node_modules/character-entities-legacy
  character-entities@2.0.2:
    - node_modules/character-entities
  character-reference-invalid@2.0.1:
    - node_modules/character-reference-invalid
  chownr@2.0.0:
    - node_modules/chownr
  chromium-pickle-js@0.2.0:
    - node_modules/chromium-pickle-js
  ci-info@3.9.0:
    - node_modules/ci-info
  clean-stack@2.2.0:
    - node_modules/clean-stack
  cli-cursor@3.1.0:
    - node_modules/cli-cursor
  cli-spinners@2.9.2:
    - node_modules/cli-spinners
  cli-truncate@2.1.0:
    - node_modules/cli-truncate
  cliui@8.0.1:
    - node_modules/cliui
  clone-response@1.0.3:
    - node_modules/clone-response
  clone@1.0.4:
    - node_modules/clone
  color-convert@2.0.1:
    - node_modules/color-convert
  color-name@1.1.4:
    - node_modules/color-name
  color-support@1.1.3:
    - node_modules/color-support
  combined-stream@1.0.8:
    - node_modules/combined-stream
  comma-separated-tokens@2.0.3:
    - node_modules/comma-separated-tokens
  commander@5.1.0:
    - node_modules/commander
  compare-version@0.1.2:
    - node_modules/compare-version
  compress-commons@4.1.2:
    - node_modules/compress-commons
  concat-map@0.0.1:
    - node_modules/concat-map
  config-file-ts@0.2.8-rc1:
    - node_modules/config-file-ts
  console-control-strings@1.1.0:
    - node_modules/console-control-strings
  convert-source-map@2.0.0:
    - node_modules/convert-source-map
  core-util-is@1.0.2:
    - node_modules/verror/node_modules/core-util-is
  core-util-is@1.0.3:
    - node_modules/core-util-is
  crc-32@1.2.2:
    - node_modules/crc-32
  crc32-stream@4.0.3:
    - node_modules/crc32-stream
  crc@3.8.0:
    - node_modules/crc
  cross-spawn@7.0.6:
    - node_modules/cross-spawn
  csstype@3.1.3:
    - node_modules/csstype
  data-view-buffer@1.0.2:
    - node_modules/data-view-buffer
  data-view-byte-length@1.0.2:
    - node_modules/data-view-byte-length
  data-view-byte-offset@1.0.1:
    - node_modules/data-view-byte-offset
  debug@4.4.0:
    - node_modules/debug
  decode-named-character-reference@1.1.0:
    - node_modules/decode-named-character-reference
  decompress-response@6.0.0:
    - node_modules/decompress-response
  deep-is@0.1.4:
    - node_modules/deep-is
  defaults@1.0.4:
    - node_modules/defaults
  defer-to-connect@2.0.1:
    - node_modules/defer-to-connect
  define-data-property@1.1.4:
    - node_modules/define-data-property
  define-properties@1.2.1:
    - node_modules/define-properties
  delayed-stream@1.0.0:
    - node_modules/delayed-stream
  delegates@1.0.0:
    - node_modules/delegates
  dequal@2.0.3:
    - node_modules/dequal
  detect-libc@2.0.3:
    - node_modules/detect-libc
  detect-node@2.1.0:
    - node_modules/detect-node
  devlop@1.1.0:
    - node_modules/devlop
  dir-compare@4.2.0:
    - node_modules/dir-compare
  dmg-builder@25.1.8(electron-builder-squirrel-windows@25.1.8):
    - node_modules/dmg-builder
  dmg-license@1.0.11:
    - node_modules/dmg-license
  doctrine@2.1.0:
    - node_modules/doctrine
  dotenv-expand@11.0.7:
    - node_modules/dotenv-expand
  dotenv@16.4.7:
    - node_modules/dotenv
  dunder-proto@1.0.1:
    - node_modules/dunder-proto
  eastasianwidth@0.2.0:
    - node_modules/eastasianwidth
  ejs@3.1.10:
    - node_modules/ejs
  electron-builder-squirrel-windows@25.1.8(dmg-builder@25.1.8):
    - node_modules/electron-builder-squirrel-windows
  electron-builder@25.1.8(electron-builder-squirrel-windows@25.1.8):
    - node_modules/electron-builder
  electron-publish@25.1.7:
    - node_modules/electron-publish
  electron-to-chromium@1.5.129:
    - node_modules/electron-to-chromium
  electron-updater@6.6.2:
    - node_modules/electron-updater
  electron-vite@3.1.0(vite@6.2.4(@types/node@22.13.17)):
    - node_modules/electron-vite
  electron@35.1.2:
    - node_modules/electron
  emoji-regex@8.0.0:
    - node_modules/emoji-regex
  emoji-regex@9.2.2:
    - node_modules/@isaacs/cliui/node_modules/emoji-regex
  encoding@0.1.13:
    - node_modules/encoding
  end-of-stream@1.4.4:
    - node_modules/end-of-stream
  env-paths@2.2.1:
    - node_modules/env-paths
  err-code@2.0.3:
    - node_modules/err-code
  es-abstract@1.23.9:
    - node_modules/es-abstract
  es-define-property@1.0.1:
    - node_modules/es-define-property
  es-errors@1.3.0:
    - node_modules/es-errors
  es-iterator-helpers@1.2.1:
    - node_modules/es-iterator-helpers
  es-object-atoms@1.1.1:
    - node_modules/es-object-atoms
  es-set-tostringtag@2.1.0:
    - node_modules/es-set-tostringtag
  es-shim-unscopables@1.1.0:
    - node_modules/es-shim-unscopables
  es-to-primitive@1.3.0:
    - node_modules/es-to-primitive
  es6-error@4.1.1:
    - node_modules/es6-error
  esbuild@0.25.2:
    - node_modules/esbuild
  escalade@3.2.0:
    - node_modules/escalade
  escape-string-regexp@4.0.0:
    - node_modules/escape-string-regexp
  escape-string-regexp@5.0.0:
    - node_modules/mdast-util-find-and-replace/node_modules/escape-string-regexp
  eslint-config-prettier@10.1.1(eslint@9.23.0):
    - node_modules/eslint-config-prettier
  eslint-plugin-prettier@5.2.5(eslint-config-prettier@10.1.1(eslint@9.23.0))(eslint@9.23.0)(prettier@3.5.3):
    - node_modules/eslint-plugin-prettier
  eslint-plugin-react-hooks@5.2.0(eslint@9.23.0):
    - node_modules/eslint-plugin-react-hooks
  eslint-plugin-react-refresh@0.4.19(eslint@9.23.0):
    - node_modules/eslint-plugin-react-refresh
  eslint-plugin-react@7.37.4(eslint@9.23.0):
    - node_modules/eslint-plugin-react
  eslint-scope@8.3.0:
    - node_modules/eslint-scope
  eslint-visitor-keys@3.4.3:
    - node_modules/@eslint-community/eslint-utils/node_modules/eslint-visitor-keys
  eslint-visitor-keys@4.2.0:
    - node_modules/eslint-visitor-keys
  eslint@9.23.0:
    - node_modules/eslint
  espree@10.3.0:
    - node_modules/espree
  esquery@1.6.0:
    - node_modules/esquery
  esrecurse@4.3.0:
    - node_modules/esrecurse
  estraverse@5.3.0:
    - node_modules/estraverse
  estree-util-is-identifier-name@3.0.0:
    - node_modules/estree-util-is-identifier-name
  esutils@2.0.3:
    - node_modules/esutils
  exponential-backoff@3.1.2:
    - node_modules/exponential-backoff
  extend@3.0.2:
    - node_modules/extend
  extract-zip@2.0.1:
    - node_modules/extract-zip
  extsprintf@1.4.1:
    - node_modules/extsprintf
  fast-deep-equal@3.1.3:
    - node_modules/fast-deep-equal
  fast-diff@1.3.0:
    - node_modules/fast-diff
  fast-json-stable-stringify@2.1.0:
    - node_modules/fast-json-stable-stringify
  fast-levenshtein@2.0.6:
    - node_modules/fast-levenshtein
  fd-slicer@1.1.0:
    - node_modules/fd-slicer
  file-entry-cache@8.0.0:
    - node_modules/file-entry-cache
  filelist@1.0.4:
    - node_modules/filelist
  find-up@5.0.0:
    - node_modules/find-up
  flat-cache@4.0.1:
    - node_modules/flat-cache
  flatted@3.3.3:
    - node_modules/flatted
  for-each@0.3.5:
    - node_modules/for-each
  foreground-child@3.3.1:
    - node_modules/foreground-child
  form-data@4.0.2:
    - node_modules/form-data
  fs-constants@1.0.0:
    - node_modules/fs-constants
  fs-extra@10.1.0:
    - node_modules/fs-extra
  fs-extra@11.3.0:
    - node_modules/@electron/universal/node_modules/fs-extra
  fs-extra@8.1.0:
    - node_modules/@electron/get/node_modules/fs-extra
  fs-extra@9.1.0:
    - node_modules/@malept/flatpak-bundler/node_modules/fs-extra
    - node_modules/@electron/notarize/node_modules/fs-extra
  fs-minipass@2.1.0:
    - node_modules/fs-minipass
  fs.realpath@1.0.0:
    - node_modules/fs.realpath
  fsevents@2.3.3:
    - node_modules/fsevents
  function-bind@1.1.2:
    - node_modules/function-bind
  function.prototype.name@1.1.8:
    - node_modules/function.prototype.name
  functions-have-names@1.2.3:
    - node_modules/functions-have-names
  gauge@4.0.4:
    - node_modules/gauge
  gensync@1.0.0-beta.2:
    - node_modules/gensync
  get-caller-file@2.0.5:
    - node_modules/get-caller-file
  get-intrinsic@1.3.0:
    - node_modules/get-intrinsic
  get-proto@1.0.1:
    - node_modules/get-proto
  get-stream@5.2.0:
    - node_modules/get-stream
  get-symbol-description@1.1.0:
    - node_modules/get-symbol-description
  glob-parent@6.0.2:
    - node_modules/glob-parent
  glob@10.4.5:
    - node_modules/config-file-ts/node_modules/glob
  glob@7.2.3:
    - node_modules/glob
  glob@8.1.0:
    - node_modules/cacache/node_modules/glob
  global-agent@3.0.0:
    - node_modules/global-agent
  globals@11.12.0:
    - node_modules/@babel/traverse/node_modules/globals
  globals@14.0.0:
    - node_modules/@eslint/eslintrc/node_modules/globals
  globals@15.15.0:
    - node_modules/globals
  globalthis@1.0.4:
    - node_modules/globalthis
  gopd@1.2.0:
    - node_modules/gopd
  got@11.8.6:
    - node_modules/got
  graceful-fs@4.2.11:
    - node_modules/graceful-fs
  has-bigints@1.1.0:
    - node_modules/has-bigints
  has-flag@4.0.0:
    - node_modules/has-flag
  has-property-descriptors@1.0.2:
    - node_modules/has-property-descriptors
  has-proto@1.2.0:
    - node_modules/has-proto
  has-symbols@1.1.0:
    - node_modules/has-symbols
  has-tostringtag@1.0.2:
    - node_modules/has-tostringtag
  has-unicode@2.0.1:
    - node_modules/has-unicode
  hasown@2.0.2:
    - node_modules/hasown
  hast-util-to-jsx-runtime@2.3.6:
    - node_modules/hast-util-to-jsx-runtime
  hast-util-whitespace@3.0.0:
    - node_modules/hast-util-whitespace
  hosted-git-info@4.1.0:
    - node_modules/hosted-git-info
  html-url-attributes@3.0.1:
    - node_modules/html-url-attributes
  http-cache-semantics@4.1.1:
    - node_modules/http-cache-semantics
  http-proxy-agent@5.0.0:
    - node_modules/make-fetch-happen/node_modules/http-proxy-agent
  http-proxy-agent@7.0.2:
    - node_modules/http-proxy-agent
  http2-wrapper@1.0.3:
    - node_modules/http2-wrapper
  https-proxy-agent@5.0.1:
    - node_modules/make-fetch-happen/node_modules/https-proxy-agent
  https-proxy-agent@7.0.6:
    - node_modules/https-proxy-agent
  humanize-ms@1.2.1:
    - node_modules/humanize-ms
  iconv-corefoundation@1.1.7:
    - node_modules/iconv-corefoundation
  iconv-lite@0.6.3:
    - node_modules/iconv-lite
  ieee754@1.2.1:
    - node_modules/ieee754
  ignore@5.3.2:
    - node_modules/ignore
  import-fresh@3.3.1:
    - node_modules/import-fresh
  imurmurhash@0.1.4:
    - node_modules/imurmurhash
  indent-string@4.0.0:
    - node_modules/indent-string
  infer-owner@1.0.4:
    - node_modules/infer-owner
  inflight@1.0.6:
    - node_modules/inflight
  inherits@2.0.4:
    - node_modules/inherits
  inline-style-parser@0.2.4:
    - node_modules/inline-style-parser
  internal-slot@1.1.0:
    - node_modules/internal-slot
  ip-address@9.0.5:
    - node_modules/ip-address
  is-alphabetical@2.0.1:
    - node_modules/is-alphabetical
  is-alphanumerical@2.0.1:
    - node_modules/is-alphanumerical
  is-array-buffer@3.0.5:
    - node_modules/is-array-buffer
  is-async-function@2.1.1:
    - node_modules/is-async-function
  is-bigint@1.1.0:
    - node_modules/is-bigint
  is-boolean-object@1.2.2:
    - node_modules/is-boolean-object
  is-callable@1.2.7:
    - node_modules/is-callable
  is-ci@3.0.1:
    - node_modules/is-ci
  is-core-module@2.16.1:
    - node_modules/is-core-module
  is-data-view@1.0.2:
    - node_modules/is-data-view
  is-date-object@1.1.0:
    - node_modules/is-date-object
  is-decimal@2.0.1:
    - node_modules/is-decimal
  is-extglob@2.1.1:
    - node_modules/is-extglob
  is-finalizationregistry@1.1.1:
    - node_modules/is-finalizationregistry
  is-fullwidth-code-point@3.0.0:
    - node_modules/is-fullwidth-code-point
  is-generator-function@1.1.0:
    - node_modules/is-generator-function
  is-glob@4.0.3:
    - node_modules/is-glob
  is-hexadecimal@2.0.1:
    - node_modules/is-hexadecimal
  is-interactive@1.0.0:
    - node_modules/is-interactive
  is-lambda@1.0.1:
    - node_modules/is-lambda
  is-map@2.0.3:
    - node_modules/is-map
  is-number-object@1.1.1:
    - node_modules/is-number-object
  is-plain-obj@4.1.0:
    - node_modules/is-plain-obj
  is-regex@1.2.1:
    - node_modules/is-regex
  is-set@2.0.3:
    - node_modules/is-set
  is-shared-array-buffer@1.0.4:
    - node_modules/is-shared-array-buffer
  is-string@1.1.1:
    - node_modules/is-string
  is-symbol@1.1.1:
    - node_modules/is-symbol
  is-typed-array@1.1.15:
    - node_modules/is-typed-array
  is-unicode-supported@0.1.0:
    - node_modules/is-unicode-supported
  is-weakmap@2.0.2:
    - node_modules/is-weakmap
  is-weakref@1.1.1:
    - node_modules/is-weakref
  is-weakset@2.0.4:
    - node_modules/is-weakset
  isarray@1.0.0:
    - node_modules/archiver-utils/node_modules/isarray
    - node_modules/lazystream/node_modules/isarray
  isarray@2.0.5:
    - node_modules/isarray
  isbinaryfile@4.0.10:
    - node_modules/@electron/osx-sign/node_modules/isbinaryfile
  isbinaryfile@5.0.4:
    - node_modules/isbinaryfile
  isexe@2.0.0:
    - node_modules/isexe
  iterator.prototype@1.1.5:
    - node_modules/iterator.prototype
  jackspeak@3.4.3:
    - node_modules/jackspeak
  jake@10.9.2:
    - node_modules/jake
  js-tokens@4.0.0:
    - node_modules/js-tokens
  js-yaml@4.1.0:
    - node_modules/js-yaml
  jsbn@1.1.0:
    - node_modules/jsbn
  jsesc@3.1.0:
    - node_modules/jsesc
  json-buffer@3.0.1:
    - node_modules/json-buffer
  json-schema-traverse@0.4.1:
    - node_modules/json-schema-traverse
  json-stable-stringify-without-jsonify@1.0.1:
    - node_modules/json-stable-stringify-without-jsonify
  json-stringify-safe@5.0.1:
    - node_modules/json-stringify-safe
  json5@2.2.3:
    - node_modules/json5
  jsonfile@4.0.0:
    - node_modules/@electron/get/node_modules/jsonfile
  jsonfile@6.1.0:
    - node_modules/jsonfile
  jsx-ast-utils@3.3.5:
    - node_modules/jsx-ast-utils
  keyv@4.5.4:
    - node_modules/keyv
  lazy-val@1.0.5:
    - node_modules/lazy-val
  lazystream@1.0.1:
    - node_modules/lazystream
  levn@0.4.1:
    - node_modules/levn
  locate-path@6.0.0:
    - node_modules/locate-path
  lodash.defaults@4.2.0:
    - node_modules/lodash.defaults
  lodash.difference@4.5.0:
    - node_modules/lodash.difference
  lodash.escaperegexp@4.1.2:
    - node_modules/lodash.escaperegexp
  lodash.flatten@4.4.0:
    - node_modules/lodash.flatten
  lodash.isequal@4.5.0:
    - node_modules/lodash.isequal
  lodash.isplainobject@4.0.6:
    - node_modules/lodash.isplainobject
  lodash.merge@4.6.2:
    - node_modules/lodash.merge
  lodash.union@4.6.0:
    - node_modules/lodash.union
  lodash@4.17.21:
    - node_modules/lodash
  log-symbols@4.1.0:
    - node_modules/log-symbols
  longest-streak@3.1.0:
    - node_modules/longest-streak
  loose-envify@1.4.0:
    - node_modules/loose-envify
  lowercase-keys@2.0.0:
    - node_modules/lowercase-keys
  lru-cache@10.4.3:
    - node_modules/path-scurry/node_modules/lru-cache
  lru-cache@5.1.1:
    - node_modules/@babel/helper-compilation-targets/node_modules/lru-cache
  lru-cache@6.0.0:
    - node_modules/hosted-git-info/node_modules/lru-cache
  lru-cache@7.18.3:
    - node_modules/lru-cache
  magic-string@0.30.17:
    - node_modules/magic-string
  make-fetch-happen@10.2.1:
    - node_modules/make-fetch-happen
  markdown-table@3.0.4:
    - node_modules/markdown-table
  matcher@3.0.0:
    - node_modules/matcher
  math-intrinsics@1.1.0:
    - node_modules/math-intrinsics
  mdast-util-find-and-replace@3.0.2:
    - node_modules/mdast-util-find-and-replace
  mdast-util-from-markdown@2.0.2:
    - node_modules/mdast-util-from-markdown
  mdast-util-gfm-autolink-literal@2.0.1:
    - node_modules/mdast-util-gfm-autolink-literal
  mdast-util-gfm-footnote@2.1.0:
    - node_modules/mdast-util-gfm-footnote
  mdast-util-gfm-strikethrough@2.0.0:
    - node_modules/mdast-util-gfm-strikethrough
  mdast-util-gfm-table@2.0.0:
    - node_modules/mdast-util-gfm-table
  mdast-util-gfm-task-list-item@2.0.0:
    - node_modules/mdast-util-gfm-task-list-item
  mdast-util-gfm@3.1.0:
    - node_modules/mdast-util-gfm
  mdast-util-mdx-expression@2.0.1:
    - node_modules/mdast-util-mdx-expression
  mdast-util-mdx-jsx@3.2.0:
    - node_modules/mdast-util-mdx-jsx
  mdast-util-mdxjs-esm@2.0.1:
    - node_modules/mdast-util-mdxjs-esm
  mdast-util-phrasing@4.1.0:
    - node_modules/mdast-util-phrasing
  mdast-util-to-hast@13.2.0:
    - node_modules/mdast-util-to-hast
  mdast-util-to-markdown@2.1.2:
    - node_modules/mdast-util-to-markdown
  mdast-util-to-string@4.0.0:
    - node_modules/mdast-util-to-string
  micromark-core-commonmark@2.0.3:
    - node_modules/micromark-core-commonmark
  micromark-extension-gfm-autolink-literal@2.1.0:
    - node_modules/micromark-extension-gfm-autolink-literal
  micromark-extension-gfm-footnote@2.1.0:
    - node_modules/micromark-extension-gfm-footnote
  micromark-extension-gfm-strikethrough@2.1.0:
    - node_modules/micromark-extension-gfm-strikethrough
  micromark-extension-gfm-table@2.1.1:
    - node_modules/micromark-extension-gfm-table
  micromark-extension-gfm-tagfilter@2.0.0:
    - node_modules/micromark-extension-gfm-tagfilter
  micromark-extension-gfm-task-list-item@2.1.0:
    - node_modules/micromark-extension-gfm-task-list-item
  micromark-extension-gfm@3.0.0:
    - node_modules/micromark-extension-gfm
  micromark-factory-destination@2.0.1:
    - node_modules/micromark-factory-destination
  micromark-factory-label@2.0.1:
    - node_modules/micromark-factory-label
  micromark-factory-space@2.0.1:
    - node_modules/micromark-factory-space
  micromark-factory-title@2.0.1:
    - node_modules/micromark-factory-title
  micromark-factory-whitespace@2.0.1:
    - node_modules/micromark-factory-whitespace
  micromark-util-character@2.1.1:
    - node_modules/micromark-util-character
  micromark-util-chunked@2.0.1:
    - node_modules/micromark-util-chunked
  micromark-util-classify-character@2.0.1:
    - node_modules/micromark-util-classify-character
  micromark-util-combine-extensions@2.0.1:
    - node_modules/micromark-util-combine-extensions
  micromark-util-decode-numeric-character-reference@2.0.2:
    - node_modules/micromark-util-decode-numeric-character-reference
  micromark-util-decode-string@2.0.1:
    - node_modules/micromark-util-decode-string
  micromark-util-encode@2.0.1:
    - node_modules/micromark-util-encode
  micromark-util-html-tag-name@2.0.1:
    - node_modules/micromark-util-html-tag-name
  micromark-util-normalize-identifier@2.0.1:
    - node_modules/micromark-util-normalize-identifier
  micromark-util-resolve-all@2.0.1:
    - node_modules/micromark-util-resolve-all
  micromark-util-sanitize-uri@2.0.1:
    - node_modules/micromark-util-sanitize-uri
  micromark-util-subtokenize@2.1.0:
    - node_modules/micromark-util-subtokenize
  micromark-util-symbol@2.0.1:
    - node_modules/micromark-util-symbol
  micromark-util-types@2.0.2:
    - node_modules/micromark-util-types
  micromark@4.0.2:
    - node_modules/micromark
  mime-db@1.52.0:
    - node_modules/mime-db
  mime-types@2.1.35:
    - node_modules/mime-types
  mime@2.6.0:
    - node_modules/mime
  mimic-fn@2.1.0:
    - node_modules/mimic-fn
  mimic-response@1.0.1:
    - node_modules/mimic-response
  mimic-response@3.1.0:
    - node_modules/decompress-response/node_modules/mimic-response
  minimatch@10.0.1:
    - node_modules/app-builder-lib/node_modules/minimatch
  minimatch@3.1.2:
    - node_modules/minimatch
  minimatch@5.1.6:
    - node_modules/filelist/node_modules/minimatch
    - node_modules/readdir-glob/node_modules/minimatch
    - node_modules/cacache/node_modules/minimatch
  minimatch@9.0.5:
    - node_modules/config-file-ts/node_modules/minimatch
    - node_modules/@electron/universal/node_modules/minimatch
  minimist@1.2.8:
    - node_modules/minimist
  minipass-collect@1.0.2:
    - node_modules/minipass-collect
  minipass-fetch@2.1.2:
    - node_modules/minipass-fetch
  minipass-flush@1.0.5:
    - node_modules/minipass-flush
  minipass-pipeline@1.2.4:
    - node_modules/minipass-pipeline
  minipass-sized@1.0.3:
    - node_modules/minipass-sized
  minipass@3.3.6:
    - node_modules/minipass
  minipass@5.0.0:
    - node_modules/tar/node_modules/minipass
  minipass@7.1.2:
    - node_modules/config-file-ts/node_modules/minipass
    - node_modules/path-scurry/node_modules/minipass
  minizlib@2.1.2:
    - node_modules/minizlib
  mkdirp@1.0.4:
    - node_modules/mkdirp
  ms@2.1.3:
    - node_modules/ms
  nanoid@3.3.11:
    - node_modules/nanoid
  natural-compare@1.4.0:
    - node_modules/natural-compare
  negotiator@0.6.4:
    - node_modules/negotiator
  node-abi@3.74.0:
    - node_modules/node-abi
  node-addon-api@1.7.2:
    - node_modules/node-addon-api
  node-api-version@0.2.1:
    - node_modules/node-api-version
  node-gyp@9.4.1:
    - node_modules/node-gyp
  node-releases@2.0.19:
    - node_modules/node-releases
  nopt@6.0.0:
    - node_modules/nopt
  normalize-path@3.0.0:
    - node_modules/normalize-path
  normalize-url@6.1.0:
    - node_modules/normalize-url
  npmlog@6.0.2:
    - node_modules/npmlog
  object-assign@4.1.1:
    - node_modules/object-assign
  object-inspect@1.13.4:
    - node_modules/object-inspect
  object-keys@1.1.1:
    - node_modules/object-keys
  object.assign@4.1.7:
    - node_modules/object.assign
  object.entries@1.1.9:
    - node_modules/object.entries
  object.fromentries@2.0.8:
    - node_modules/object.fromentries
  object.values@1.2.1:
    - node_modules/object.values
  once@1.4.0:
    - node_modules/once
  onetime@5.1.2:
    - node_modules/onetime
  optionator@0.9.4:
    - node_modules/optionator
  ora@5.4.1:
    - node_modules/ora
  own-keys@1.0.1:
    - node_modules/own-keys
  p-cancelable@2.1.1:
    - node_modules/p-cancelable
  p-limit@3.1.0:
    - node_modules/p-limit
  p-locate@5.0.0:
    - node_modules/p-locate
  p-map@4.0.0:
    - node_modules/p-map
  package-json-from-dist@1.0.1:
    - node_modules/package-json-from-dist
  parent-module@1.0.1:
    - node_modules/parent-module
  parse-entities@4.0.2:
    - node_modules/parse-entities
  path-exists@4.0.0:
    - node_modules/path-exists
  path-is-absolute@1.0.1:
    - node_modules/path-is-absolute
  path-key@3.1.1:
    - node_modules/path-key
  path-parse@1.0.7:
    - node_modules/path-parse
  path-scurry@1.11.1:
    - node_modules/path-scurry
  pe-library@0.4.1:
    - node_modules/pe-library
  pend@1.2.0:
    - node_modules/pend
  picocolors@1.1.1:
    - node_modules/picocolors
  plist@3.1.0:
    - node_modules/plist
  possible-typed-array-names@1.1.0:
    - node_modules/possible-typed-array-names
  postcss@8.5.3:
    - node_modules/postcss
  prelude-ls@1.2.1:
    - node_modules/prelude-ls
  prettier-linter-helpers@1.0.0:
    - node_modules/prettier-linter-helpers
  prettier@3.5.3:
    - node_modules/prettier
  process-nextick-args@2.0.1:
    - node_modules/process-nextick-args
  progress@2.0.3:
    - node_modules/progress
  promise-inflight@1.0.1:
    - node_modules/promise-inflight
  promise-retry@2.0.1:
    - node_modules/promise-retry
  prop-types@15.8.1:
    - node_modules/prop-types
  property-information@7.0.0:
    - node_modules/property-information
  pump@3.0.2:
    - node_modules/pump
  punycode@2.3.1:
    - node_modules/punycode
  quick-lru@5.1.1:
    - node_modules/quick-lru
  react-dom@18.3.1(react@18.3.1):
    - node_modules/react-dom
  react-is@16.13.1:
    - node_modules/react-is
  react-markdown@10.1.0(@types/react@19.1.0)(react@18.3.1):
    - node_modules/react-markdown
  react-refresh@0.14.2:
    - node_modules/react-refresh
  react@18.3.1:
    - node_modules/react
  read-binary-file-arch@1.0.6:
    - node_modules/read-binary-file-arch
  readable-stream@2.3.8:
    - node_modules/archiver-utils/node_modules/readable-stream
    - node_modules/lazystream/node_modules/readable-stream
  readable-stream@3.6.2:
    - node_modules/readable-stream
  readdir-glob@1.1.3:
    - node_modules/readdir-glob
  reflect.getprototypeof@1.0.10:
    - node_modules/reflect.getprototypeof
  regexp.prototype.flags@1.5.4:
    - node_modules/regexp.prototype.flags
  remark-gfm@4.0.1:
    - node_modules/remark-gfm
  remark-parse@11.0.0:
    - node_modules/remark-parse
  remark-rehype@11.1.2:
    - node_modules/remark-rehype
  remark-stringify@11.0.0:
    - node_modules/remark-stringify
  require-directory@2.1.1:
    - node_modules/require-directory
  resedit@1.7.2:
    - node_modules/resedit
  resolve-alpn@1.2.1:
    - node_modules/resolve-alpn
  resolve-from@4.0.0:
    - node_modules/resolve-from
  resolve@2.0.0-next.5:
    - node_modules/resolve
  responselike@2.0.1:
    - node_modules/responselike
  restore-cursor@3.1.0:
    - node_modules/restore-cursor
  retry@0.12.0:
    - node_modules/retry
  rimraf@3.0.2:
    - node_modules/rimraf
  roarr@2.15.4:
    - node_modules/roarr
  rollup@4.39.0:
    - node_modules/rollup
  safe-array-concat@1.1.3:
    - node_modules/safe-array-concat
  safe-buffer@5.1.2:
    - node_modules/safe-buffer
  safe-buffer@5.2.1:
    - node_modules/string_decoder/node_modules/safe-buffer
  safe-push-apply@1.0.0:
    - node_modules/safe-push-apply
  safe-regex-test@1.1.0:
    - node_modules/safe-regex-test
  safer-buffer@2.1.2:
    - node_modules/safer-buffer
  sanitize-filename@1.6.3:
    - node_modules/sanitize-filename
  sax@1.4.1:
    - node_modules/sax
  scheduler@0.23.2:
    - node_modules/scheduler
  semver-compare@1.0.0:
    - node_modules/semver-compare
  semver@6.3.1:
    - node_modules/eslint-plugin-react/node_modules/semver
    - node_modules/@electron/get/node_modules/semver
    - node_modules/@babel/helper-compilation-targets/node_modules/semver
    - node_modules/@babel/core/node_modules/semver
  semver@7.7.1:
    - node_modules/semver
  serialize-error@7.0.1:
    - node_modules/serialize-error
  set-blocking@2.0.0:
    - node_modules/set-blocking
  set-function-length@1.2.2:
    - node_modules/set-function-length
  set-function-name@2.0.2:
    - node_modules/set-function-name
  set-proto@1.0.0:
    - node_modules/set-proto
  shebang-command@2.0.0:
    - node_modules/shebang-command
  shebang-regex@3.0.0:
    - node_modules/shebang-regex
  side-channel-list@1.0.0:
    - node_modules/side-channel-list
  side-channel-map@1.0.1:
    - node_modules/side-channel-map
  side-channel-weakmap@1.0.2:
    - node_modules/side-channel-weakmap
  side-channel@1.1.0:
    - node_modules/side-channel
  signal-exit@3.0.7:
    - node_modules/signal-exit
  signal-exit@4.1.0:
    - node_modules/foreground-child/node_modules/signal-exit
  simple-update-notifier@2.0.0:
    - node_modules/simple-update-notifier
  slice-ansi@3.0.0:
    - node_modules/slice-ansi
  smart-buffer@4.2.0:
    - node_modules/smart-buffer
  socks-proxy-agent@7.0.0:
    - node_modules/socks-proxy-agent
  socks@2.8.4:
    - node_modules/socks
  source-map-js@1.2.1:
    - node_modules/source-map-js
  source-map-support@0.5.21:
    - node_modules/source-map-support
  source-map@0.6.1:
    - node_modules/source-map
  space-separated-tokens@2.0.2:
    - node_modules/space-separated-tokens
  sprintf-js@1.1.3:
    - node_modules/sprintf-js
  ssri@9.0.1:
    - node_modules/ssri
  stat-mode@1.0.0:
    - node_modules/stat-mode
  string-width@4.2.3:
    - node_modules/string-width
    - node_modules/string-width-cjs
  string-width@5.1.2:
    - node_modules/@isaacs/cliui/node_modules/string-width
  string.prototype.matchall@4.0.12:
    - node_modules/string.prototype.matchall
  string.prototype.repeat@1.0.0:
    - node_modules/string.prototype.repeat
  string.prototype.trim@1.2.10:
    - node_modules/string.prototype.trim
  string.prototype.trimend@1.0.9:
    - node_modules/string.prototype.trimend
  string.prototype.trimstart@1.0.8:
    - node_modules/string.prototype.trimstart
  string_decoder@1.1.1:
    - node_modules/archiver-utils/node_modules/string_decoder
    - node_modules/lazystream/node_modules/string_decoder
  string_decoder@1.3.0:
    - node_modules/string_decoder
  stringify-entities@4.0.4:
    - node_modules/stringify-entities
  strip-ansi@6.0.1:
    - node_modules/strip-ansi
    - node_modules/strip-ansi-cjs
  strip-ansi@7.1.0:
    - node_modules/@isaacs/cliui/node_modules/strip-ansi
  strip-json-comments@3.1.1:
    - node_modules/strip-json-comments
  style-to-js@1.1.16:
    - node_modules/style-to-js
  style-to-object@1.0.8:
    - node_modules/style-to-object
  sumchecker@3.0.1:
    - node_modules/sumchecker
  supports-color@7.2.0:
    - node_modules/supports-color
  supports-preserve-symlinks-flag@1.0.0:
    - node_modules/supports-preserve-symlinks-flag
  synckit@0.10.3:
    - node_modules/synckit
  tar-stream@2.2.0:
    - node_modules/tar-stream
  tar@6.2.1:
    - node_modules/tar
  temp-file@3.4.0:
    - node_modules/temp-file
  tiny-typed-emitter@2.1.0:
    - node_modules/tiny-typed-emitter
  tmp-promise@3.0.3:
    - node_modules/tmp-promise
  tmp@0.2.3:
    - node_modules/tmp
  trim-lines@3.0.1:
    - node_modules/trim-lines
  trough@2.2.0:
    - node_modules/trough
  truncate-utf8-bytes@1.0.2:
    - node_modules/truncate-utf8-bytes
  tslib@2.8.1:
    - node_modules/tslib
  type-check@0.4.0:
    - node_modules/type-check
  type-fest@0.13.1:
    - node_modules/type-fest
  typed-array-buffer@1.0.3:
    - node_modules/typed-array-buffer
  typed-array-byte-length@1.0.3:
    - node_modules/typed-array-byte-length
  typed-array-byte-offset@1.0.4:
    - node_modules/typed-array-byte-offset
  typed-array-length@1.0.7:
    - node_modules/typed-array-length
  typescript@5.8.2:
    - node_modules/typescript
  unbox-primitive@1.1.0:
    - node_modules/unbox-primitive
  undici-types@6.20.0:
    - node_modules/undici-types
  unified@11.0.5:
    - node_modules/unified
  unique-filename@2.0.1:
    - node_modules/unique-filename
  unique-slug@3.0.0:
    - node_modules/unique-slug
  unist-util-is@6.0.0:
    - node_modules/unist-util-is
  unist-util-position@5.0.0:
    - node_modules/unist-util-position
  unist-util-stringify-position@4.0.0:
    - node_modules/unist-util-stringify-position
  unist-util-visit-parents@6.0.1:
    - node_modules/unist-util-visit-parents
  unist-util-visit@5.0.0:
    - node_modules/unist-util-visit
  universalify@0.1.2:
    - node_modules/@electron/get/node_modules/universalify
  universalify@2.0.1:
    - node_modules/universalify
  update-browserslist-db@1.1.3(browserslist@4.24.4):
    - node_modules/update-browserslist-db
  uri-js@4.4.1:
    - node_modules/uri-js
  utf8-byte-length@1.0.5:
    - node_modules/utf8-byte-length
  util-deprecate@1.0.2:
    - node_modules/util-deprecate
  verror@1.10.1:
    - node_modules/verror
  vfile-message@4.0.2:
    - node_modules/vfile-message
  vfile@6.0.3:
    - node_modules/vfile
  vite@6.2.4(@types/node@22.13.17):
    - node_modules/vite
  wcwidth@1.0.1:
    - node_modules/wcwidth
  which-boxed-primitive@1.1.1:
    - node_modules/which-boxed-primitive
  which-builtin-type@1.2.1:
    - node_modules/which-builtin-type
  which-collection@1.0.2:
    - node_modules/which-collection
  which-typed-array@1.1.19:
    - node_modules/which-typed-array
  which@2.0.2:
    - node_modules/which
  wide-align@1.1.5:
    - node_modules/wide-align
  word-wrap@1.2.5:
    - node_modules/word-wrap
  wrap-ansi@7.0.0:
    - node_modules/wrap-ansi
    - node_modules/wrap-ansi-cjs
  wrap-ansi@8.1.0:
    - node_modules/@isaacs/cliui/node_modules/wrap-ansi
  wrappy@1.0.2:
    - node_modules/wrappy
  xmlbuilder@15.1.1:
    - node_modules/xmlbuilder
  y18n@5.0.8:
    - node_modules/y18n
  yallist@3.1.1:
    - node_modules/@babel/helper-compilation-targets/node_modules/yallist
  yallist@4.0.0:
    - node_modules/yallist
  yargs-parser@21.1.1:
    - node_modules/yargs-parser
  yargs@17.7.2:
    - node_modules/yargs
  yauzl@2.10.0:
    - node_modules/yauzl
  yocto-queue@0.1.0:
    - node_modules/yocto-queue
  zip-stream@4.1.1:
    - node_modules/zip-stream
  zwitch@2.0.4:
    - node_modules/zwitch
ignoredBuilds:
  - electron
  - esbuild
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: hoisted
packageManager: pnpm@10.6.5
pendingBuilds: []
prunedAt: Wed, 02 Apr 2025 15:01:29 GMT
publicHoistPattern:
  - '*'
registries:
  default: https://registry.yarnpkg.com/
skipped:
  - '@esbuild/aix-ppc64@0.25.2'
  - '@esbuild/android-arm64@0.25.2'
  - '@esbuild/android-arm@0.25.2'
  - '@esbuild/android-x64@0.25.2'
  - '@esbuild/darwin-x64@0.25.2'
  - '@esbuild/freebsd-arm64@0.25.2'
  - '@esbuild/freebsd-x64@0.25.2'
  - '@esbuild/linux-arm64@0.25.2'
  - '@esbuild/linux-arm@0.25.2'
  - '@esbuild/linux-ia32@0.25.2'
  - '@esbuild/linux-loong64@0.25.2'
  - '@esbuild/linux-mips64el@0.25.2'
  - '@esbuild/linux-ppc64@0.25.2'
  - '@esbuild/linux-riscv64@0.25.2'
  - '@esbuild/linux-s390x@0.25.2'
  - '@esbuild/linux-x64@0.25.2'
  - '@esbuild/netbsd-arm64@0.25.2'
  - '@esbuild/netbsd-x64@0.25.2'
  - '@esbuild/openbsd-arm64@0.25.2'
  - '@esbuild/openbsd-x64@0.25.2'
  - '@esbuild/sunos-x64@0.25.2'
  - '@esbuild/win32-arm64@0.25.2'
  - '@esbuild/win32-ia32@0.25.2'
  - '@esbuild/win32-x64@0.25.2'
  - '@rollup/rollup-android-arm-eabi@4.39.0'
  - '@rollup/rollup-android-arm64@4.39.0'
  - '@rollup/rollup-darwin-x64@4.39.0'
  - '@rollup/rollup-freebsd-arm64@4.39.0'
  - '@rollup/rollup-freebsd-x64@4.39.0'
  - '@rollup/rollup-linux-arm-gnueabihf@4.39.0'
  - '@rollup/rollup-linux-arm-musleabihf@4.39.0'
  - '@rollup/rollup-linux-arm64-gnu@4.39.0'
  - '@rollup/rollup-linux-arm64-musl@4.39.0'
  - '@rollup/rollup-linux-loongarch64-gnu@4.39.0'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.39.0'
  - '@rollup/rollup-linux-riscv64-gnu@4.39.0'
  - '@rollup/rollup-linux-riscv64-musl@4.39.0'
  - '@rollup/rollup-linux-s390x-gnu@4.39.0'
  - '@rollup/rollup-linux-x64-gnu@4.39.0'
  - '@rollup/rollup-linux-x64-musl@4.39.0'
  - '@rollup/rollup-win32-arm64-msvc@4.39.0'
  - '@rollup/rollup-win32-ia32-msvc@4.39.0'
  - '@rollup/rollup-win32-x64-msvc@4.39.0'
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
