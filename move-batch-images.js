#!/usr/bin/env node

const fs = require('fs').promises
const path = require('path')
const { existsSync } = require('fs')

/**
 * 简单的批次图片移动脚本
 * 将批次目录中的图片移动到上级目录
 */

async function moveBatchImages(targetDir) {
  try {
    console.log('🔍 扫描目录:', targetDir)
    
    if (!existsSync(targetDir)) {
      console.log('❌ 目录不存在:', targetDir)
      return
    }
    
    // 获取目录中的所有项目
    const items = await fs.readdir(targetDir)
    
    let totalMoved = 0
    let totalDirectories = 0
    
    for (const item of items) {
      const itemPath = path.join(targetDir, item)
      const stat = await fs.stat(itemPath)
      
      // 只处理目录
      if (!stat.isDirectory()) continue
      
      // 检查是否是批次目录
      if (!item.startsWith('batch_')) {
        console.log(`⏭️  跳过非批次目录: ${item}`)
        continue
      }
      
      totalDirectories++
      console.log(`\n📁 处理批次目录: ${item}`)
      
      // 获取批次目录中的所有文件
      const files = await fs.readdir(itemPath)
      let movedInThisBatch = 0
      
      for (const file of files) {
        const filePath = path.join(itemPath, file)
        const fileStat = await fs.stat(filePath)
        
        if (!fileStat.isFile()) continue
        
        // 检查是否是图片文件
        const ext = path.extname(file).toLowerCase()
        if (!['.jpg', '.jpeg', '.png', '.webp', '.gif', '.bmp', '.tiff'].includes(ext)) {
          console.log(`⏭️  跳过非图片文件: ${file}`)
          continue
        }
        
        // 目标路径（移动到上级目录）
        let targetPath = path.join(targetDir, file)
        
        // 如果文件已存在，添加批次前缀避免冲突
        if (existsSync(targetPath)) {
          const baseName = path.basename(file, ext)
          const batchPrefix = item.replace('batch_', '').substring(0, 8) // 取批次ID前8位
          const newFileName = `${batchPrefix}_${baseName}${ext}`
          targetPath = path.join(targetDir, newFileName)
          
          console.log(`⚠️  文件冲突，重命名: ${file} -> ${newFileName}`)
        } else {
          console.log(`✅ 移动文件: ${file}`)
        }
        
        await fs.rename(filePath, targetPath)
        movedInThisBatch++
        totalMoved++
      }
      
      // 检查批次目录是否为空
      const remainingItems = await fs.readdir(itemPath)
      if (remainingItems.length === 0) {
        console.log(`🗑️  删除空目录: ${item}`)
        await fs.rmdir(itemPath)
      } else {
        console.log(`📋 目录不为空，保留: ${item} (剩余 ${remainingItems.length} 个文件)`)
      }
      
      console.log(`   移动了 ${movedInThisBatch} 个图片文件`)
    }
    
    console.log(`\n🎉 处理完成！`)
    console.log(`📊 统计信息:`)
    console.log(`   - 处理了 ${totalDirectories} 个批次目录`)
    console.log(`   - 移动了 ${totalMoved} 个图片文件`)
    
  } catch (error) {
    console.error('❌ 处理过程中出错:', error)
  }
}

// 获取命令行参数
const args = process.argv.slice(2)

if (args.includes('--help') || args.includes('-h')) {
  console.log(`
📁 批次图片移动工具

用法: node move-batch-images.js [目录路径]

参数:
  目录路径    要处理的目录路径（可选，默认为当前目录）

选项:
  --help, -h  显示帮助信息

示例:
  node move-batch-images.js
  node move-batch-images.js /path/to/your/directory
  node move-batch-images.js "~/Library/Application Support/Observer-137/outputs/2025-06-30"
`)
  process.exit(0)
}

// 确定目标目录
let targetDirectory = process.cwd() // 默认当前目录

if (args.length > 0 && !args[0].startsWith('--')) {
  targetDirectory = path.resolve(args[0])
}

console.log('🚀 开始整理批次图片...')
console.log('📂 目标目录:', targetDirectory)

// 运行脚本
moveBatchImages(targetDirectory)
