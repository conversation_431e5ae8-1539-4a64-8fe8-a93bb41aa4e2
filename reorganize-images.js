#!/usr/bin/env node

const fs = require('fs').promises
const path = require('path')
const { existsSync } = require('fs')

/**
 * 整理批次目录中的图片文件
 * 将所有批次子目录中的图片移动到父目录
 */

async function reorganizeImages() {
  try {
    // 获取用户数据目录
    const os = require('os')
    const userDataPath = path.join(os.homedir(), 'Library', 'Application Support', 'Observer-137')
    const outputsPath = path.join(userDataPath, 'outputs')
    
    console.log('🔍 扫描输出目录:', outputsPath)
    
    if (!existsSync(outputsPath)) {
      console.log('❌ 输出目录不存在:', outputsPath)
      return
    }
    
    // 获取所有日期目录
    const dateDirs = await fs.readdir(outputsPath)
    
    for (const dateDir of dateDirs) {
      const datePath = path.join(outputsPath, dateDir)
      const stat = await fs.stat(datePath)
      
      if (!stat.isDirectory()) continue
      
      console.log(`\n📅 处理日期目录: ${dateDir}`)
      
      // 获取该日期下的所有批次目录
      const batchDirs = await fs.readdir(datePath)
      
      let movedCount = 0
      let skippedCount = 0
      
      for (const batchDir of batchDirs) {
        const batchPath = path.join(datePath, batchDir)
        const batchStat = await fs.stat(batchPath)
        
        if (!batchStat.isDirectory()) continue
        
        // 检查是否是批次目录（以batch_开头）
        if (!batchDir.startsWith('batch_')) {
          console.log(`⏭️  跳过非批次目录: ${batchDir}`)
          continue
        }
        
        console.log(`📁 处理批次目录: ${batchDir}`)
        
        // 获取批次目录中的所有文件
        const files = await fs.readdir(batchPath)
        
        for (const file of files) {
          const filePath = path.join(batchPath, file)
          const fileStat = await fs.stat(filePath)
          
          if (!fileStat.isFile()) continue
          
          // 检查是否是图片文件
          const ext = path.extname(file).toLowerCase()
          if (!['.jpg', '.jpeg', '.png', '.webp', '.gif'].includes(ext)) {
            console.log(`⏭️  跳过非图片文件: ${file}`)
            continue
          }
          
          // 目标路径（移动到日期目录下）
          const targetPath = path.join(datePath, file)
          
          // 检查目标文件是否已存在
          if (existsSync(targetPath)) {
            // 生成新的文件名避免冲突
            const baseName = path.basename(file, ext)
            const timestamp = Date.now()
            const newFileName = `${baseName}_${timestamp}${ext}`
            const newTargetPath = path.join(datePath, newFileName)
            
            console.log(`⚠️  文件已存在，重命名: ${file} -> ${newFileName}`)
            await fs.rename(filePath, newTargetPath)
            movedCount++
          } else {
            console.log(`✅ 移动文件: ${file}`)
            await fs.rename(filePath, targetPath)
            movedCount++
          }
        }
        
        // 检查批次目录是否为空，如果为空则删除
        const remainingFiles = await fs.readdir(batchPath)
        if (remainingFiles.length === 0) {
          console.log(`🗑️  删除空目录: ${batchDir}`)
          await fs.rmdir(batchPath)
        } else {
          console.log(`📋 目录不为空，保留: ${batchDir} (剩余 ${remainingFiles.length} 个文件)`)
          skippedCount++
        }
      }
      
      console.log(`📊 ${dateDir} 处理完成: 移动了 ${movedCount} 个文件，跳过 ${skippedCount} 个目录`)
    }
    
    console.log('\n🎉 图片整理完成！')
    
  } catch (error) {
    console.error('❌ 整理过程中出错:', error)
  }
}

// 添加命令行参数支持
const args = process.argv.slice(2)

if (args.includes('--help') || args.includes('-h')) {
  console.log(`
📁 图片目录整理工具

用法: node reorganize-images.js [选项]

选项:
  --help, -h     显示帮助信息
  --dry-run      预览模式，不实际移动文件
  --target-dir   指定目标目录路径

示例:
  node reorganize-images.js
  node reorganize-images.js --dry-run
  node reorganize-images.js --target-dir "/path/to/outputs"
`)
  process.exit(0)
}

if (args.includes('--dry-run')) {
  console.log('🔍 预览模式：将显示要执行的操作，但不会实际移动文件')
  // TODO: 实现预览模式
}

// 运行脚本
reorganizeImages()
