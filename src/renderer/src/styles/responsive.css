/* 响应式布局辅助样式 */

/* 大屏幕设备 (桌面, 1200px 及以上) */
@media (min-width: 1200px) {
  .chat-container {
    width: 100%; /* 移除最大宽度限制，占满屏幕 */
    max-width: 100%;
  }
}

/* 中等屏幕设备 (平板电脑, 768px 到 1199px) */
@media (min-width: 768px) and (max-width: 1199px) {
  .chat-container {
    max-width: 100%;
    padding: var(--spacing-md);
  }
}

/* 小屏幕设备 (手机, 767px 及以下) */
@media (max-width: 767px) {
  .chat-container {
    padding: var(--spacing-sm);
  }
  
  .chat-content {
    padding: var(--spacing-sm);
  }
  
  .chat-header {
    margin-bottom: var(--spacing-md);
  }
  
  .chat-title {
    font-size: var(--font-size-subtitle);
  }
  
  .chat-subtitle {
    font-size: var(--font-size-small);
  }
  
  .message-item {
    margin-bottom: var(--spacing-md);
  }
  
  .message-content {
    max-width: 85%;
  }
  
  .avatar {
    width: 32px;
    height: 32px;
    font-size: 10px;
  }
  
  .input-area {
    margin-top: var(--spacing-sm);
  }
}

/* 确保内容不会被键盘挤压在移动设备上 */
@media (max-height: 500px) {
  .chat-header {
    display: none;
  }
  
  .chat-container {
    padding-top: var(--spacing-xs);
    padding-bottom: var(--spacing-xs);
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  :root {
    --color-text: #000000;
    --color-background: #ffffff;
    --color-primary: #006400;
    --color-border: #000000;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.4);
  }
  
  .message-body {
    border: 2px solid var(--color-border);
  }
}

/* 减少动画 - 对动画敏感的用户 */
@media (prefers-reduced-motion) {
  *, *::before, *::after {
    animation-duration: 0.001ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.001ms !important;
  }
  
  .welcome-message {
    animation: none !important;
  }
}
