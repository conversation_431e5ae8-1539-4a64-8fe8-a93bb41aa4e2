/* 吉卜力风格装饰元素样式 */

/* 基础样式 */
.ghibli-decorations {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
  overflow: hidden;
}

.decoration-svg {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 云朵样式 */
.cloud {
  position: absolute;
  opacity: 0.2;
}

.cloud-1 {
  top: 15%;
  left: -5%;
  width: 150px;
  height: 75px;
  animation: cloud-drift 120s linear infinite;
}

.cloud-2 {
  top: 45%;
  left: -8%;
  width: 200px;
  height: 100px;
  animation: cloud-drift 150s linear infinite 30s;
}

.cloud-3 {
  top: 75%;
  left: -7%;
  width: 120px;
  height: 60px;
  animation: cloud-drift 180s linear infinite 60s;
}

.cloud-4 {
  top: 25%;
  left: -10%;
  width: 100px;
  height: 50px;
  animation: cloud-drift 140s linear infinite 15s;
}

.cloud-5 {
  top: 60%;
  left: -12%;
  width: 160px;
  height: 80px;
  animation: cloud-drift 160s linear infinite 45s;
}



/* 动画效果 */
@keyframes cloud-drift {
  from {
    transform: translateX(0);
    opacity: 0;
  }
  10% {
    opacity: 0.2;
  }
  90% {
    opacity: 0.2;
  }
  to {
    transform: translateX(calc(100vw + 200px));
    opacity: 0;
  }
}



/* 媒体查询 - 在小屏幕上减少装饰元素 */
@media (max-width: 768px) {
  .cloud-4, .cloud-5 {
    display: none;
  }
}

/* 减少动画 - 对动画敏感的用户 */
@media (prefers-reduced-motion) {
  .cloud {
    animation: none !important;
    opacity: 0.2 !important;
  }
}
