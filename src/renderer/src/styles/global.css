@import './variables.css';

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body, #root {
  height: 100%;
  width: 100%;
}

body {
  font-family: 'Alibaba PuHuiTi', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  font-size: var(--font-size-body);
  line-height: 1.5;
  color: var(--color-text);
  background-color: var(--color-background);
  background-image: url('../assets/wavy-lines.svg');
  background-repeat: repeat;
  background-size: 300px;
  background-blend-mode: soft-light;
  background-attachment: fixed;
  overflow-x: hidden;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-serif);
  margin-bottom: var(--spacing-md);
}

h1 {
  font-size: var(--font-size-large);
}

h2 {
  font-size: var(--font-size-title);
}

h3 {
  font-size: var(--font-size-subtitle);
}

p {
  margin-bottom: var(--spacing-md);
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--color-forest-green);
}

button {
  cursor: pointer;
  font-family: var(--font-family-sans);
  border: 2px solid var(--color-border);
  background-color: var(--color-soft-beige);
  color: var(--color-text);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-sm);
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
}

button:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('../assets/wavy-lines.svg');
  background-size: 150px;
  opacity: 0.1;
  z-index: -1;
}

button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

button:active {
  transform: translateY(1px);
  box-shadow: var(--shadow-sm);
}

button.primary {
  background-color: var(--color-primary);
  color: white;
}

button.secondary {
  background-color: var(--color-secondary);
  color: var(--color-text);
}

button.accent {
  background-color: var(--color-accent);
  color: white;
}

input, textarea {
  font-family: var(--font-family-sans);
  padding: var(--spacing-sm) var(--spacing-md);
  border: 2px solid var(--color-border);
  border-radius: var(--border-radius-sm);
  background-color: rgba(255, 255, 255, 0.8);
  transition: all var(--transition-fast);
}

input:focus, textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(79, 121, 66, 0.2);
}

.paper-card {
  background-color: transparent;
  border-radius: var(--border-radius-md);
  border: 1px solid rgba(139, 69, 19, 0.2);
  padding: var(--spacing-lg);
  position: relative;
  overflow: hidden;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--color-primary);
  opacity: 0.5;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-forest-green);
}

/* 页面转场动画 */
.fade-enter {
  opacity: 0;
}

.fade-enter-active {
  opacity: 1;
  transition: opacity var(--transition-normal);
}

.fade-exit {
  opacity: 1;
}

.fade-exit-active {
  opacity: 0;
  transition: opacity var(--transition-normal);
}
