@import './variables.css';

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-md);
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.spinner-dot {
  background-color: var(--color-primary);
  border-radius: 50%;
  display: inline-block;
  margin: 0 var(--spacing-xs);
  opacity: 0.6;
  animation: bounce 1.4s infinite ease-in-out both;
}

.spinner-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.spinner-dot:nth-child(2) {
  animation-delay: -0.16s;
}

/* 小尺寸 */
.loading-indicator-small .spinner-dot {
  width: 6px;
  height: 6px;
}

/* 中尺寸 */
.loading-indicator-medium .spinner-dot {
  width: 10px;
  height: 10px;
}

/* 大尺寸 */
.loading-indicator-large .spinner-dot {
  width: 14px;
  height: 14px;
}

.loading-text {
  margin-top: var(--spacing-md);
  font-size: var(--font-size-small);
  color: var(--color-text-light);
  font-family: var(--font-family-serif);
  letter-spacing: 1px;
}

/* 吉卜力风格的弹性动画 */
@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0.6);
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}
