.image-converter {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #333;
  font-family: var(--font-family-sans, 'Segoe UI', 'Microsoft YaHei', sans-serif);
}

.converter-header {
  background: rgba(255, 255, 255, 0.95);
  padding: 20px 30px;
  text-align: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.converter-header h1 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
}

.converter-header p {
  margin: 0 0 15px 0;
  color: #666;
  font-size: 14px;
}

/* API配置状态 */
.api-status {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 15px;
  padding: 12px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.status-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 500;
  color: #2c3e50;
}

.status-indicator {
  font-size: 14px;
}

.status-indicator.connected {
  color: #27ae60;
}

.status-indicator.warning {
  color: #f39c12;
}

.status-indicator.error {
  color: #e74c3c;
}

.converter-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 左侧输入区域 */
.input-section {
  width: 400px;
  background: rgba(255, 255, 255, 0.95);
  padding: 20px;
  overflow-y: auto;
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.input-group {
  margin-bottom: 20px;
}

.input-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #2c3e50;
  font-size: 14px;
}

.file-input-wrapper {
  display: flex;
  gap: 10px;
}

.file-select-btn {
  flex: 1;
  padding: 12px 16px;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.file-select-btn:hover:not(:disabled) {
  background: #2980b9;
  transform: translateY(-1px);
}

.file-select-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.clear-btn {
  padding: 12px 16px;
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.clear-btn:hover:not(:disabled) {
  background: #c0392b;
  transform: translateY(-1px);
}

/* 图片预览区域 */
.image-preview-section {
  margin-bottom: 20px;
}

.preview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 12px;
  max-height: 200px;
  overflow-y: auto;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.preview-item {
  position: relative;
  background: white;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.preview-image {
  width: 100%;
  height: 80px;
  object-fit: cover;
}

.preview-info {
  padding: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.image-name {
  font-size: 11px;
  color: #666;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.remove-btn {
  width: 20px;
  height: 20px;
  border: none;
  background: #e74c3c;
  color: white;
  border-radius: 50%;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.remove-btn:hover:not(:disabled) {
  background: #c0392b;
  transform: scale(1.1);
}

/* 提示词输入 */
textarea {
  width: 100%;
  padding: 12px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  transition: border-color 0.2s ease;
  box-sizing: border-box;
}

textarea:focus {
  outline: none;
  border-color: #3498db;
}

textarea:disabled {
  background: #f8f9fa;
  opacity: 0.7;
}

/* 参数设置 */
.params-section {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.params-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #2c3e50;
}

/* 风格预览 */
.style-preview {
  margin-bottom: 16px;
  padding: 12px;
  background: #e8f4fd;
  border-radius: 6px;
  border-left: 4px solid #3498db;
}

.style-preview label {
  font-size: 12px;
  color: #2c3e50;
  font-weight: 600;
  margin-bottom: 6px;
  display: block;
}

.style-prompt-preview {
  font-size: 11px;
  color: #555;
  line-height: 1.4;
  font-family: 'Courier New', monospace;
  background: rgba(255, 255, 255, 0.7);
  padding: 8px;
  border-radius: 4px;
}

/* 高级参数 */
.advanced-params {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 2px dashed #ddd;
}

.param-divider {
  text-align: center;
  margin-bottom: 16px;
}

.param-divider span {
  background: #f8f9fa;
  padding: 0 12px;
  color: #666;
  font-size: 13px;
  font-weight: 500;
}

.param-input-group {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.param-value {
  min-width: 40px;
  text-align: center;
  font-weight: 600;
  color: #2c3e50;
  background: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.param-description {
  font-size: 11px;
  color: #666;
  margin-top: 6px;
  line-height: 1.4;
  font-style: italic;
  padding: 6px 8px;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 4px;
  border-left: 2px solid rgba(102, 126, 234, 0.3);
}

.param-row {
  margin-bottom: 16px;
}

.param-row:last-child {
  margin-bottom: 0;
}

.param-row label {
  font-size: 13px;
  color: #555;
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
}

/* 复合参数行（带控件和值显示的） */
.param-row.has-control {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.param-control-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

/* 简单参数行（只有复选框的） */
.param-row.simple {
  display: flex;
  align-items: center;
  gap: 8px;
}

.param-row.simple label {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.param-control-row select,
.param-control-row input[type="number"] {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 13px;
  min-width: 140px;
  flex: 1;
  background: white;
  transition: border-color 0.2s ease;
}

.param-control-row select:focus,
.param-control-row input[type="number"]:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.param-row input[type="range"] {
  flex: 1;
  margin: 0 8px;
}

.param-row input[type="checkbox"] {
  margin: 0;
}

/* 转换按钮 */
.convert-btn {
  width: 100%;
  padding: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.convert-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.convert-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 右侧结果区域 */
.results-section {
  flex: 1;
  background: rgba(255, 255, 255, 0.9);
  padding: 20px;
  overflow-y: auto;
  backdrop-filter: blur(10px);
}

.results-header {
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid #e1e5e9;
}

.results-header > h3 {
  margin-bottom: 12px;
}

.results-header h3 {
  margin: 0;
  font-size: 20px;
  color: #2c3e50;
}

.stats {
  display: flex;
  gap: 16px;
  font-size: 13px;
  color: #666;
}

.stats span {
  padding: 4px 8px;
  background: #f8f9fa;
  border-radius: 4px;
}

.results-actions {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-top: 12px;
  flex-wrap: wrap;
}

.download-all-btn,
.open-folder-btn,
.file-manager-btn,
.clean-btn,
.copy-path-btn,
.open-local-btn {
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.download-all-btn {
  background: #27ae60;
  color: white;
}

.download-all-btn:hover {
  background: #219a52;
  transform: translateY(-1px);
}

.open-folder-btn {
  background: #3498db;
  color: white;
}

.open-folder-btn:hover {
  background: #2980b9;
  transform: translateY(-1px);
}

.file-manager-btn {
  background: #9b59b6;
  color: white;
}

.file-manager-btn:hover {
  background: #8e44ad;
  transform: translateY(-1px);
}

.clean-btn {
  background: #e74c3c;
  color: white;
}

.clean-btn:hover {
  background: #c0392b;
  transform: translateY(-1px);
}

.copy-path-btn,
.open-local-btn {
  background: #95a5a6;
  color: white;
  padding: 4px 8px;
  font-size: 11px;
}

.copy-path-btn:hover,
.open-local-btn:hover {
  background: #7f8c8d;
  transform: translateY(-1px);
}

/* 文件管理面板 */
.file-manager-panel {
  background: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.file-info h4 {
  margin: 0 0 12px 0;
  color: #2c3e50;
  font-size: 14px;
}

.path-info {
  margin-bottom: 12px;
}

.path-info label {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
  font-weight: 500;
}

.path-display {
  display: flex;
  align-items: center;
  gap: 8px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px;
}

.path-display code {
  flex: 1;
  font-size: 11px;
  color: #2c3e50;
  background: none;
  padding: 0;
  word-break: break-all;
}

.file-actions {
  display: flex;
  gap: 8px;
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid #e1e5e9;
  flex-wrap: wrap;
}

/* 处理中指示器 */
.processing-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* 结果网格 */
.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.result-item {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.result-item:hover {
  transform: translateY(-4px);
}

.result-image-container {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.result-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.result-image:hover {
  transform: scale(1.05);
}

.result-info {
  padding: 16px;
}

.result-name {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.result-meta {
  font-size: 11px;
  color: #666;
  margin-bottom: 12px;
  line-height: 1.4;
}

.result-meta > div {
  margin-bottom: 4px;
}

.local-file {
  color: #2c3e50;
  font-weight: 500;
}

.local-status {
  color: #27ae60;
  font-weight: 500;
}

.result-actions {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.download-btn {
  flex: 1;
  padding: 8px 12px;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: auto;
}

.download-btn:hover {
  background: #2980b9;
  transform: translateY(-1px);
}

/* 空状态 */
.empty-results {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  color: #999;
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .converter-content {
    flex-direction: column;
  }
  
  .input-section {
    width: 100%;
    max-height: 50vh;
  }
  
  .results-section {
    max-height: 50vh;
  }
}

@media (max-width: 768px) {
  .converter-header {
    padding: 16px 20px;
  }

  .converter-header h1 {
    font-size: 24px;
  }
  
  .api-status {
    flex-direction: column;
    gap: 8px;
  }
  
  .status-item {
    justify-content: center;
  }
  
  .input-section,
  .results-section {
    padding: 16px;
  }
  
  .file-input-wrapper {
    flex-direction: column;
  }
  
  .preview-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  }
  
  .results-grid {
    grid-template-columns: 1fr;
  }
  
  .param-row.has-control {
    gap: 6px;
  }
  
  .param-control-row {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .param-control-row select,
  .param-control-row input[type="number"] {
    width: 100%;
    min-width: unset;
  }
  
  .param-input-group {
    width: 100%;
  }
  
  .results-actions {
    justify-content: flex-start;
  }
  
  .file-actions {
    flex-direction: column;
  }
  
  .path-display {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .result-actions {
    flex-direction: column;
  }
  
  .download-btn,
  .open-local-btn {
    width: 100%;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.result-item {
  animation: fadeIn 0.3s ease-out;
}

/* 滚动条样式 */
.input-section::-webkit-scrollbar,
.results-section::-webkit-scrollbar,
.preview-grid::-webkit-scrollbar {
  width: 6px;
}

.input-section::-webkit-scrollbar-track,
.results-section::-webkit-scrollbar-track,
.preview-grid::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.input-section::-webkit-scrollbar-thumb,
.results-section::-webkit-scrollbar-thumb,
.preview-grid::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.input-section::-webkit-scrollbar-thumb:hover,
.results-section::-webkit-scrollbar-thumb:hover,
.preview-grid::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
} 