/* Markdown样式 */

/* 基础文本样式 */
.message-text {
  font-family: var(--font-family-sans);
  font-size: var(--font-size-body);
  line-height: 1.6;
  color: var(--color-text);
  word-break: break-word;
  overflow-wrap: break-word;
  white-space: pre-wrap;
}

/* 标题样式 */
.message-text h1, 
.message-text h2, 
.message-text h3, 
.message-text h4, 
.message-text h5, 
.message-text h6 {
  font-family: var(--font-family-serif);
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  font-weight: 600;
  line-height: 1.2;
  color: var(--color-primary);
}

.message-text h1 {
  font-size: 1.6em;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding-bottom: 0.2em;
}

.message-text h2 {
  font-size: 1.4em;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding-bottom: 0.2em;
}

.message-text h3 {
  font-size: 1.2em;
}

.message-text h4 {
  font-size: 1.1em;
}

.message-text h5, .message-text h6 {
  font-size: 1em;
}

/* 段落样式 */
.message-text p {
  margin: 0.8em 0;
}

/* 链接样式 */
.message-text a {
  color: var(--color-primary);
  text-decoration: none;
  position: relative;
  transition: color var(--transition-fast);
}

.message-text a:hover {
  color: var(--color-forest-green);
  text-decoration: underline;
}

/* 行内代码样式 */
.message-text .inline-code {
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
  padding: 0.2em 0.4em;
  font-family: var(--font-family-mono);
  font-size: 0.9em;
  color: #d73a49;
}

/* 代码块样式 */
.message-text .code-block {
  background-color: #f8f8f8;
  border-radius: var(--border-radius-sm);
  padding: 0.8em;
  margin: 1em 0;
  overflow: auto;
  border: 1px solid #e0e0e0;
  box-shadow: var(--shadow-sm);
}

.message-text .code-block code {
  font-family: var(--font-family-mono);
  font-size: 0.9em;
  line-height: 1.5;
  display: block;
  color: #333;
}

/* 引用块样式 */
.message-text blockquote {
  border-left: 3px solid var(--color-accent);
  margin: 1em 0;
  padding: 0.5em 1em;
  background-color: rgba(165, 214, 167, 0.1);
  color: var(--color-text-light);
  font-style: italic;
}

/* 列表样式 */
.message-text ul, 
.message-text ol {
  padding-left: 1.8em;
  margin: 0.8em 0;
}

.message-text li {
  margin: 0.3em 0;
}

.message-text li > ul, 
.message-text li > ol {
  margin: 0.2em 0;
}

/* 表格样式 */
.message-text .md-table {
  border-collapse: collapse;
  margin: 1em 0;
  width: 100%;
  overflow-x: auto;
  display: block;
}

.message-text .md-table th,
.message-text .md-table td {
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  text-align: left;
}

.message-text .md-table th {
  background-color: rgba(0, 0, 0, 0.05);
  font-weight: 600;
}

.message-text .md-table tr:nth-child(even) {
  background-color: rgba(0, 0, 0, 0.02);
}

/* 水平线样式 */
.message-text hr {
  border: 0;
  height: 1px;
  background-color: #e0e0e0;
  margin: 2em 0;
}

/* 图片样式 */
.message-text img {
  max-width: 100%;
  border-radius: var(--border-radius-sm);
  margin: 1em 0;
  box-shadow: var(--shadow-sm);
}

/* 复选框样式 */
.message-text input[type="checkbox"] {
  margin-right: 0.5em;
}

/* 突出显示的语法 */
.message-text mark {
  background-color: rgba(255, 255, 0, 0.3);
  padding: 0.1em 0.2em;
  border-radius: 3px;
}

/* 适配深色模式 */
@media (prefers-color-scheme: dark) {
  .message-text code {
    background-color: rgba(255, 255, 255, 0.1);
  }
  
  .message-text .code-block {
    background-color: #2d2d2d;
    border-color: #444;
  }
  
  .message-text .code-block code {
    color: #eee;
  }
  
  .message-text .md-table th,
  .message-text .md-table td {
    border-color: #444;
  }
  
  .message-text .md-table th {
    background-color: rgba(255, 255, 255, 0.1);
  }
  
  .message-text .md-table tr:nth-child(even) {
    background-color: rgba(255, 255, 255, 0.05);
  }
  
  .message-text blockquote {
    background-color: rgba(165, 214, 167, 0.05);
  }
}
