/**
 * Replicate API服务
 * 使用官方 Replicate Node.js 客户端调用 flux-kontext-pro 模型进行图像转换
 */

const REPLICATE_API_TOKEN = import.meta.env.VITE_REPLICATE_API_TOKEN
const DEEPSEEK_API_KEY = import.meta.env.VITE_DEEPSEEK_API_KEY

// API 基础 URL
const REPLICATE_BASE_URL = 'https://api.replicate.com/v1'
const DEEPSEEK_BASE_URL = import.meta.env.PROD
  ? 'https://api.deepseek.com/v1'
  : '/deepseek'

// 本地存储路径管理
const getAppDataPath = () => {
  // Electron环境下获取应用数据目录
  if (window.electronAPI) {
    return window.electronAPI.getAppDataPath()
  }
  // Web环境下使用Downloads目录
  return 'Downloads/FluxConverter'
}

const getOutputPath = (batchId = null) => {
  const appDataPath = getAppDataPath()
  const timestamp = new Date().toISOString().split('T')[0] // YYYY-MM-DD

  if (batchId) {
    // 为批次创建专门的目录
    return `${appDataPath}/outputs/${timestamp}/${batchId}`
  }

  return `${appDataPath}/outputs/${timestamp}`
}

// 创建输出目录
const ensureOutputDirectory = async (batchId = null) => {
  const outputPath = getOutputPath(batchId)
  if (window.electronAPI) {
    await window.electronAPI.ensureDirectory(outputPath)
  }
  return outputPath
}

// 保存base64图片到本地
const saveBase64Image = async (dataUrl, fileName, outputPath) => {
  try {
    console.log('[文件管理] 保存Base64图片:', fileName)
    
    // 提取base64数据
    let base64Data = dataUrl
    if (dataUrl.startsWith('data:')) {
      base64Data = dataUrl.split(',')[1]
    }
    
    // 如果是Electron环境，通过主进程保存
    if (window.electronAPI) {
      console.log('[文件管理] 使用主进程保存Base64数据')
      const filePath = `${outputPath}/${fileName}`
      
      // 将base64转换为Buffer
      const buffer = Buffer.from(base64Data, 'base64')
      await window.electronAPI.writeFile(filePath, buffer)
      console.log('[文件管理] Base64图片已保存:', filePath)
      return filePath
    } else {
      // Web环境下使用Blob下载
      console.log('[文件管理] 使用Web方式保存Base64数据')
      
      // 将base64转换为Blob
      const byteCharacters = atob(base64Data)
      const byteNumbers = new Array(byteCharacters.length)
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i)
      }
      const byteArray = new Uint8Array(byteNumbers)
      const blob = new Blob([byteArray], { type: 'image/jpeg' })
      
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
      return fileName
    }
    
  } catch (error) {
    console.error('[文件管理] 保存Base64图片失败:', error)
    console.error('[文件管理] 数据URL长度:', dataUrl?.length || 0)
    console.error('[文件管理] 文件名:', fileName)
    console.error('[文件管理] 输出路径:', outputPath)
    throw error
  }
}

// 下载并保存图片到本地 - 通过主进程避免CORS
const downloadAndSaveImage = async (imageUrl, fileName, outputPath) => {
  try {
    console.log('[文件管理] 下载图片:', fileName)
    console.log('[文件管理] 图片URL:', imageUrl)
    
    // 验证URL
    if (!isValidUrl(imageUrl)) {
      throw new Error(`Invalid URL: ${imageUrl}`)
    }
    
    // 如果是Electron环境，通过主进程下载
    if (window.electronApiClient) {
      console.log('[文件管理] 使用主进程下载')
      const result = await window.electronApiClient.fetchUrl(imageUrl)
      if (!result.success) {
        throw new Error(`主进程下载失败: ${result.error}`)
      }
      
      const filePath = `${outputPath}/${fileName}`
      await window.electronAPI.writeFile(filePath, result.data)
      console.log('[文件管理] 图片已保存:', filePath)
      return filePath
    } else {
      // Web环境下使用传统方式
      console.log('[文件管理] 使用Web方式下载')
      const response = await fetch(imageUrl)
      if (!response.ok) {
        throw new Error(`下载失败: ${response.status} ${response.statusText}`)
      }
      
      const blob = await response.blob()
      const arrayBuffer = await blob.arrayBuffer()
      const uint8Array = new Uint8Array(arrayBuffer)
      
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
      return fileName
    }
    
  } catch (error) {
    console.error('[文件管理] 保存图片失败:', error)
    console.error('[文件管理] URL:', imageUrl)
    console.error('[文件管理] 文件名:', fileName)
    console.error('[文件管理] 输出路径:', outputPath)
    throw error
  }
}



// 工具函数：验证URL是否有效
function isValidUrl(string) {
  try {
    // 检查是否为空或非字符串
    if (!string || typeof string !== 'string') {
      return false
    }
    
    // 尝试创建URL对象
    const url = new URL(string)
    
    // 检查协议是否为http或https
    return url.protocol === 'http:' || url.protocol === 'https:'
  } catch (e) {
    return false
  }
}

// 工具函数：检查是否为base64编码的图片数据（简化版）
function isBase64Image(string) {
  if (!string || typeof string !== 'string') {
    return false
  }
  
  // 简化：只要字符串足够长就认为是base64数据
  const minLength = 1000 // base64图片数据通常很长
  
  console.log(`[Base64检测] 字符串长度: ${string.length}, 最小长度要求: ${minLength}`)
  
  return string.length > minLength
}

// 工具函数：将base64数据转换为data URL
function base64ToDataUrl(base64String, mimeType = 'image/jpeg') {
  console.log('[Base64转换] 输入数据长度:', base64String?.length || 0)
  console.log('[Base64转换] 输入数据前50字符:', base64String?.substring(0, 50) || 'N/A')
  
  // 如果已经是data URL格式，直接返回
  if (base64String && base64String.startsWith('data:')) {
    console.log('[Base64转换] 已是data URL格式，直接返回')
    return base64String
  }
  
  // 清理base64字符串（移除可能的换行符、空格等）
  const cleanBase64 = base64String ? base64String.replace(/\s+/g, '') : ''
  
  // 构造data URL
  const dataUrl = `data:${mimeType};base64,${cleanBase64}`
  console.log('[Base64转换] 生成的data URL长度:', dataUrl.length)
  console.log('[Base64转换] 生成的data URL前100字符:', dataUrl.substring(0, 100))
  
  return dataUrl
}

// 工具函数：将File转换为base64
function fileToBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => resolve(reader.result)
    reader.onerror = reject
    reader.readAsDataURL(file)
  })
}



// 使用DeepSeek优化提示词 - 通过主进程避免CORS
async function optimizePrompt(chinesePrompt) {
  try {
    console.log('[DeepSeek] 开始优化提示词:', chinesePrompt)
    
    const result = await window.electronApiClient.deepseekApi('/chat/completions', {
      method: 'POST',
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: [
          {
            role: 'system',
            content: 'You are a professional prompt engineer specializing in image transformation prompts for FLUX models. Convert Chinese prompts into optimized English prompts that describe artistic styles, transformations, and visual effects. Focus on clear, specific descriptions.'
          },
          {
            role: 'user',
            content: `Convert this Chinese prompt into an optimized English prompt for FLUX image transformation: "${chinesePrompt}". Return only the English prompt without explanations.`
          }
        ],
        temperature: 0.7,
        max_tokens: 150
      })
    })

    if (!result.success) {
      throw new Error(result.error)
    }

    const englishPrompt = result.data.choices[0].message.content.trim()
    console.log('[DeepSeek] 优化后的提示词:', englishPrompt)
    return englishPrompt
  } catch (error) {
    console.error('[DeepSeek] 提示词优化失败:', error)
    // 如果优化失败，返回原始提示词
    return chinesePrompt
  }
}

// 计算适合FLUX的aspect_ratio
function calculateAspectRatio(width, height) {
  const ratio = width / height
  
  // 映射到FLUX支持的标准比例
  if (Math.abs(ratio - 1.0) < 0.1) return "1:1"
  if (Math.abs(ratio - 16/9) < 0.1) return "16:9"
  if (Math.abs(ratio - 9/16) < 0.1) return "9:16"
  if (Math.abs(ratio - 4/3) < 0.1) return "4:3"
  if (Math.abs(ratio - 3/4) < 0.1) return "3:4"
  if (Math.abs(ratio - 3/2) < 0.1) return "3:2"
  if (Math.abs(ratio - 2/3) < 0.1) return "2:3"
  if (Math.abs(ratio - 21/9) < 0.1) return "21:9"
  if (Math.abs(ratio - 9/21) < 0.1) return "9:21"
  
  // 默认选择
  return ratio < 1.0 ? "9:16" : "16:9"
}

// 生成批次ID
const generateBatchId = () => {
  return `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

// 生成文件名
const generateFileName = (originalName, seed, outputFormat) => {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0]
  const baseName = originalName.replace(/\.[^/.]+$/, '') // 移除扩展名
  return `converted_${baseName}_${timestamp}_${seed}.${outputFormat}`
}

// 使用FLUX转换单张图片
async function transformImage(imageFile, prompt, options = {}, externalBatchId = null, externalOutputPath = null) {
  console.log('[Flux API] 开始转换图片:', { prompt, options })
  
  try {
    // 优化提示词
    const finalPrompt = options.useDeepseek ? await optimizePrompt(prompt) : prompt
    console.log('[Flux API] 使用提示词:', finalPrompt)
    
    // 转换图片为base64
    const imageBase64 = await fileToBase64(imageFile)
    
    // 获取图片尺寸用于aspect_ratio计算
    const img = new Image()
    const aspectRatio = await new Promise((resolve) => {
      img.onload = () => {
        const ratio = calculateAspectRatio(img.width, img.height)
        resolve(ratio)
      }
      img.src = imageBase64
    })
    
    // 生成随机种子
    const seed = options.seed || Math.floor(Math.random() * 1000000)
    
    // 构建输入参数 - 按照官方示例格式
    const input = {
      prompt: finalPrompt,
      input_image: imageBase64,
      aspect_ratio: options.aspect_ratio || aspectRatio || "match_input_image",
      output_format: options.output_format || "jpg",
      safety_tolerance: options.safety_tolerance ?? 2
    }
    
    // 添加可选参数
    if (seed) {
      input.seed = seed
    }
    
    console.log('[Flux API] 输入参数:', input)
    
    // 按照官方示例格式调用模型
    const result = await window.electronApiClient.replicateRun('black-forest-labs/flux-kontext-pro', input)
    
    if (!result.success) {
      throw new Error(result.error)
    }
    
    const output = result.data
    console.log('[Flux API] ====== 完整输出调试 ======')
    console.log('[Flux API] result对象:', result)
    console.log('[Flux API] result.data类型:', typeof output)
    console.log('[Flux API] result.data内容:', output)
    
    // 如果是字符串，直接输出完整内容
    if (typeof output === 'string') {
      console.log('[Flux API] 字符串长度:', output.length)
      console.log('[Flux API] 完整字符串内容:', output)
    }
    
    // 如果是对象，输出完整结构
    if (typeof output === 'object') {
      console.log('[Flux API] 对象完整结构:', JSON.stringify(output, null, 2))
    }
    
    console.log('[Flux API] ====== 调试结束 ======')
    
    // 处理主进程处理后的输出 - 应该是URL字符串
    let imageData = null
    let isBase64Data = false
    
    console.log('[Flux API] 处理主进程返回的数据...')
    
    if (typeof output === 'string') {
      if (isValidUrl(output)) {
        // 输出是URL字符串（主进程已处理）
        imageData = output
        console.log('[Flux API] ✅ 接收到图片URL:', imageData)
      } else if (output.length > 100) {
        // 输出是base64字符串
        imageData = `data:image/${options.output_format || 'jpeg'};base64,${output}`
        isBase64Data = true
        console.log('[Flux API] ✅ 输出格式: Base64字符串')
      } else {
        console.error('[Flux API] ❌ 字符串格式未知:', output)
      }
    } else {
      console.error('[Flux API] ❌ 期望字符串，实际接收:', typeof output)
      console.error('[Flux API] 输出内容:', output)
      
      return {
        success: false,
        error: '主进程返回了意外的数据格式',
        rawOutput: output,
        outputType: typeof output
      }
    }
    
    // 验证获取到的URL
    if (imageData && isValidUrl(imageData)) {
      console.log('[Flux API] ✅ 图片URL验证通过，准备显示图片')
    } else {
      console.error('[Flux API] ❌ 图片URL验证失败:', imageData)
      return {
        success: false,
        error: 'URL验证失败',
        rawOutput: output,
        imageData: imageData
      }
    }
    
    // 使用外部传入的批次ID和输出路径，或生成新的
    const batchId = externalBatchId || generateBatchId()
    const outputPath = externalOutputPath || await ensureOutputDirectory(batchId)

    // 生成本地文件名
    const fileName = generateFileName(
      imageFile.name,
      seed,
      options.output_format || "jpg"
    )
    
    // 保存图片到本地
    let localPath = null
    try {
      console.log('[文件管理] 准备保存图片，数据类型:', isBase64Data ? 'Base64' : 'URL')
      
      if (isBase64Data) {
        // 处理base64数据
        localPath = await saveBase64Image(
          imageData, 
          fileName, 
          outputPath
        )
      } else {
        // 处理URL数据
        localPath = await downloadAndSaveImage(
          imageData, 
          fileName, 
          outputPath
        )
      }
    } catch (saveError) {
      console.warn('[文件管理] 保存到本地失败，但转换成功:', saveError)
      console.warn('[文件管理] 错误详情:', saveError.stack)
    }
    
    const finalResult = {
      success: true,
      output: imageData, // 图片数据（URL或DataURL）
      localPath: localPath, // 本地路径
      fileName: fileName,
      outputPath: outputPath,
      seed: seed,
      isBase64: isBase64Data // 标识数据类型
    }

    // 只有在单独转换时才保存历史记录（批量转换会统一保存）
    if (!externalBatchId) {
      try {
        const historyData = {
          batchId,
          timestamp: new Date().toISOString(),
          prompt: finalPrompt,
          originalPrompt: prompt,
          options,
          total: 1,
          successful: 1,
          failed: 0,
          images: [{
            originalFileName: imageFile.name,
            fileName: fileName,
            localPath: localPath,
            output: imageData,
            seed: seed
          }]
        }

        if (window.electronAPI) {
          await window.electronAPI.saveConversionHistory(historyData)
        }

        finalResult.batchId = batchId
      } catch (error) {
        console.warn('[历史记录] 保存失败:', error)
      }
    } else {
      finalResult.batchId = batchId
    }

    return finalResult
    
  } catch (error) {
    console.error('[Flux API] 转换失败:', error)
    throw error
  }
}

// 批量转换图片
async function transformImages(imageFiles, prompt, options = {}) {
  console.log('[Flux API] 开始批量转换:', { 
    count: imageFiles.length, 
    prompt, 
    options 
  })
  
  const results = []
  const errors = []
  const batchId = generateBatchId()

  // 优化提示词（只需要优化一次）
  const finalPrompt = options.useDeepseek ? await optimizePrompt(prompt) : prompt
  console.log('[Flux API] 批量转换使用提示词:', finalPrompt)
  
  // 确保输出目录存在（使用批次ID）
  const outputPath = await ensureOutputDirectory(batchId)
  
  // 串行处理以避免API限制
  for (let i = 0; i < imageFiles.length; i++) {
    const file = imageFiles[i]
    
    try {
      console.log(`[Flux API] 转换第 ${i + 1}/${imageFiles.length} 张图片:`, file.name)
      
      const result = await transformImage(file, finalPrompt, {
        ...options,
        useDeepseek: false // 避免重复优化提示词
      }, batchId, outputPath)
      
      results.push({
        ...result,
        originalFileName: file.name,
        index: i,
        batchId
      })
      
      console.log(`[Flux API] 第 ${i + 1} 张图片转换成功`)
      
      // API限制等待
      if (i < imageFiles.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 3000))
      }
      
    } catch (error) {
      console.error(`[Flux API] 第 ${i + 1} 张图片转换失败:`, error)
      errors.push({
        index: i,
        fileName: file.name,
        error: error.message
      })
    }
  }

  // 保存历史记录
  if (results.length > 0) {
    try {
      const historyData = {
        batchId,
        timestamp: new Date().toISOString(),
        prompt: finalPrompt,
        originalPrompt: prompt,
        options,
        total: imageFiles.length,
        successful: results.length,
        failed: errors.length,
        images: results.map(result => ({
          originalFileName: result.originalFileName,
          fileName: result.fileName,
          localPath: result.localPath,
          output: result.output,
          seed: result.seed
        }))
      }

      if (window.electronAPI) {
        await window.electronAPI.saveConversionHistory(historyData)
      }
    } catch (error) {
      console.warn('[历史记录] 保存失败:', error)
    }
  }

  return {
    success: results.length > 0,
    results,
    errors,
    total: imageFiles.length,
    successful: results.length,
    failed: errors.length,
    outputPath: outputPath, // 批量转换的输出目录
    batchId
  }
}

// 文件管理功能
const fileManager = {
  // 打开输出文件夹
  async openOutputFolder(path) {
    if (window.electronAPI) {
      return await window.electronAPI.openFolder(path || getOutputPath())
    } else {
      console.warn('文件夹打开功能仅在桌面版本中可用')
      return false
    }
  },
  
  // 获取今日输出目录
  getTodayOutputPath() {
    return getOutputPath()
  },
  
  // 列出输出目录中的文件
  async listOutputFiles(path) {
    if (window.electronAPI) {
      return await window.electronAPI.listFiles(path || getOutputPath())
    }
    return []
  },
  
  // 清理旧文件（保留最近N天）
  async cleanOldFiles(daysToKeep = 7) {
    if (window.electronAPI) {
      return await window.electronAPI.cleanOldFiles(daysToKeep)
    }
    return false
  }
}

// 历史记录管理功能
const historyManager = {
  // 获取转换历史记录
  async getHistory() {
    if (window.electronAPI) {
      return await window.electronAPI.getConversionHistory()
    }
    return []
  },

  // 删除历史记录
  async deleteHistory(batchId) {
    if (window.electronAPI) {
      return await window.electronAPI.deleteConversionHistory(batchId)
    }
    return false
  },

  // 格式化历史记录显示
  formatHistoryItem(item) {
    const date = new Date(item.timestamp)
    return {
      ...item,
      displayDate: date.toLocaleDateString('zh-CN'),
      displayTime: date.toLocaleTimeString('zh-CN'),
      displayPrompt: item.prompt.length > 50 ?
        item.prompt.substring(0, 50) + '...' :
        item.prompt
    }
  }
}

// 验证API配置 - 检查主进程API可用性
const validateApiConfig = () => {
  const config = {
    replicate: {
      configured: !!window.electronApiClient,
      url: 'Main Process API (CORS-free)'
    },
    deepseek: {
      configured: !!window.electronApiClient,
      url: 'Main Process API (CORS-free)'
    }
  }
  
  console.log('[API配置] 当前配置状态:', config)
  return config
}

export default {
  // 转换单张图片
  async transformImage(imageFile, prompt, options = {}) {
    return transformImage(imageFile, prompt, options)
  },

  // 批量转换图片
  async transformImages(imageFiles, prompt, options = {}) {
    return transformImages(imageFiles, prompt, options)
  },

  // 文件管理
  fileManager,

  // 历史记录管理
  historyManager,

  // 工具方法
  fileToBase64,
  isValidUrl,
  isBase64Image,
  base64ToDataUrl,
  optimizePrompt,
  validateApiConfig
}
