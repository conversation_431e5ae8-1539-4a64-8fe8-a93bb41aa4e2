import '../../styles/loading-indicator.css'
import PropTypes from 'prop-types'

function LoadingIndicator({ size = 'medium', text = '加载中...' }) {
  const sizeClass = {
    small: 'loading-indicator-small',
    medium: 'loading-indicator-medium',
    large: 'loading-indicator-large',
  }[size] || 'loading-indicator-medium'

  return (
    <div className="loading-indicator">
      <div className={`loading-spinner ${sizeClass}`}>
        <div className="spinner-dot"></div>
        <div className="spinner-dot"></div>
        <div className="spinner-dot"></div>
      </div>
      {text && <div className="loading-text">{text}</div>}
    </div>
  )
}

LoadingIndicator.propTypes = {
  size: PropTypes.oneOf(['small', 'medium', 'large']),
  text: PropTypes.string
}

export default LoadingIndicator
