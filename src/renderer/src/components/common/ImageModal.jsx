import { useEffect } from 'react'
import PropTypes from 'prop-types'
import '../../styles/ImageModal.css'

function ImageModal({ image, alt, onClose }) {
  // 按ESC关闭modal
  useEffect(() => {
    const handleEsc = (event) => {
      if (event.key === 'Escape') {
        onClose()
      }
    }
    window.addEventListener('keydown', handleEsc)
    return () => {
      window.removeEventListener('keydown', handleEsc)
    }
  }, [onClose])

  return (
    <div className="image-modal-overlay" onClick={onClose}>
      <div className="image-modal-content" onClick={e => e.stopPropagation()}>
        <button className="image-modal-close" onClick={onClose}>×</button>
        <img src={image} alt={alt} className="image-modal-image" />
      </div>
    </div>
  )
}

ImageModal.propTypes = {
  image: PropTypes.string.isRequired,
  alt: PropTypes.string,
  onClose: PropTypes.func.isRequired
}

export default ImageModal 