import { useState } from 'react'
import api from '../../services/api'

function ApiDebugger() {
  const [result, setResult] = useState(null)
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('你好')

  const testApi = async () => {
    setLoading(true)
    try {
      // 测试文本消息API
      const response = await api.sendTextMessage(message)
      console.log('API 测试响应:', response)
      setResult(response)
    } catch (error) {
      console.error('API 测试错误:', error)
      setResult({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const prettifyJson = (obj) => {
    return JSON.stringify(obj, null, 2)
  }

  return (
    <div style={{ 
      position: 'fixed', 
      bottom: '10px', 
      right: '10px', 
      zIndex: 1000,
      background: 'rgba(0,0,0,0.7)',
      color: '#0f0',
      padding: '10px',
      borderRadius: '5px',
      maxWidth: '500px',
      maxHeight: '400px',
      overflow: 'auto',
      fontFamily: 'monospace',
      fontSize: '12px'
    }}>
      <h3 style={{ margin: '0 0 8px 0', color: '#fff' }}>API调试</h3>
      <div style={{ display: 'flex', marginBottom: '10px' }}>
        <input 
          type="text" 
          value={message} 
          onChange={(e) => setMessage(e.target.value)}
          style={{ 
            flex: 1, 
            background: '#222', 
            color: '#fff',
            border: '1px solid #444',
            borderRadius: '3px',
            padding: '5px'
          }}
        />
        <button 
          onClick={testApi} 
          disabled={loading}
          style={{
            marginLeft: '5px',
            background: '#444',
            color: '#fff',
            border: 'none',
            borderRadius: '3px',
            padding: '5px 10px'
          }}
        >
          {loading ? '请求中...' : '测试API'}
        </button>
      </div>
      
      {result && (
        <pre style={{ margin: 0 }}>
          {prettifyJson(result)}
        </pre>
      )}
    </div>
  )
}

export default ApiDebugger
