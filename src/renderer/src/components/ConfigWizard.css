.config-wizard-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  backdrop-filter: blur(4px);
}

.config-wizard {
  background: white;
  border-radius: 16px;
  padding: 32px;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.config-wizard-header {
  text-align: center;
  margin-bottom: 24px;
}

.config-wizard-header h2 {
  color: #2c3e50;
  margin: 0 0 8px 0;
  font-size: 24px;
}

.config-wizard-header p {
  color: #7f8c8d;
  margin: 0;
  font-size: 14px;
}

.config-status {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
}

.config-status h3 {
  margin: 0 0 12px 0;
  color: #2c3e50;
  font-size: 16px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.status-item:last-child {
  margin-bottom: 0;
}

.status-label {
  font-weight: 500;
  color: #34495e;
}

.status-value {
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.status-value.configured {
  background: #d4edda;
  color: #155724;
}

.status-value.missing {
  background: #f8d7da;
  color: #721c24;
}

.config-instructions {
  margin-bottom: 24px;
}

.config-instructions h3 {
  color: #2c3e50;
  margin: 0 0 16px 0;
  font-size: 16px;
}

.config-instructions ol {
  padding-left: 20px;
  color: #34495e;
}

.config-instructions li {
  margin-bottom: 12px;
  line-height: 1.5;
}

.config-instructions a {
  color: #3498db;
  text-decoration: none;
  word-break: break-all;
}

.config-instructions a:hover {
  text-decoration: underline;
}

.config-instructions code {
  background: #f1f2f6;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 12px;
}

.config-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.config-path {
  background: #f8f9fa;
  padding: 8px 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  border-left: 3px solid #3498db;
}

.config-path small {
  color: #6c757d;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 11px;
  word-break: break-all;
}

.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 16px;
  border-left: 3px solid #dc3545;
}

.config-wizard-footer {
  text-align: center;
  padding-top: 16px;
  border-top: 1px solid #e9ecef;
}

.config-warning {
  color: #856404;
  background: #fff3cd;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 16px;
  border-left: 3px solid #ffc107;
  font-size: 14px;
}

/* 按钮样式 */
.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2980b9;
  transform: translateY(-1px);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #5a6268;
  transform: translateY(-1px);
}

.btn-success {
  background: #28a745;
  color: white;
  font-size: 16px;
  padding: 12px 24px;
}

.btn-success:hover:not(:disabled) {
  background: #218838;
  transform: translateY(-1px);
}

.btn-text {
  background: transparent;
  color: #6c757d;
  padding: 8px 16px;
}

.btn-text:hover:not(:disabled) {
  color: #495057;
  background: #f8f9fa;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .config-wizard {
    padding: 24px;
    margin: 16px;
    width: calc(100% - 32px);
  }
  
  .config-actions {
    flex-direction: column;
  }
  
  .btn {
    width: 100%;
    justify-content: center;
  }
}
