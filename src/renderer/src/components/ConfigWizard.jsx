import React, { useState, useEffect } from 'react'
import './ConfigWizard.css'

/**
 * 配置向导组件
 * 用于首次运行时或API配置缺失时引导用户配置
 */
function ConfigWizard({ onConfigComplete, onClose }) {
  const [config, setConfig] = useState({
    replicateToken: '',
    deepseekKey: ''
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [currentConfig, setCurrentConfig] = useState(null)

  // 检查当前配置
  useEffect(() => {
    checkCurrentConfig()
  }, [])

  const checkCurrentConfig = async () => {
    try {
      if (window.electronApiClient) {
        const configInfo = await window.electronApiClient.checkApiConfig()
        setCurrentConfig(configInfo)
        
        // 如果已经配置了，预填充表单
        if (configInfo.replicateConfigured) {
          setConfig(prev => ({ ...prev, replicateToken: '已配置' }))
        }
        if (configInfo.deepseekConfigured) {
          setConfig(prev => ({ ...prev, deepseekKey: '已配置' }))
        }
      }
    } catch (error) {
      console.error('检查配置失败:', error)
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      // 这里需要实现保存配置的逻辑
      // 由于安全原因，我们不能直接从渲染进程写入文件
      // 需要通过主进程来处理
      
      if (window.electronApiClient) {
        const result = await window.electronApiClient.reloadEnvConfig()
        if (result.success) {
          onConfigComplete && onConfigComplete()
        } else {
          setError(`配置失败: ${result.error}`)
        }
      } else {
        setError('无法访问系统配置功能')
      }
    } catch (error) {
      setError(`配置过程中出错: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const openConfigFile = async () => {
    if (currentConfig && currentConfig.configPath) {
      try {
        // 打开配置文件所在的文件夹
        const folderPath = currentConfig.configPath.replace(/\/[^/]*$/, '')
        await window.electronAPI.openFolder(folderPath)
      } catch (error) {
        console.error('打开配置文件夹失败:', error)
        setError('无法打开配置文件夹')
      }
    }
  }

  return (
    <div className="config-wizard-overlay">
      <div className="config-wizard">
        <div className="config-wizard-header">
          <h2>🔧 API配置向导</h2>
          <p>请配置必要的API密钥以使用图片转换功能</p>
        </div>

        <div className="config-status">
          <h3>当前配置状态</h3>
          <div className="status-item">
            <span className="status-label">Replicate API:</span>
            <span className={`status-value ${currentConfig?.replicateConfigured ? 'configured' : 'missing'}`}>
              {currentConfig?.replicateConfigured ? '✅ 已配置' : '❌ 未配置'}
            </span>
          </div>
          <div className="status-item">
            <span className="status-label">DeepSeek API:</span>
            <span className={`status-value ${currentConfig?.deepseekConfigured ? 'configured' : 'missing'}`}>
              {currentConfig?.deepseekConfigured ? '✅ 已配置' : '⚠️ 未配置 (可选)'}
            </span>
          </div>
        </div>

        <div className="config-instructions">
          <h3>配置步骤</h3>
          <ol>
            <li>
              <strong>获取 Replicate API Token (必需)</strong>
              <br />
              访问 <a href="https://replicate.com/account/api-tokens" target="_blank" rel="noopener noreferrer">
                https://replicate.com/account/api-tokens
              </a> 获取您的API Token
            </li>
            <li>
              <strong>获取 DeepSeek API Key (可选)</strong>
              <br />
              访问 <a href="https://platform.deepseek.com/api_keys" target="_blank" rel="noopener noreferrer">
                https://platform.deepseek.com/api_keys
              </a> 获取您的API Key (用于提示词优化)
            </li>
            <li>
              <strong>编辑配置文件</strong>
              <br />
              点击下方按钮打开配置文件夹，编辑 <code>.env</code> 文件
            </li>
          </ol>
        </div>

        <div className="config-actions">
          <button 
            type="button" 
            onClick={openConfigFile}
            className="btn btn-secondary"
            disabled={!currentConfig?.configPath}
          >
            📁 打开配置文件夹
          </button>
          
          <button 
            type="button" 
            onClick={checkCurrentConfig}
            className="btn btn-primary"
            disabled={loading}
          >
            🔄 重新检查配置
          </button>
        </div>

        {currentConfig?.configPath && (
          <div className="config-path">
            <small>配置文件路径: {currentConfig.configPath}</small>
          </div>
        )}

        {error && (
          <div className="error-message">
            ❌ {error}
          </div>
        )}

        <div className="config-wizard-footer">
          {currentConfig?.replicateConfigured ? (
            <button 
              onClick={() => onConfigComplete && onConfigComplete()}
              className="btn btn-success"
            >
              ✅ 配置完成，开始使用
            </button>
          ) : (
            <p className="config-warning">
              ⚠️ 需要配置 Replicate API Token 才能使用转换功能
            </p>
          )}
          
          {onClose && (
            <button 
              onClick={onClose}
              className="btn btn-text"
            >
              稍后配置
            </button>
          )}
        </div>
      </div>
    </div>
  )
}

export default ConfigWizard
