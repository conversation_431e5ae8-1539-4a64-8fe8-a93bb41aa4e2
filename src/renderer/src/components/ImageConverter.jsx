import { useState, useRef, useEffect } from 'react'
import PropTypes from 'prop-types'
import api from '../services/api'
import LoadingIndicator from './common/LoadingIndicator'
import ImageModal from './common/ImageModal'
import HistoryViewer from './HistoryViewer'

function ImageConverter() {
  // 核心状态
  const [images, setImages] = useState([])
  const [prompt, setPrompt] = useState('Transform this into Studio Ghibli anime style, maintain temporal continuity and smooth transitions')
  const [processing, setProcessing] = useState(false)
  const [results, setResults] = useState([])
  const [selectedImage, setSelectedImage] = useState(null)
  
  // 转换参数 - 完全匹配 convert_video.py 脚本的专业配置
  const [useDeepseek, setUseDeepseek] = useState(true)
  const [aspectRatio, setAspectRatio] = useState('match_input_image')
  const [outputFormat, setOutputFormat] = useState('jpg') // 匹配脚本默认
  const [safetyTolerance, setSafetyTolerance] = useState(2)
  const [seed, setSeed] = useState('42') // 使用脚本的base_seed
  
  // 高级参数 - 来自 convert_video.py 脚本
  const [guidanceScale, setGuidanceScale] = useState(3.5) // 降低引导强度，保持一致性
  const [numInferenceSteps, setNumInferenceSteps] = useState(25) // 适中的推理步数
  const [stylePreset, setStylePreset] = useState('ghibli') // 预定义风格
  const [enableFrameContinuity, setEnableFrameContinuity] = useState(true) // 帧间连续性
  // 移除高级设置切换功能，始终显示所有配置
  const [apiConfig, setApiConfig] = useState(null)
  
  // 统计状态
  const [stats, setStats] = useState({
    total: 0,
    successful: 0,
    failed: 0,
    processing: 0
  })
  
  // 文件管理状态
  const [outputPath, setOutputPath] = useState('')
  const [showFileManager, setShowFileManager] = useState(false)
  const [showHistoryViewer, setShowHistoryViewer] = useState(false)
  
  const fileInputRef = useRef(null)
  
  // 检查API配置状态
  useEffect(() => {
    const checkApiConfig = () => {
      const config = api.validateApiConfig()
      setApiConfig(config)
    }
    checkApiConfig()
  }, [])
  
  // 文件选择处理
  const handleFileSelect = (event) => {
    const files = Array.from(event.target.files)
    const imageFiles = files.filter(file => file.type.startsWith('image/'))
    
    if (imageFiles.length === 0) {
      alert('请选择图片文件')
      return
    }
    
    setImages(imageFiles)
    setResults([]) // 清空之前的结果
    
    console.log('选择的图片:', imageFiles.map(f => f.name))
  }
  
  // 移除图片
  const removeImage = (index) => {
    setImages(prev => prev.filter((_, i) => i !== index))
    setResults(prev => prev.filter(r => r.index !== index))
  }
  
  // 清空所有图片
  const clearImages = () => {
    setImages([])
    setResults([])
    setStats({ total: 0, successful: 0, failed: 0, processing: 0 })
  }
  
  // 转换处理
  const handleConvert = async () => {
    if (images.length === 0) {
      alert('请先选择图片')
      return
    }
    
    if (stylePreset === 'custom' && !prompt.trim()) {
      alert('请输入自定义转换提示词')
      return
    }
    
    setProcessing(true)
    setResults([])
    setStats({
      total: images.length,
      successful: 0,
      failed: 0,
      processing: images.length
    })
    
    try {
      // 使用最终提示词（可能是预设风格或自定义）
      const finalPrompt = getFinalPrompt()
      
      const options = {
        useDeepseek: stylePreset === 'custom' ? useDeepseek : false, // 预设风格不需要优化
        aspect_ratio: aspectRatio === 'match_input_image' ? undefined : aspectRatio,
        output_format: outputFormat,
        safety_tolerance: safetyTolerance,
        guidance_scale: guidanceScale, // 新增脚本参数
        num_inference_steps: numInferenceSteps, // 新增脚本参数
        seed: seed ? parseInt(seed) : undefined,
        enable_frame_continuity: enableFrameContinuity
      }
      
      if (images.length === 1) {
        // 单张图片转换
        console.log('开始单张图片转换')
        const result = await api.transformImage(images[0], finalPrompt, options)
        
        setResults([{
          ...result,
          originalFileName: images[0].name,
          index: 0
        }])
        
        setStats({
          total: 1,
          successful: 1,
          failed: 0,
          processing: 0
        })
        
        // 设置输出路径
        if (result.outputPath) {
          setOutputPath(result.outputPath)
        }
        
      } else {
        // 批量转换
        console.log('开始批量转换')
        const batchResult = await api.transformImages(images, finalPrompt, options)
        
        setResults(batchResult.results)
        setStats({
          total: batchResult.total,
          successful: batchResult.successful,
          failed: batchResult.failed,
          processing: 0
        })
        
        // 设置输出路径
        if (batchResult.outputPath) {
          setOutputPath(batchResult.outputPath)
        }
        
        if (batchResult.errors.length > 0) {
          console.error('转换错误:', batchResult.errors)
        }
      }
      
    } catch (error) {
      console.error('转换失败:', error)
      alert(`转换失败: ${error.message}`)
      setStats(prev => ({
        ...prev,
        processing: 0,
        failed: prev.total - prev.successful
      }))
    } finally {
      setProcessing(false)
    }
  }
  
  // 下载结果
  const downloadResult = (result) => {
    try {
      const link = document.createElement('a')
      link.href = result.output
      link.download = `converted_${result.originalFileName}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    } catch (error) {
      console.error('下载失败:', error)
      alert('下载失败')
    }
  }
  
  // 下载所有结果
  const downloadAll = () => {
    results.forEach((result, index) => {
      setTimeout(() => downloadResult(result), index * 100)
    })
  }
  
  // 打开输出文件夹
  const openOutputFolder = async () => {
    try {
      const success = await api.fileManager.openOutputFolder(outputPath)
      if (!success) {
        alert('打开文件夹失败，可能不在桌面版本中运行')
      }
    } catch (error) {
      console.error('打开文件夹失败:', error)
      alert('打开文件夹失败')
    }
  }

  // 处理历史记录选择
  const handleHistorySelect = (batch) => {
    // 恢复历史批次的设置
    setPrompt(batch.originalPrompt)

    // 恢复选项设置
    if (batch.options) {
      if (batch.options.aspect_ratio) setAspectRatio(batch.options.aspect_ratio)
      if (batch.options.output_format) setOutputFormat(batch.options.output_format)
      if (batch.options.safety_tolerance !== undefined) setSafetyTolerance(batch.options.safety_tolerance)
      if (batch.options.guidance_scale !== undefined) setGuidanceScale(batch.options.guidance_scale)
      if (batch.options.num_inference_steps !== undefined) setNumInferenceSteps(batch.options.num_inference_steps)
      if (batch.options.enable_frame_continuity !== undefined) setEnableFrameContinuity(batch.options.enable_frame_continuity)
      if (batch.options.useDeepseek !== undefined) setUseDeepseek(batch.options.useDeepseek)
    }

    // 显示历史图片作为结果
    const historyResults = batch.images.map((image, index) => ({
      ...image,
      index,
      batchId: batch.batchId
    }))

    setResults(historyResults)
    setStats({
      total: batch.total,
      successful: batch.successful,
      failed: batch.failed,
      processing: 0
    })

    alert(`已加载历史批次：${batch.successful}/${batch.total} 张图片`)
  }
  
  // 获取今日输出路径显示
  const getTodayOutputPath = () => {
    return api.fileManager.getTodayOutputPath()
  }
  
  // 清理旧文件
  const cleanOldFiles = async () => {
    if (confirm('确定要清理7天前的旧文件吗？此操作不可撤销。')) {
      try {
        const success = await api.fileManager.cleanOldFiles(7)
        if (success) {
          alert('清理完成')
        } else {
          alert('清理失败')
        }
      } catch (error) {
        console.error('清理失败:', error)
        alert('清理失败')
      }
    }
  }
  
  // 预定义风格提示词 - 来自 convert_video.py 脚本
  const stylePrompts = {
    ghibli: "Transform this into Studio Ghibli anime style, maintain high quality and artistic consistency, Hayao Miyazaki art style, soft watercolor painting, magical atmosphere, hand-drawn animation feel, whimsical details, preserve temporal continuity, beautiful composition, ensure natural seamless edges without visible borders or white lines, blend all areas smoothly",
    shinkai: "Convert this to Makoto Shinkai anime style, maintain visual consistency and high quality, Your Name movie aesthetic, beautiful detailed sky, dramatic lighting, photorealistic anime, cinematic composition, preserve character identity and scene continuity, ensure seamless borders and natural edge transitions, no white or harsh boundaries",
    anime: "Make this into high quality anime style, maintain consistent art style across sequence, cel shading, vibrant colors, traditional Japanese animation, detailed character art, clean lines, preserve temporal flow and character consistency, ensure smooth edge transitions and natural borders without white edges or visible seams",
    realistic: "Transform into photorealistic style, maintain high quality and natural appearance, professional photography, cinematic lighting, detailed textures, preserve temporal continuity, ensure natural edge blending and seamless transitions, avoid any artificial borders or white edges",
    oil_painting: "Convert to oil painting style, artistic brushstrokes, rich colors, traditional art technique, maintain composition and temporal flow, blend edges naturally with painterly transitions, avoid hard borders or white edges, ensure seamless artistic continuity"
  }
  
  // 获取最终提示词
  const getFinalPrompt = () => {
    if (stylePreset !== 'custom') {
      const basePrompt = stylePrompts[stylePreset]
      if (enableFrameContinuity && images.length > 1) {
        return `${basePrompt}, maintain consistency across video frames`
      }
      return basePrompt
    }
    return prompt
  }
  
  // 生成连续性种子
  const generateConsistentSeed = (frameIndex, baseSeed = 42) => {
    // 每5帧使用相同基础种子，模仿脚本逻辑
    const seedGroup = Math.floor(frameIndex / 5)
    return parseInt(baseSeed) + seedGroup
  }
  
  return (
    <div className="image-converter">
      <div className="converter-header">
        <h1>视频帧图像风格转换工具</h1>
        <p>5种预设卡通风格 | 完美的视频帧连续性</p>
        
        {/* API配置状态 */}
        {/* {apiConfig && (
          <div className="api-status">
            <div className="status-item">
              <span className={`status-indicator ${apiConfig.replicate.configured ? 'connected' : 'error'}`}>
                {apiConfig.replicate.configured ? '✅' : '❌'}
              </span>
              <span>Replicate API: {apiConfig.replicate.configured ? '已配置' : '未配置'}</span>
            </div>
            <div className="status-item">
              <span className={`status-indicator ${apiConfig.deepseek.configured ? 'connected' : 'warning'}`}>
                {apiConfig.deepseek.configured ? '✅' : '⚠️'}
              </span>
              <span>DeepSeek API: {apiConfig.deepseek.configured ? '已配置' : '未配置 (可选)'}</span>
            </div>
          </div>
        )} */}
      </div>
      
      <div className="converter-content">
        {/* 左侧：输入区域 */}
        <div className="input-section">
          <div className="input-group">
            <label>选择图片</label>
            <div className="file-input-wrapper">
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileSelect}
                multiple
                accept="image/*"
                style={{ display: 'none' }}
              />
              <button 
                className="file-select-btn"
                onClick={() => fileInputRef.current?.click()}
                disabled={processing}
              >
                {images.length === 0 ? '选择图片' : `已选择 ${images.length} 张图片`}
              </button>
              {images.length > 0 && (
                <button 
                  className="clear-btn"
                  onClick={clearImages}
                  disabled={processing}
                >
                  清空
                </button>
              )}
            </div>
          </div>
          
          {/* 图片预览 */}
          {images.length > 0 && (
            <div className="image-preview-section">
              <div className="preview-grid">
                {images.map((image, index) => (
                  <div key={index} className="preview-item">
                    <img
                      src={URL.createObjectURL(image)}
                      alt={image.name}
                      className="preview-image"
                    />
                    <div className="preview-info">
                      <span className="image-name">{image.name}</span>
                      <button
                        className="remove-btn"
                        onClick={() => removeImage(index)}
                        disabled={processing}
                      >
                        ×
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
          
          {/* 风格选择 */}
          <div className="input-group">
            <label>转换风格</label>
            <select
              value={stylePreset}
              onChange={(e) => setStylePreset(e.target.value)}
              disabled={processing}
            >
              <option value="ghibli">🎨 宫崎骏/吉卜力工作室风格 (推荐)</option>
              <option value="shinkai">🌸 新海诚风格 (唯美)</option>
              <option value="anime">📺 传统动漫风格</option>
              <option value="realistic">📷 写实风格</option>
              <option value="oil_painting">🖼️ 油画风格</option>
              <option value="custom">✏️ 自定义提示词</option>
            </select>
          </div>

          {/* 自定义提示词输入 */}
          {stylePreset === 'custom' && (
            <div className="input-group">
              <label>自定义转换提示词</label>
              <textarea
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                placeholder="描述你想要的转换效果，例如：转换为动漫风格，保持帧间连续性"
                rows={3}
                disabled={processing}
              />
            </div>
          )}

          {/* 预设风格预览 */}
          {stylePreset !== 'custom' && (
            <div className="style-preview">
              <label>当前风格预设:</label>
              <div className="style-prompt-preview">
                {stylePrompts[stylePreset]?.substring(0, 100)}...
              </div>
            </div>
          )}
          
          {/* 参数设置 */}
          <div className="params-section">
            <h3>转换参数 (匹配 convert_video.py 脚本)</h3>
            
            {/* 帧间连续性设置 */}
            <div className="param-row simple">
              <label>
                <input
                  type="checkbox"
                  checked={enableFrameContinuity}
                  onChange={(e) => setEnableFrameContinuity(e.target.checked)}
                  disabled={processing}
                />
                启用帧间连续性优化 (视频帧必备)
              </label>
            </div>
            
            {/* 自定义风格时才显示DeepSeek优化 */}
            {stylePreset === 'custom' && (
              <div className="param-row simple">
                <label>
                  <input
                    type="checkbox"
                    checked={useDeepseek}
                    onChange={(e) => setUseDeepseek(e.target.checked)}
                    disabled={processing}
                  />
                  使用DeepSeek优化提示词
                </label>
              </div>
            )}
            
            <div className="param-row has-control">
              <label>宽高比</label>
              <div className="param-control-row">
                <select
                  value={aspectRatio}
                  onChange={(e) => setAspectRatio(e.target.value)}
                  disabled={processing}
                >
                  <option value="match_input_image">匹配输入图像</option>
                  <option value="1:1">1:1 (正方形)</option>
                  <option value="16:9">16:9 (横屏)</option>
                  <option value="9:16">9:16 (竖屏)</option>
                  <option value="4:3">4:3</option>
                  <option value="3:4">3:4</option>
                  <option value="21:9">21:9 (超宽屏)</option>
                </select>
              </div>
            </div>
            
            <div className="param-row has-control">
              <label>输出格式</label>
              <div className="param-control-row">
                <select
                  value={outputFormat}
                  onChange={(e) => setOutputFormat(e.target.value)}
                  disabled={processing}
                >
                  <option value="jpg">JPG</option>
                  <option value="png">PNG</option>
                  <option value="webp">WebP</option>
                </select>
              </div>
            </div>
            
            <div className="param-row has-control">
              <label>安全容忍度 (0-6)</label>
              <div className="param-control-row">
                <div className="param-input-group">
                  <input
                    type="range"
                    min="0"
                    max="6"
                    value={safetyTolerance}
                    onChange={(e) => setSafetyTolerance(parseInt(e.target.value))}
                    disabled={processing}
                  />
                  <span className="param-value">{safetyTolerance}</span>
                </div>
              </div>
            </div>
            
            <div className="param-row has-control">
              <label>基础种子 (连续性的关键)</label>
              <div className="param-control-row">
                <input
                  type="number"
                  value={seed}
                  onChange={(e) => setSeed(e.target.value)}
                  placeholder="脚本默认42，建议使用固定值"
                  disabled={processing}
                />
              </div>
            </div>
            
            {/* 高级参数设置 - 始终显示 */}
            <div className="advanced-params">
              <div className="param-divider">
                <span>高级参数 (来自脚本优化)</span>
              </div>
              
              <div className="param-row has-control">
                <label>引导强度 (Guidance Scale)</label>
                <div className="param-control-row">
                  <div className="param-input-group">
                    <input
                      type="range"
                      min="1"
                      max="10"
                      step="0.1"
                      value={guidanceScale}
                      onChange={(e) => setGuidanceScale(parseFloat(e.target.value))}
                      disabled={processing}
                    />
                    <span className="param-value">{guidanceScale}</span>
                  </div>
                </div>
                <div className="param-description">
                  脚本默认3.5，降低引导强度保持帧间一致性
                </div>
              </div>
              
              <div className="param-row has-control">
                <label>推理步数 (Inference Steps)</label>
                <div className="param-control-row">
                  <div className="param-input-group">
                    <input
                      type="range"
                      min="10"
                      max="50"
                      value={numInferenceSteps}
                      onChange={(e) => setNumInferenceSteps(parseInt(e.target.value))}
                      disabled={processing}
                    />
                    <span className="param-value">{numInferenceSteps}</span>
                  </div>
                </div>
                <div className="param-description">
                  脚本默认25步，适中的推理步数平衡质量和速度
                </div>
              </div>
            </div>
          </div>
          
          {/* 转换按钮 */}
          <button
            className="convert-btn"
            onClick={handleConvert}
            disabled={processing || images.length === 0 || (stylePreset === 'custom' && !prompt.trim())}
          >
            {processing ? '转换中...' : `转换 ${images.length} 张图片`}
          </button>
        </div>
        
        {/* 右侧：结果区域 */}
        <div className="results-section">
          <div className="results-header">
            <h3>转换结果</h3>
            {stats.total > 0 && (
              <div className="stats">
                <span>总计: {stats.total}</span>
                <span>成功: {stats.successful}</span>
                <span>失败: {stats.failed}</span>
                {stats.processing > 0 && <span>处理中: {stats.processing}</span>}
              </div>
            )}
            <div className="results-actions">
              {results.length > 0 && (
                <>
                  <button className="download-all-btn" onClick={downloadAll}>
                    下载全部
                  </button>
                  <button className="open-folder-btn" onClick={openOutputFolder}>
                    📁 打开文件夹
                  </button>
                </>
              )}
              <button
                className="file-manager-btn"
                onClick={() => setShowFileManager(!showFileManager)}
              >
                🛠️ 文件管理
              </button>
              <button
                className="history-btn"
                onClick={() => setShowHistoryViewer(true)}
              >
                📚 历史记录
              </button>
            </div>
          </div>
          
          {/* 文件管理面板 */}
          {showFileManager && (
            <div className="file-manager-panel">
              <div className="file-info">
                <h4>文件存储信息</h4>
                <div className="path-info">
                  <label>今日输出目录:</label>
                  <div className="path-display">
                    <code>{getTodayOutputPath()}</code>
                    <button className="copy-path-btn" onClick={() => {
                      navigator.clipboard.writeText(getTodayOutputPath())
                      alert('路径已复制到剪贴板')
                    }}>
                      📋
                    </button>
                  </div>
                </div>
                {outputPath && (
                  <div className="path-info">
                    <label>当前批次目录:</label>
                    <div className="path-display">
                      <code>{outputPath}</code>
                      <button className="copy-path-btn" onClick={() => {
                        navigator.clipboard.writeText(outputPath)
                        alert('路径已复制到剪贴板')
                      }}>
                        📋
                      </button>
                    </div>
                  </div>
                )}
              </div>
              <div className="file-actions">
                <button className="clean-btn" onClick={cleanOldFiles}>
                  🗑️ 清理旧文件
                </button>
                <button className="open-folder-btn" onClick={openOutputFolder}>
                  📁 打开当前文件夹
                </button>
              </div>
            </div>
          )}
          
          {processing && (
            <div className="processing-indicator">
              <LoadingIndicator size="large" text="正在转换图片..." />
            </div>
          )}
          
          {results.length > 0 && (
            <div className="results-grid">
              {results.map((result, index) => (
                <div key={index} className="result-item">
                  <div className="result-image-container">
                    <img
                      src={result.output}
                      alt={`转换结果 ${index + 1}`}
                      className="result-image"
                      onClick={() => setSelectedImage(result.output)}
                    />
                  </div>
                  <div className="result-info">
                    <div className="result-name">{result.originalFileName}</div>
                    <div className="result-meta">
                      <div>种子: {result.seed}</div>
                      {result.fileName && (
                        <div className="local-file">
                          📄 {result.fileName}
                        </div>
                      )}
                      {result.localPath && (
                        <div className="local-status">
                          ✅ 已保存到本地
                        </div>
                      )}
                    </div>
                    <div className="result-actions">
                      <button
                        className="download-btn"
                        onClick={() => downloadResult(result)}
                        title="从远程URL下载"
                      >
                        🔗 远程下载
                      </button>
                      {result.localPath && (
                        <button
                          className="open-local-btn"
                          onClick={() => {
                            // 复制本地路径到剪贴板
                            navigator.clipboard.writeText(result.localPath)
                            alert('本地路径已复制到剪贴板')
                          }}
                          title="复制本地路径"
                        >
                          📋 复制路径
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
          
          {results.length === 0 && !processing && (
            <div className="empty-results">
              <p>选择图片并点击转换开始使用</p>
            </div>
          )}
        </div>
      </div>
      
      {/* 图片预览模态框 */}
      {selectedImage && (
        <ImageModal
          src={selectedImage}
          alt="转换结果预览"
          onClose={() => setSelectedImage(null)}
        />
      )}

      {/* 历史记录查看器 */}
      {showHistoryViewer && (
        <HistoryViewer
          onClose={() => setShowHistoryViewer(false)}
          onSelectBatch={handleHistorySelect}
        />
      )}
    </div>
  )
}

ImageConverter.propTypes = {}

export default ImageConverter 