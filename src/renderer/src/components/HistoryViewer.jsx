import React, { useState, useEffect } from 'react'
import PropTypes from 'prop-types'
import api from '../services/api'
import './HistoryViewer.css'

function HistoryViewer({ onClose, onSelectBatch }) {
  const [history, setHistory] = useState([])
  const [loading, setLoading] = useState(true)
  const [selectedBatch, setSelectedBatch] = useState(null)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(null)

  // 加载历史记录
  useEffect(() => {
    loadHistory()
  }, [])

  const loadHistory = async () => {
    try {
      setLoading(true)
      const historyData = await api.historyManager.getHistory()
      const formattedHistory = historyData.map(item => 
        api.historyManager.formatHistoryItem(item)
      )
      setHistory(formattedHistory)
    } catch (error) {
      console.error('加载历史记录失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 删除历史记录
  const deleteHistory = async (batchId) => {
    try {
      const success = await api.historyManager.deleteHistory(batchId)
      if (success) {
        setHistory(prev => prev.filter(item => item.batchId !== batchId))
        setShowDeleteConfirm(null)
      } else {
        alert('删除失败')
      }
    } catch (error) {
      console.error('删除历史记录失败:', error)
      alert('删除失败')
    }
  }

  // 查看批次详情
  const viewBatchDetails = (batch) => {
    setSelectedBatch(batch)
  }

  // 使用历史批次
  const useBatch = (batch) => {
    if (onSelectBatch) {
      onSelectBatch(batch)
    }
    onClose()
  }

  if (loading) {
    return (
      <div className="history-viewer-overlay">
        <div className="history-viewer">
          <div className="loading">加载历史记录中...</div>
        </div>
      </div>
    )
  }

  return (
    <div className="history-viewer-overlay">
      <div className="history-viewer">
        <div className="history-header">
          <h2>转换历史记录</h2>
          <button className="close-btn" onClick={onClose}>✕</button>
        </div>

        {history.length === 0 ? (
          <div className="empty-history">
            <p>暂无转换历史记录</p>
          </div>
        ) : (
          <div className="history-content">
            <div className="history-list">
              {history.map((item) => (
                <div key={item.batchId} className="history-item">
                  <div className="history-item-header">
                    <div className="history-meta">
                      <span className="history-date">{item.displayDate} {item.displayTime}</span>
                      <span className="history-stats">
                        {item.successful}/{item.total} 张成功
                      </span>
                    </div>
                    <div className="history-actions">
                      <button 
                        className="view-btn"
                        onClick={() => viewBatchDetails(item)}
                      >
                        查看详情
                      </button>
                      <button 
                        className="use-btn"
                        onClick={() => useBatch(item)}
                      >
                        重新使用
                      </button>
                      <button 
                        className="delete-btn"
                        onClick={() => setShowDeleteConfirm(item.batchId)}
                      >
                        删除
                      </button>
                    </div>
                  </div>
                  <div className="history-prompt">
                    <strong>提示词:</strong> {item.displayPrompt}
                  </div>
                  <div className="history-images-preview">
                    {item.images.slice(0, 4).map((image, index) => (
                      <div key={index} className="preview-image">
                        <img 
                          src={image.output} 
                          alt={image.originalFileName}
                          onError={(e) => {
                            e.target.style.display = 'none'
                          }}
                        />
                      </div>
                    ))}
                    {item.images.length > 4 && (
                      <div className="more-images">
                        +{item.images.length - 4}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 批次详情模态框 */}
        {selectedBatch && (
          <div className="batch-detail-overlay">
            <div className="batch-detail">
              <div className="batch-detail-header">
                <h3>批次详情</h3>
                <button 
                  className="close-btn"
                  onClick={() => setSelectedBatch(null)}
                >
                  ✕
                </button>
              </div>
              <div className="batch-detail-content">
                <div className="batch-info">
                  <p><strong>时间:</strong> {selectedBatch.displayDate} {selectedBatch.displayTime}</p>
                  <p><strong>原始提示词:</strong> {selectedBatch.originalPrompt}</p>
                  <p><strong>最终提示词:</strong> {selectedBatch.prompt}</p>
                  <p><strong>转换结果:</strong> {selectedBatch.successful}/{selectedBatch.total} 张成功</p>
                </div>
                <div className="batch-images">
                  {selectedBatch.images.map((image, index) => (
                    <div key={index} className="batch-image-item">
                      <div className="image-container">
                        <img
                          src={image.output}
                          alt={image.originalFileName}
                          onError={(e) => {
                            e.target.style.display = 'none'
                          }}
                        />
                      </div>
                      <div className="image-info">
                        <div className="image-name">{image.originalFileName}</div>
                        <div className="image-seed">种子: {image.seed}</div>
                        {image.localPath && (
                          <div className="local-status">✅ 已保存到本地</div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 删除确认对话框 */}
        {showDeleteConfirm && (
          <div className="delete-confirm-overlay">
            <div className="delete-confirm">
              <h3>确认删除</h3>
              <p>确定要删除这个转换批次的历史记录吗？</p>
              <p className="warning">注意：这不会删除本地保存的图片文件</p>
              <div className="confirm-actions">
                <button 
                  className="cancel-btn"
                  onClick={() => setShowDeleteConfirm(null)}
                >
                  取消
                </button>
                <button 
                  className="confirm-btn"
                  onClick={() => deleteHistory(showDeleteConfirm)}
                >
                  确认删除
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

HistoryViewer.propTypes = {
  onClose: PropTypes.func.isRequired,
  onSelectBatch: PropTypes.func
}

export default HistoryViewer
