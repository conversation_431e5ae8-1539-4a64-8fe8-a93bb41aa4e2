import ImageConverter from './components/ImageConverter'
import GhibliDecorations from './components/common/GhibliDecorations'
import './styles/global.css'
import './styles/responsive.css'
import './styles/ImageConverter.css'

function App() {
  return (
    <div className="app-container">
      {/* 吉卜力风格装饰元素 */}
      <GhibliDecorations />
      
      {/* 使用新的图像转换器组件 */}
      <ImageConverter />
    </div>
  )
}

export default App

// 更新 App 容器的基本样式
const style = document.createElement('style')
style.textContent = `
  .app-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100vh;
    overflow: hidden;
    position: relative;
  }
`
document.head.appendChild(style)
