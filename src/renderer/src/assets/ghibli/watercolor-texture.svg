<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="400" viewBox="0 0 400 400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter id="watercolor" x="-50%" y="-50%" width="200%" height="200%">
      <feTurbulence type="fractalNoise" baseFrequency="0.01" numOctaves="5" seed="1" />
      <feGaussianBlur stdDeviation="5" />
      <feComposite in="SourceGraphic" operator="in" />
    </filter>
    <filter id="texture" x="-50%" y="-50%" width="200%" height="200%">
      <feTurbulence type="turbulence" baseFrequency="0.05" numOctaves="2" seed="2" />
      <feColorMatrix values="1 0 0 0 0
                           0 1 0 0 0
                           0 0 1 0 0
                           0 0 0 0.1 0" />
      <feComposite in="SourceGraphic" operator="in" />
    </filter>
  </defs>
  <rect width="100%" height="100%" fill="#FFF5E6" filter="url(#watercolor)" />
  <rect width="100%" height="100%" fill="#F5DEB3" opacity="0.1" filter="url(#texture)" />
</svg> 