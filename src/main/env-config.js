/**
 * 环境变量配置管理
 * 处理开发环境和生产环境的API密钥
 */

import { app } from 'electron'
import { join } from 'path'
import { existsSync, readFileSync } from 'fs'
import dotenv from 'dotenv'

// 环境变量配置
export function setupEnvironment() {
  try {
    console.log('[环境配置] 开始设置环境变量')
    console.log('[环境配置] NODE_ENV:', process.env.NODE_ENV)
    console.log('[环境配置] __dirname:', __dirname)
    console.log('[环境配置] process.resourcesPath:', process.resourcesPath)

    // 开发环境：从项目根目录加载.env文件
    if (process.env.NODE_ENV === 'development') {
      dotenv.config()
      console.log('[环境配置] 开发环境：从.env文件加载配置')
    } else {
      // 生产环境：从多个位置尝试加载配置
      const possibleEnvPaths = [
        // 应用包内的.env文件 (打包时包含的，在app.asar.unpacked中)
        join(process.resourcesPath, 'app.asar.unpacked', '.env'),
        join(process.resourcesPath, '.env'),
        // 应用数据目录
        join(app.getPath('userData'), '.env'),
        // 用户主目录
        join(app.getPath('home'), '.observer-137.env'),
        // 系统配置目录
        join(app.getPath('home'), 'Library', 'Preferences', 'Observer-137', '.env')
      ]

      let envLoaded = false
      console.log('[环境配置] 尝试加载配置文件，可能的路径:')
      possibleEnvPaths.forEach((path, index) => {
        const exists = existsSync(path)
        console.log(`  ${index + 1}. ${path} - ${exists ? '✅ 存在' : '❌ 不存在'}`)
      })

      for (const envPath of possibleEnvPaths) {
        if (existsSync(envPath)) {
          console.log('[环境配置] 生产环境：从文件加载配置:', envPath)
          dotenv.config({ path: envPath })
          envLoaded = true
          break
        }
      }

      if (!envLoaded) {
        console.warn('[环境配置] 警告：未找到环境配置文件')
        console.log('[环境配置] 尝试的路径:', possibleEnvPaths)
        
        // 创建默认配置文件
        createDefaultEnvFile()
      }
    }

    // 验证必要的环境变量
    validateEnvironmentVariables()

  } catch (error) {
    console.error('[环境配置] 加载环境变量失败:', error)
  }
}

// 验证环境变量
function validateEnvironmentVariables() {
  const requiredVars = [
    'VITE_REPLICATE_API_TOKEN',
    'VITE_DEEPSEEK_API_KEY'
  ]

  const missing = []
  for (const varName of requiredVars) {
    if (!process.env[varName]) {
      missing.push(varName)
    }
  }

  if (missing.length > 0) {
    console.error('[环境配置] 缺少必要的环境变量:', missing)
    console.log('[环境配置] 请在以下位置之一创建.env文件:')
    console.log('  1.', join(app.getPath('userData'), '.env'))
    console.log('  2.', join(app.getPath('home'), '.observer-137.env'))
  } else {
    console.log('[环境配置] ✅ 所有必要的环境变量已加载')
  }
}

// 创建默认配置文件
function createDefaultEnvFile() {
  try {
    const { writeFileSync, mkdirSync } = require('fs')
    const configPath = join(app.getPath('userData'), '.env')
    const configDir = join(app.getPath('userData'))

    // 确保目录存在
    if (!existsSync(configDir)) {
      mkdirSync(configDir, { recursive: true })
    }

    const defaultConfig = `# Observer-137 配置文件
# 请填入您的API密钥

# Replicate API Token (必需)
# 获取地址: https://replicate.com/account/api-tokens
VITE_REPLICATE_API_TOKEN=your_replicate_token_here

# DeepSeek API Key (可选，用于提示词优化)
# 获取地址: https://platform.deepseek.com/api_keys
VITE_DEEPSEEK_API_KEY=your_deepseek_key_here

# 其他配置
NODE_ENV=production
`

    writeFileSync(configPath, defaultConfig, 'utf8')
    console.log('[环境配置] 已创建默认配置文件:', configPath)
    console.log('[环境配置] 请编辑此文件并填入您的API密钥')

  } catch (error) {
    console.error('[环境配置] 创建默认配置文件失败:', error)
  }
}

// 获取配置信息（用于调试）
export function getConfigInfo() {
  return {
    nodeEnv: process.env.NODE_ENV,
    hasReplicateToken: !!process.env.VITE_REPLICATE_API_TOKEN,
    hasDeepseekKey: !!process.env.VITE_DEEPSEEK_API_KEY,
    userDataPath: app.getPath('userData'),
    resourcesPath: process.resourcesPath || 'N/A'
  }
}
