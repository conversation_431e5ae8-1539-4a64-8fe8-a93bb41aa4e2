import { app, shell, BrowserWindow, ipcMain, dialog } from 'electron'
import { join } from 'path'
import { electronApp, optimizer, is } from '@electron-toolkit/utils'
import { promises as fs } from 'fs'
import { existsSync, mkdirSync } from 'fs'
import Replicate from 'replicate'
import icon from '../../resources/icon.png?asset'
import { setupEnvironment, getConfigInfo } from './env-config.js'

// 设置环境变量
setupEnvironment()

// 初始化Replicate客户端
let replicate = null
try {
  if (process.env.VITE_REPLICATE_API_TOKEN) {
    replicate = new Replicate({
      auth: process.env.VITE_REPLICATE_API_TOKEN,
    })
    console.log('[Main Process] ✅ Replicate客户端初始化成功')
  } else {
    console.warn('[Main Process] ⚠️ Replicate API Token未配置')
  }
} catch (error) {
  console.error('[Main Process] ❌ Replicate客户端初始化失败:', error)
}

// 调试：检查API Token是否正确加载
console.log('[Main Process] 配置信息:', getConfigInfo())

function createWindow() {
  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    show: false,
    autoHideMenuBar: true,
    title: 'FLUX 图像转换工具',
    backgroundColor: '#F5F5DC', // 吉卜力风格的柔和米色
    ...(process.platform === 'linux' ? { icon } : {}),
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false,
      contextIsolation: true,
      nodeIntegration: false
    }
  })

  mainWindow.on('ready-to-show', () => {
    mainWindow.show()
  })

  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // 开发环境下自动打开开发者工具
  if (is.dev) {
    mainWindow.webContents.openDevTools()
  }

  // HMR for renderer base on electron-vite cli.
  // Load the remote URL for development or the local html file for production.
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }
}

// 文件系统API处理器
function setupFileSystemHandlers() {
  // 获取应用数据目录
  ipcMain.handle('get-app-data-path', () => {
    return app.getPath('userData')
  })

  // 确保目录存在
  ipcMain.handle('ensure-directory', async (event, dirPath) => {
    try {
      if (!existsSync(dirPath)) {
        mkdirSync(dirPath, { recursive: true })
        console.log('[文件系统] 创建目录:', dirPath)
      }
      return true
    } catch (error) {
      console.error('[文件系统] 创建目录失败:', error)
      return false
    }
  })

  // 写入文件
  ipcMain.handle('write-file', async (event, filePath, data) => {
    try {
      await fs.writeFile(filePath, Buffer.from(data))
      console.log('[文件系统] 文件写入成功:', filePath)
      return true
    } catch (error) {
      console.error('[文件系统] 文件写入失败:', error)
      throw error
    }
  })

  // 打开文件夹
  ipcMain.handle('open-folder', async (event, folderPath) => {
    try {
      // 确保目录存在
      if (!existsSync(folderPath)) {
        mkdirSync(folderPath, { recursive: true })
        console.log('[文件系统] 创建目录:', folderPath)
      }

      // 尝试打开文件夹
      const result = await shell.openPath(folderPath)

      if (result) {
        console.error('[文件系统] 打开文件夹失败:', result)
        // 显示错误对话框
        dialog.showErrorBox('无法打开文件夹', `无法打开文件夹: ${folderPath}\n错误: ${result}`)
        return false
      } else {
        console.log('[文件系统] 打开文件夹成功:', folderPath)
        return true
      }
    } catch (error) {
      console.error('[文件系统] 打开文件夹异常:', error)
      dialog.showErrorBox('文件夹访问错误', `访问文件夹时发生错误: ${error.message}`)
      return false
    }
  })

  // 列出文件
  ipcMain.handle('list-files', async (event, dirPath) => {
    try {
      if (!existsSync(dirPath)) {
        return []
      }
      const files = await fs.readdir(dirPath, { withFileTypes: true })
      return files
        .filter(file => file.isFile() && /\.(jpg|jpeg|png|webp|gif)$/i.test(file.name))
        .map(file => ({
          name: file.name,
          path: join(dirPath, file.name)
        }))
    } catch (error) {
      console.error('[文件系统] 列出文件失败:', error)
      return []
    }
  })

  // 清理旧文件
  ipcMain.handle('clean-old-files', async (event, daysToKeep = 7) => {
    try {
      const appDataPath = app.getPath('userData')
      const outputsPath = join(appDataPath, 'outputs')
      
      if (!existsSync(outputsPath)) {
        return true
      }

      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep)

      const dirs = await fs.readdir(outputsPath, { withFileTypes: true })
      let cleanedCount = 0

      for (const dir of dirs) {
        if (dir.isDirectory()) {
          const dirDate = new Date(dir.name)
          if (dirDate < cutoffDate) {
            const dirPath = join(outputsPath, dir.name)
            await fs.rm(dirPath, { recursive: true, force: true })
            cleanedCount++
            console.log('[文件系统] 清理旧目录:', dirPath)
          }
        }
      }

      console.log(`[文件系统] 清理完成，删除了 ${cleanedCount} 个旧目录`)
      return true
    } catch (error) {
      console.error('[文件系统] 清理失败:', error)
      return false
    }
  })

  // 保存转换历史记录
  ipcMain.handle('save-conversion-history', async (event, historyData) => {
    try {
      const historyDir = join(app.getPath('userData'), 'history')
      if (!existsSync(historyDir)) {
        mkdirSync(historyDir, { recursive: true })
      }

      const historyFile = join(historyDir, 'conversions.json')
      let history = []

      // 读取现有历史记录
      if (existsSync(historyFile)) {
        const data = await fs.readFile(historyFile, 'utf8')
        history = JSON.parse(data)
      }

      // 添加新记录
      history.unshift(historyData) // 最新的在前面

      // 限制历史记录数量（保留最近100个批次）
      if (history.length > 100) {
        history = history.slice(0, 100)
      }

      // 保存历史记录
      await fs.writeFile(historyFile, JSON.stringify(history, null, 2))
      console.log('[历史记录] 保存成功:', historyData.batchId)
      return true
    } catch (error) {
      console.error('[历史记录] 保存失败:', error)
      return false
    }
  })

  // 获取转换历史记录
  ipcMain.handle('get-conversion-history', async (event) => {
    try {
      const historyFile = join(app.getPath('userData'), 'history', 'conversions.json')
      if (!existsSync(historyFile)) {
        return []
      }

      const data = await fs.readFile(historyFile, 'utf8')
      const history = JSON.parse(data)
      console.log(`[历史记录] 读取成功，共 ${history.length} 条记录`)
      return history
    } catch (error) {
      console.error('[历史记录] 读取失败:', error)
      return []
    }
  })

  // 删除转换历史记录
  ipcMain.handle('delete-conversion-history', async (event, batchId) => {
    try {
      const historyFile = join(app.getPath('userData'), 'history', 'conversions.json')
      if (!existsSync(historyFile)) {
        return true
      }

      const data = await fs.readFile(historyFile, 'utf8')
      let history = JSON.parse(data)

      // 过滤掉指定的批次
      history = history.filter(item => item.batchId !== batchId)

      // 保存更新后的历史记录
      await fs.writeFile(historyFile, JSON.stringify(history, null, 2))
      console.log('[历史记录] 删除成功:', batchId)
      return true
    } catch (error) {
      console.error('[历史记录] 删除失败:', error)
      return false
    }
  })

  // 获取文件统计信息
  ipcMain.handle('get-file-stats', async (event, filePath) => {
    try {
      const stats = await fs.stat(filePath)
      return {
        size: stats.size,
        created: stats.birthtime,
        modified: stats.mtime
      }
    } catch (error) {
      console.error('[文件系统] 获取文件统计失败:', error)
      return null
    }
  })
}

// API调用处理器 - 避免CORS问题
function setupApiHandlers() {
  // 检查API配置
  ipcMain.handle('check-api-config', () => {
    const config = getConfigInfo()
    return {
      ...config,
      replicateConfigured: !!process.env.VITE_REPLICATE_API_TOKEN,
      deepseekConfigured: !!process.env.VITE_DEEPSEEK_API_KEY,
      configPath: join(app.getPath('userData'), '.env')
    }
  })

  // 重新加载环境配置
  ipcMain.handle('reload-env-config', () => {
    try {
      setupEnvironment()

      // 重新初始化Replicate客户端
      if (process.env.VITE_REPLICATE_API_TOKEN) {
        replicate = new Replicate({
          auth: process.env.VITE_REPLICATE_API_TOKEN,
        })
        console.log('[Main Process] ✅ Replicate客户端重新初始化成功')
      }

      return { success: true, config: getConfigInfo() }
    } catch (error) {
      console.error('[Main Process] 重新加载配置失败:', error)
      return { success: false, error: error.message }
    }
  })

  // Replicate Run - 使用官方客户端
  ipcMain.handle('replicate-run', async (event, model, input) => {
    try {
      // 检查Replicate客户端是否已初始化
      if (!replicate) {
        return {
          success: false,
          error: 'API_NOT_CONFIGURED',
          message: 'Replicate API未配置，请先配置API密钥',
          needsConfig: true
        }
      }

      console.log('[Replicate Run] 运行模型:', model)
      console.log('[Replicate Run] 输入参数:', input)
      
      // 按照官方示例直接运行模型 - 修正参数格式
      const output = await replicate.run(model, { input })
      
      console.log('[Replicate Run] 模型运行成功，输出类型:', typeof output)
      console.log('[Replicate Run] 输出长度:', output?.length || 'N/A')
      
      // 处理输出 - 提取实际的图片URL
      let finalData = output
      
      if (typeof output === 'object' && output !== null) {
        console.log('[Replicate Run] 对象属性:', Object.keys(output))
        console.log('[Replicate Run] 有url方法:', typeof output.url)
        
        // 如果有url方法，直接调用获取URL
        if (typeof output.url === 'function') {
          try {
            const url = output.url()
            console.log('[Replicate Run] ✅ 成功获取图片URL:', url)
            // 确保返回的是字符串而不是URL对象
            finalData = typeof url === 'string' ? url : url.toString()
          } catch (e) {
            console.error('[Replicate Run] URL方法调用失败:', e)
            finalData = output.toString() // 回退到toString
          }
        } else if (output.url) {
          // 如果有url属性
          finalData = typeof output.url === 'string' ? output.url : output.url.toString()
          console.log('[Replicate Run] ✅ 使用url属性:', finalData)
        } else {
          // 尝试转换为字符串
          try {
            finalData = output.toString()
            console.log('[Replicate Run] ✅ 使用toString:', finalData)
          } catch (e) {
            console.error('[Replicate Run] toString失败:', e)
          }
        }
      }
      
      console.log('[Replicate Run] 最终返回数据:', finalData)
      return { success: true, data: finalData }
      
    } catch (error) {
      console.error('[Replicate Run] 运行失败:', error)
      return { success: false, error: error.message }
    }
  })

  // Replicate API 调用 (保留用于其他用途)
  ipcMain.handle('replicate-api', async (event, endpoint, options = {}) => {
    const { default: fetch } = await import('node-fetch')
    
    try {
      const url = `https://api.replicate.com/v1${endpoint}`
      console.log('[Replicate API] 发送请求:', options.method || 'GET', url)
      
      const response = await fetch(url, {
        ...options,
        headers: {
          'Authorization': `Token ${process.env.VITE_REPLICATE_API_TOKEN}`,
          'Content-Type': 'application/json',
          ...options.headers
        }
      })
      
      if (!response.ok) {
        const errorText = await response.text().catch(() => 'No error details')
        console.error(`[Replicate API] 响应错误 ${response.status}: ${errorText}`)
        throw new Error(`Replicate API错误 ${response.status}: ${errorText}`)
      }
      
      const data = await response.json()
      console.log('[Replicate API] 成功响应:', url)
      return { success: true, data }
    } catch (error) {
      console.error('[Replicate API] 请求失败:', error)
      return { success: false, error: error.message }
    }
  })

  // DeepSeek API 调用
  ipcMain.handle('deepseek-api', async (event, endpoint, options = {}) => {
    const { default: fetch } = await import('node-fetch')
    
    try {
      const url = `https://api.deepseek.com/v1${endpoint}`
      console.log('[DeepSeek API] 发送请求:', options.method || 'GET', url)
      
      const response = await fetch(url, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.VITE_DEEPSEEK_API_KEY}`,
          ...options.headers
        }
      })
      
      if (!response.ok) {
        const errorText = await response.text().catch(() => 'No error details')
        console.error(`[DeepSeek API] 响应错误 ${response.status}: ${errorText}`)
        throw new Error(`DeepSeek API错误 ${response.status}: ${errorText}`)
      }
      
      const data = await response.json()
      console.log('[DeepSeek API] 成功响应:', url)
      return { success: true, data }
    } catch (error) {
      console.error('[DeepSeek API] 请求失败:', error)
      return { success: false, error: error.message }
    }
  })

  // 通用HTTP请求
  ipcMain.handle('fetch-url', async (event, url, options = {}) => {
    const { default: fetch } = await import('node-fetch')
    
    try {
      console.log('[HTTP] 下载:', url)
      const response = await fetch(url, options)
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      const buffer = await response.buffer()
      return { success: true, data: buffer }
    } catch (error) {
      console.error('[HTTP] 请求失败:', error)
      return { success: false, error: error.message }
    }
  })
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(() => {
  // Set app user model id for windows
  electronApp.setAppUserModelId('com.fluxconverter.app')

  // 设置文件系统处理器
  setupFileSystemHandlers()
  
  // 设置API处理器
  setupApiHandlers()

  // Default open or close DevTools by F12 in development
  // and ignore CommandOrControl + R in production.
  // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
  app.on('browser-window-created', (_, window) => {
    optimizer.watchWindowShortcuts(window)
  })

  createWindow()

  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and require them here.
