import { contextBridge, ipcRenderer } from 'electron'
import { electronAPI } from '@electron-toolkit/preload'

// Custom APIs for renderer
const api = {}

// 文件系统API
const electronFileAPI = {
  // 获取应用数据目录
  getAppDataPath: () => ipcRenderer.invoke('get-app-data-path'),
  
  // 确保目录存在
  ensureDirectory: (dirPath) => ipcRenderer.invoke('ensure-directory', dirPath),
  
  // 写入文件
  writeFile: (filePath, data) => ipcRenderer.invoke('write-file', filePath, data),
  
  // 打开文件夹
  openFolder: (folderPath) => ipcRenderer.invoke('open-folder', folderPath),
  
  // 列出文件
  listFiles: (dirPath) => ipcRenderer.invoke('list-files', dirPath),
  
  // 清理旧文件
  cleanOldFiles: (daysToKeep) => ipcRenderer.invoke('clean-old-files', daysToKeep),
  
  // 获取文件统计信息
  getFileStats: (filePath) => ipcRenderer.invoke('get-file-stats', filePath),

  // 历史记录管理
  saveConversionHistory: (historyData) => ipcRenderer.invoke('save-conversion-history', historyData),
  getConversionHistory: () => ipcRenderer.invoke('get-conversion-history'),
  deleteConversionHistory: (batchId) => ipcRenderer.invoke('delete-conversion-history', batchId)
}

// API调用接口 - 通过主进程避免CORS
const electronApiClient = {
  // Replicate Run - 按照官方示例运行模型
  replicateRun: (model, input) => ipcRenderer.invoke('replicate-run', model, input),
  
  // Replicate API 调用
  replicateApi: (endpoint, options) => ipcRenderer.invoke('replicate-api', endpoint, options),
  
  // DeepSeek API 调用
  deepseekApi: (endpoint, options) => ipcRenderer.invoke('deepseek-api', endpoint, options),
  
  // 通用HTTP请求
  fetchUrl: (url, options) => ipcRenderer.invoke('fetch-url', url, options)
}

// Use `contextBridge` APIs to expose Electron APIs to
// renderer only if context isolation is enabled, otherwise
// just add to the DOM global.
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electron', electronAPI)
    contextBridge.exposeInMainWorld('electronAPI', electronFileAPI)
    contextBridge.exposeInMainWorld('electronApiClient', electronApiClient)
    contextBridge.exposeInMainWorld('api', api)
  } catch (error) {
    console.error(error)
  }
} else {
  window.electron = electronAPI
  window.electronAPI = electronFileAPI
  window.electronApiClient = electronApiClient
  window.api = api
}
