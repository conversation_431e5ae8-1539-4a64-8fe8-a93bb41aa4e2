// 测试历史记录功能的简单脚本
const fs = require('fs')
const path = require('path')
const os = require('os')

// 模拟历史记录数据
const mockHistoryData = {
  batchId: `batch_${Date.now()}_test123`,
  timestamp: new Date().toISOString(),
  prompt: "Transform this into Studio Ghibli anime style, maintain high quality and artistic consistency",
  originalPrompt: "Transform this into Studio Ghibli anime style",
  options: {
    aspect_ratio: "match_input_image",
    output_format: "jpg",
    safety_tolerance: 2,
    guidance_scale: 3.5,
    num_inference_steps: 25,
    useDeepseek: true
  },
  total: 2,
  successful: 2,
  failed: 0,
  images: [
    {
      originalFileName: "test1.jpg",
      fileName: "converted_test1_2024-06-30_12345.jpg",
      localPath: "/path/to/local/converted_test1_2024-06-30_12345.jpg",
      output: "https://replicate.delivery/pbxt/test1.jpg",
      seed: 12345
    },
    {
      originalFileName: "test2.jpg", 
      fileName: "converted_test2_2024-06-30_67890.jpg",
      localPath: "/path/to/local/converted_test2_2024-06-30_67890.jpg",
      output: "https://replicate.delivery/pbxt/test2.jpg",
      seed: 67890
    }
  ]
}

// 获取应用数据目录
const appDataPath = path.join(os.homedir(), 'Library', 'Application Support', 'Observer-137')
const historyDir = path.join(appDataPath, 'history')
const historyFile = path.join(historyDir, 'conversions.json')

console.log('测试历史记录功能...')
console.log('应用数据目录:', appDataPath)
console.log('历史记录目录:', historyDir)
console.log('历史记录文件:', historyFile)

// 确保目录存在
if (!fs.existsSync(historyDir)) {
  fs.mkdirSync(historyDir, { recursive: true })
  console.log('✅ 创建历史记录目录')
}

// 读取现有历史记录
let history = []
if (fs.existsSync(historyFile)) {
  const data = fs.readFileSync(historyFile, 'utf8')
  history = JSON.parse(data)
  console.log(`📚 读取到 ${history.length} 条现有历史记录`)
} else {
  console.log('📚 没有现有历史记录')
}

// 添加测试数据
history.unshift(mockHistoryData)
console.log('➕ 添加测试历史记录')

// 保存历史记录
fs.writeFileSync(historyFile, JSON.stringify(history, null, 2))
console.log('💾 保存历史记录成功')

// 验证保存结果
const savedData = JSON.parse(fs.readFileSync(historyFile, 'utf8'))
console.log(`✅ 验证成功，现在有 ${savedData.length} 条历史记录`)
console.log('最新记录的批次ID:', savedData[0].batchId)
console.log('最新记录的图片数量:', savedData[0].images.length)

console.log('\n🎉 历史记录功能测试完成！')
console.log('现在可以在应用中点击"📚 历史记录"按钮查看测试数据')
