import { resolve } from 'path'
import { defineConfig, externalizeDepsPlugin } from 'electron-vite'
import react from '@vitejs/plugin-react'
import { loadEnv } from 'vite'

export default defineConfig(({ mode }) => {
  // 使用 resolve 确保路径正确
  const envDir = resolve(__dirname, '.')
  const env = loadEnv(mode, envDir, '')
  
  // 只保留 DeepSeek 代理用于提示词优化
  const proxyConfig = {
    '/deepseek': {
      target: 'https://api.deepseek.com',
      changeOrigin: true,
      secure: false,
      rewrite: (path) => path.replace(/^\/deepseek/, '/v1/chat')
    }
  }
  
  return {
    main: {
      plugins: [externalizeDepsPlugin()]
    },
    preload: {
      plugins: [externalizeDepsPlugin()]
    },
    renderer: {
      resolve: {
        alias: {
          '@renderer': resolve('src/renderer/src')
        }
      },
      plugins: [react()],
      envDir: envDir,
      define: {
        // 确保环境变量可以在渲染进程中使用
        'import.meta.env.VITE_REPLICATE_API_TOKEN': JSON.stringify(env.VITE_REPLICATE_API_TOKEN),
        'import.meta.env.VITE_DEEPSEEK_API_KEY': JSON.stringify(env.VITE_DEEPSEEK_API_KEY)
      },
      server: {
        proxy: proxyConfig
      },
      build: {
        rollupOptions: {
          output: {
            manualChunks: {
              vendor: ['react', 'react-dom']
            }
          }
        }
      }
    }
  }
})
