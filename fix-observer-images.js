#!/usr/bin/env node

const fs = require('fs').promises
const path = require('path')
const { existsSync } = require('fs')
const os = require('os')

/**
 * Observer-137 图片目录修复脚本
 * 自动找到应用数据目录并整理批次图片
 */

async function findObserverDataDir() {
  const possiblePaths = [
    path.join(os.homedir(), 'Library', 'Application Support', 'Observer-137'),
    path.join(os.homedir(), 'Library', 'Application Support', 'observer-137'),
    path.join(os.homedir(), 'Library', 'Application Support', 'Electron'),
    path.join(os.homedir(), '.config', 'Observer-137'),
  ]
  
  for (const possiblePath of possiblePaths) {
    const outputsPath = path.join(possiblePath, 'outputs')
    if (existsSync(outputsPath)) {
      return outputsPath
    }
  }
  
  return null
}

async function fixObserverImages() {
  try {
    console.log('🔍 正在查找 Observer-137 应用数据目录...')
    
    const outputsDir = await findObserverDataDir()
    
    if (!outputsDir) {
      console.log('❌ 未找到 Observer-137 输出目录')
      console.log('💡 请手动指定目录路径：')
      console.log('   node fix-observer-images.js "/path/to/outputs"')
      return
    }
    
    console.log('✅ 找到输出目录:', outputsDir)
    
    // 获取所有日期目录
    const dateDirs = await fs.readdir(outputsDir)
    
    for (const dateDir of dateDirs) {
      const datePath = path.join(outputsDir, dateDir)
      const stat = await fs.stat(datePath)
      
      if (!stat.isDirectory()) continue
      
      // 检查是否是日期格式的目录 (YYYY-MM-DD)
      if (!/^\d{4}-\d{2}-\d{2}$/.test(dateDir)) {
        console.log(`⏭️  跳过非日期目录: ${dateDir}`)
        continue
      }
      
      console.log(`\n📅 处理日期目录: ${dateDir}`)
      
      await processBatchesInDirectory(datePath)
    }
    
    console.log('\n🎉 所有图片整理完成！')
    
  } catch (error) {
    console.error('❌ 处理过程中出错:', error)
  }
}

async function processBatchesInDirectory(dirPath) {
  try {
    const items = await fs.readdir(dirPath)
    
    let totalMoved = 0
    let totalBatches = 0
    
    for (const item of items) {
      const itemPath = path.join(dirPath, item)
      const stat = await fs.stat(itemPath)
      
      if (!stat.isDirectory()) continue
      
      // 只处理批次目录
      if (!item.startsWith('batch_')) {
        continue
      }
      
      totalBatches++
      console.log(`  📁 处理批次: ${item}`)
      
      const files = await fs.readdir(itemPath)
      let movedCount = 0
      
      for (const file of files) {
        const filePath = path.join(itemPath, file)
        const fileStat = await fs.stat(filePath)
        
        if (!fileStat.isFile()) continue
        
        // 检查是否是图片文件
        const ext = path.extname(file).toLowerCase()
        if (!['.jpg', '.jpeg', '.png', '.webp', '.gif'].includes(ext)) {
          continue
        }
        
        // 目标路径
        let targetPath = path.join(dirPath, file)
        
        // 处理文件名冲突
        if (existsSync(targetPath)) {
          const baseName = path.basename(file, ext)
          const batchId = item.replace('batch_', '').substring(0, 8)
          const newFileName = `${baseName}_${batchId}${ext}`
          targetPath = path.join(dirPath, newFileName)
          console.log(`    ⚠️  重命名避免冲突: ${file} -> ${newFileName}`)
        } else {
          console.log(`    ✅ 移动: ${file}`)
        }
        
        await fs.rename(filePath, targetPath)
        movedCount++
        totalMoved++
      }
      
      // 删除空的批次目录
      const remaining = await fs.readdir(itemPath)
      if (remaining.length === 0) {
        await fs.rmdir(itemPath)
        console.log(`    🗑️  删除空目录: ${item}`)
      } else {
        console.log(`    📋 保留目录 (还有 ${remaining.length} 个文件): ${item}`)
      }
      
      if (movedCount > 0) {
        console.log(`    📊 移动了 ${movedCount} 个图片`)
      }
    }
    
    if (totalBatches > 0) {
      console.log(`  📈 ${path.basename(dirPath)}: 处理了 ${totalBatches} 个批次，移动了 ${totalMoved} 个图片`)
    }
    
  } catch (error) {
    console.error(`❌ 处理目录 ${dirPath} 时出错:`, error)
  }
}

// 命令行参数处理
const args = process.argv.slice(2)

if (args.includes('--help') || args.includes('-h')) {
  console.log(`
🔧 Observer-137 图片目录修复工具

这个脚本会自动：
1. 找到 Observer-137 应用的数据目录
2. 扫描所有日期目录下的批次文件夹
3. 将批次文件夹中的图片移动到日期目录下
4. 删除空的批次文件夹

用法:
  node fix-observer-images.js              # 自动查找并处理
  node fix-observer-images.js [目录路径]    # 手动指定目录

选项:
  --help, -h    显示此帮助信息

示例:
  node fix-observer-images.js
  node fix-observer-images.js "~/Library/Application Support/Observer-137/outputs"
`)
  process.exit(0)
}

// 如果提供了目录参数，直接处理该目录
if (args.length > 0 && !args[0].startsWith('--')) {
  const customDir = path.resolve(args[0])
  console.log('🎯 使用指定目录:', customDir)
  
  if (existsSync(customDir)) {
    processBatchesInDirectory(customDir)
      .then(() => console.log('🎉 处理完成！'))
      .catch(error => console.error('❌ 处理失败:', error))
  } else {
    console.log('❌ 指定的目录不存在:', customDir)
  }
} else {
  // 自动查找并处理
  fixObserverImages()
}
