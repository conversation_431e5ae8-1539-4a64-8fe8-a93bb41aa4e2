"use strict";
const electron = require("electron");
const preload = require("@electron-toolkit/preload");
const api = {};
const electronFileAPI = {
  // 获取应用数据目录
  getAppDataPath: () => electron.ipcRenderer.invoke("get-app-data-path"),
  // 确保目录存在
  ensureDirectory: (dirPath) => electron.ipcRenderer.invoke("ensure-directory", dirPath),
  // 写入文件
  writeFile: (filePath, data) => electron.ipcRenderer.invoke("write-file", filePath, data),
  // 打开文件夹
  openFolder: (folderPath) => electron.ipcRenderer.invoke("open-folder", folderPath),
  // 列出文件
  listFiles: (dirPath) => electron.ipcRenderer.invoke("list-files", dirPath),
  // 清理旧文件
  cleanOldFiles: (daysToKeep) => electron.ipcRenderer.invoke("clean-old-files", daysToKeep),
  // 获取文件统计信息
  getFileStats: (filePath) => electron.ipcRenderer.invoke("get-file-stats", filePath),
  // 历史记录管理
  saveConversionHistory: (historyData) => electron.ipcRenderer.invoke("save-conversion-history", historyData),
  getConversionHistory: () => electron.ipcRenderer.invoke("get-conversion-history"),
  deleteConversionHistory: (batchId) => electron.ipcRenderer.invoke("delete-conversion-history", batchId)
};
const electronApiClient = {
  // Replicate Run - 按照官方示例运行模型
  replicateRun: (model, input) => electron.ipcRenderer.invoke("replicate-run", model, input),
  // Replicate API 调用
  replicateApi: (endpoint, options) => electron.ipcRenderer.invoke("replicate-api", endpoint, options),
  // DeepSeek API 调用
  deepseekApi: (endpoint, options) => electron.ipcRenderer.invoke("deepseek-api", endpoint, options),
  // 通用HTTP请求
  fetchUrl: (url, options) => electron.ipcRenderer.invoke("fetch-url", url, options),
  // 检查API配置
  checkApiConfig: () => electron.ipcRenderer.invoke("check-api-config"),
  // 重新加载环境配置
  reloadEnvConfig: () => electron.ipcRenderer.invoke("reload-env-config")
};
if (process.contextIsolated) {
  try {
    electron.contextBridge.exposeInMainWorld("electron", preload.electronAPI);
    electron.contextBridge.exposeInMainWorld("electronAPI", electronFileAPI);
    electron.contextBridge.exposeInMainWorld("electronApiClient", electronApiClient);
    electron.contextBridge.exposeInMainWorld("api", api);
  } catch (error) {
    console.error(error);
  }
} else {
  window.electron = preload.electronAPI;
  window.electronAPI = electronFileAPI;
  window.electronApiClient = electronApiClient;
  window.api = api;
}
