import { r as requireReact, g as getDefaultExportFromCjs, a as requireReactDom } from "./vendor-D_QSeeZk.js";
var jsxRuntime = { exports: {} };
var reactJsxRuntime_production_min = {};
/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var hasRequiredReactJsxRuntime_production_min;
function requireReactJsxRuntime_production_min() {
  if (hasRequiredReactJsxRuntime_production_min) return reactJsxRuntime_production_min;
  hasRequiredReactJsxRuntime_production_min = 1;
  var f = requireReact(), k = Symbol.for("react.element"), l = Symbol.for("react.fragment"), m = Object.prototype.hasOwnProperty, n = f.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner, p = { key: true, ref: true, __self: true, __source: true };
  function q(c, a, g) {
    var b, d = {}, e = null, h = null;
    void 0 !== g && (e = "" + g);
    void 0 !== a.key && (e = "" + a.key);
    void 0 !== a.ref && (h = a.ref);
    for (b in a) m.call(a, b) && !p.hasOwnProperty(b) && (d[b] = a[b]);
    if (c && c.defaultProps) for (b in a = c.defaultProps, a) void 0 === d[b] && (d[b] = a[b]);
    return { $$typeof: k, type: c, key: e, ref: h, props: d, _owner: n.current };
  }
  reactJsxRuntime_production_min.Fragment = l;
  reactJsxRuntime_production_min.jsx = q;
  reactJsxRuntime_production_min.jsxs = q;
  return reactJsxRuntime_production_min;
}
var hasRequiredJsxRuntime;
function requireJsxRuntime() {
  if (hasRequiredJsxRuntime) return jsxRuntime.exports;
  hasRequiredJsxRuntime = 1;
  {
    jsxRuntime.exports = requireReactJsxRuntime_production_min();
  }
  return jsxRuntime.exports;
}
var jsxRuntimeExports = requireJsxRuntime();
var reactExports = requireReact();
const React = /* @__PURE__ */ getDefaultExportFromCjs(reactExports);
var client = {};
var hasRequiredClient;
function requireClient() {
  if (hasRequiredClient) return client;
  hasRequiredClient = 1;
  var m = requireReactDom();
  {
    client.createRoot = m.createRoot;
    client.hydrateRoot = m.hydrateRoot;
  }
  return client;
}
var clientExports = requireClient();
const ReactDOM = /* @__PURE__ */ getDefaultExportFromCjs(clientExports);
var propTypes = { exports: {} };
var ReactPropTypesSecret_1;
var hasRequiredReactPropTypesSecret;
function requireReactPropTypesSecret() {
  if (hasRequiredReactPropTypesSecret) return ReactPropTypesSecret_1;
  hasRequiredReactPropTypesSecret = 1;
  var ReactPropTypesSecret = "SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";
  ReactPropTypesSecret_1 = ReactPropTypesSecret;
  return ReactPropTypesSecret_1;
}
var factoryWithThrowingShims;
var hasRequiredFactoryWithThrowingShims;
function requireFactoryWithThrowingShims() {
  if (hasRequiredFactoryWithThrowingShims) return factoryWithThrowingShims;
  hasRequiredFactoryWithThrowingShims = 1;
  var ReactPropTypesSecret = /* @__PURE__ */ requireReactPropTypesSecret();
  function emptyFunction() {
  }
  function emptyFunctionWithReset() {
  }
  emptyFunctionWithReset.resetWarningCache = emptyFunction;
  factoryWithThrowingShims = function() {
    function shim(props, propName, componentName, location, propFullName, secret) {
      if (secret === ReactPropTypesSecret) {
        return;
      }
      var err = new Error(
        "Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types"
      );
      err.name = "Invariant Violation";
      throw err;
    }
    shim.isRequired = shim;
    function getShim() {
      return shim;
    }
    var ReactPropTypes = {
      array: shim,
      bigint: shim,
      bool: shim,
      func: shim,
      number: shim,
      object: shim,
      string: shim,
      symbol: shim,
      any: shim,
      arrayOf: getShim,
      element: shim,
      elementType: shim,
      instanceOf: getShim,
      node: shim,
      objectOf: getShim,
      oneOf: getShim,
      oneOfType: getShim,
      shape: getShim,
      exact: getShim,
      checkPropTypes: emptyFunctionWithReset,
      resetWarningCache: emptyFunction
    };
    ReactPropTypes.PropTypes = ReactPropTypes;
    return ReactPropTypes;
  };
  return factoryWithThrowingShims;
}
var hasRequiredPropTypes;
function requirePropTypes() {
  if (hasRequiredPropTypes) return propTypes.exports;
  hasRequiredPropTypes = 1;
  {
    propTypes.exports = /* @__PURE__ */ requireFactoryWithThrowingShims()();
  }
  return propTypes.exports;
}
var propTypesExports = /* @__PURE__ */ requirePropTypes();
const PropTypes = /* @__PURE__ */ getDefaultExportFromCjs(propTypesExports);
const getAppDataPath = () => {
  if (window.electronAPI) {
    return window.electronAPI.getAppDataPath();
  }
  return "Downloads/FluxConverter";
};
const getOutputPath = () => {
  const appDataPath = getAppDataPath();
  const timestamp = (/* @__PURE__ */ new Date()).toISOString().split("T")[0];
  return `${appDataPath}/outputs/${timestamp}`;
};
const ensureOutputDirectory = async () => {
  const outputPath = getOutputPath();
  if (window.electronAPI) {
    await window.electronAPI.ensureDirectory(outputPath);
  }
  return outputPath;
};
const saveBase64Image = async (dataUrl, fileName, outputPath) => {
  try {
    console.log("[文件管理] 保存Base64图片:", fileName);
    let base64Data = dataUrl;
    if (dataUrl.startsWith("data:")) {
      base64Data = dataUrl.split(",")[1];
    }
    if (window.electronAPI) {
      console.log("[文件管理] 使用主进程保存Base64数据");
      const filePath = `${outputPath}/${fileName}`;
      const buffer = Buffer.from(base64Data, "base64");
      await window.electronAPI.writeFile(filePath, buffer);
      console.log("[文件管理] Base64图片已保存:", filePath);
      return filePath;
    } else {
      console.log("[文件管理] 使用Web方式保存Base64数据");
      const byteCharacters = atob(base64Data);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: "image/jpeg" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      return fileName;
    }
  } catch (error) {
    console.error("[文件管理] 保存Base64图片失败:", error);
    console.error("[文件管理] 数据URL长度:", dataUrl?.length || 0);
    console.error("[文件管理] 文件名:", fileName);
    console.error("[文件管理] 输出路径:", outputPath);
    throw error;
  }
};
const downloadAndSaveImage = async (imageUrl, fileName, outputPath) => {
  try {
    console.log("[文件管理] 下载图片:", fileName);
    console.log("[文件管理] 图片URL:", imageUrl);
    if (!isValidUrl(imageUrl)) {
      throw new Error(`Invalid URL: ${imageUrl}`);
    }
    if (window.electronApiClient) {
      console.log("[文件管理] 使用主进程下载");
      const result = await window.electronApiClient.fetchUrl(imageUrl);
      if (!result.success) {
        throw new Error(`主进程下载失败: ${result.error}`);
      }
      const filePath = `${outputPath}/${fileName}`;
      await window.electronAPI.writeFile(filePath, result.data);
      console.log("[文件管理] 图片已保存:", filePath);
      return filePath;
    } else {
      console.log("[文件管理] 使用Web方式下载");
      const response = await fetch(imageUrl);
      if (!response.ok) {
        throw new Error(`下载失败: ${response.status} ${response.statusText}`);
      }
      const blob = await response.blob();
      const arrayBuffer = await blob.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      return fileName;
    }
  } catch (error) {
    console.error("[文件管理] 保存图片失败:", error);
    console.error("[文件管理] URL:", imageUrl);
    console.error("[文件管理] 文件名:", fileName);
    console.error("[文件管理] 输出路径:", outputPath);
    throw error;
  }
};
function isValidUrl(string) {
  try {
    if (!string || typeof string !== "string") {
      return false;
    }
    const url = new URL(string);
    return url.protocol === "http:" || url.protocol === "https:";
  } catch (e) {
    return false;
  }
}
function isBase64Image(string) {
  if (!string || typeof string !== "string") {
    return false;
  }
  const minLength = 1e3;
  console.log(`[Base64检测] 字符串长度: ${string.length}, 最小长度要求: ${minLength}`);
  return string.length > minLength;
}
function base64ToDataUrl(base64String, mimeType = "image/jpeg") {
  console.log("[Base64转换] 输入数据长度:", base64String?.length || 0);
  console.log("[Base64转换] 输入数据前50字符:", base64String?.substring(0, 50) || "N/A");
  if (base64String && base64String.startsWith("data:")) {
    console.log("[Base64转换] 已是data URL格式，直接返回");
    return base64String;
  }
  const cleanBase64 = base64String ? base64String.replace(/\s+/g, "") : "";
  const dataUrl = `data:${mimeType};base64,${cleanBase64}`;
  console.log("[Base64转换] 生成的data URL长度:", dataUrl.length);
  console.log("[Base64转换] 生成的data URL前100字符:", dataUrl.substring(0, 100));
  return dataUrl;
}
function fileToBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
}
async function optimizePrompt(chinesePrompt) {
  try {
    console.log("[DeepSeek] 开始优化提示词:", chinesePrompt);
    const result = await window.electronApiClient.deepseekApi("/chat/completions", {
      method: "POST",
      body: JSON.stringify({
        model: "deepseek-chat",
        messages: [
          {
            role: "system",
            content: "You are a professional prompt engineer specializing in image transformation prompts for FLUX models. Convert Chinese prompts into optimized English prompts that describe artistic styles, transformations, and visual effects. Focus on clear, specific descriptions."
          },
          {
            role: "user",
            content: `Convert this Chinese prompt into an optimized English prompt for FLUX image transformation: "${chinesePrompt}". Return only the English prompt without explanations.`
          }
        ],
        temperature: 0.7,
        max_tokens: 150
      })
    });
    if (!result.success) {
      throw new Error(result.error);
    }
    const englishPrompt = result.data.choices[0].message.content.trim();
    console.log("[DeepSeek] 优化后的提示词:", englishPrompt);
    return englishPrompt;
  } catch (error) {
    console.error("[DeepSeek] 提示词优化失败:", error);
    return chinesePrompt;
  }
}
function calculateAspectRatio(width, height) {
  const ratio = width / height;
  if (Math.abs(ratio - 1) < 0.1) return "1:1";
  if (Math.abs(ratio - 16 / 9) < 0.1) return "16:9";
  if (Math.abs(ratio - 9 / 16) < 0.1) return "9:16";
  if (Math.abs(ratio - 4 / 3) < 0.1) return "4:3";
  if (Math.abs(ratio - 3 / 4) < 0.1) return "3:4";
  if (Math.abs(ratio - 3 / 2) < 0.1) return "3:2";
  if (Math.abs(ratio - 2 / 3) < 0.1) return "2:3";
  if (Math.abs(ratio - 21 / 9) < 0.1) return "21:9";
  if (Math.abs(ratio - 9 / 21) < 0.1) return "9:21";
  return ratio < 1 ? "9:16" : "16:9";
}
const generateBatchId = () => {
  return `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};
const generateFileName = (originalName, seed, outputFormat) => {
  const timestamp = (/* @__PURE__ */ new Date()).toISOString().replace(/[:.]/g, "-").split("T")[0];
  const baseName = originalName.replace(/\.[^/.]+$/, "");
  return `converted_${baseName}_${timestamp}_${seed}.${outputFormat}`;
};
async function transformImage(imageFile, prompt, options = {}) {
  console.log("[Flux API] 开始转换图片:", { prompt, options });
  try {
    const finalPrompt = options.useDeepseek ? await optimizePrompt(prompt) : prompt;
    console.log("[Flux API] 使用提示词:", finalPrompt);
    const imageBase64 = await fileToBase64(imageFile);
    const img = new Image();
    const aspectRatio = await new Promise((resolve) => {
      img.onload = () => {
        const ratio = calculateAspectRatio(img.width, img.height);
        resolve(ratio);
      };
      img.src = imageBase64;
    });
    const seed = options.seed || Math.floor(Math.random() * 1e6);
    const input = {
      prompt: finalPrompt,
      input_image: imageBase64,
      aspect_ratio: options.aspect_ratio || aspectRatio || "match_input_image",
      output_format: options.output_format || "jpg",
      safety_tolerance: options.safety_tolerance ?? 2
    };
    if (seed) {
      input.seed = seed;
    }
    console.log("[Flux API] 输入参数:", input);
    const result = await window.electronApiClient.replicateRun("black-forest-labs/flux-kontext-pro", input);
    if (!result.success) {
      throw new Error(result.error);
    }
    const output = result.data;
    console.log("[Flux API] ====== 完整输出调试 ======");
    console.log("[Flux API] result对象:", result);
    console.log("[Flux API] result.data类型:", typeof output);
    console.log("[Flux API] result.data内容:", output);
    if (typeof output === "string") {
      console.log("[Flux API] 字符串长度:", output.length);
      console.log("[Flux API] 完整字符串内容:", output);
    }
    if (typeof output === "object") {
      console.log("[Flux API] 对象完整结构:", JSON.stringify(output, null, 2));
    }
    console.log("[Flux API] ====== 调试结束 ======");
    let imageData = null;
    let isBase64Data = false;
    console.log("[Flux API] 处理主进程返回的数据...");
    if (typeof output === "string") {
      if (isValidUrl(output)) {
        imageData = output;
        console.log("[Flux API] ✅ 接收到图片URL:", imageData);
      } else if (output.length > 100) {
        imageData = `data:image/${options.output_format || "jpeg"};base64,${output}`;
        isBase64Data = true;
        console.log("[Flux API] ✅ 输出格式: Base64字符串");
      } else {
        console.error("[Flux API] ❌ 字符串格式未知:", output);
      }
    } else {
      console.error("[Flux API] ❌ 期望字符串，实际接收:", typeof output);
      console.error("[Flux API] 输出内容:", output);
      return {
        success: false,
        error: "主进程返回了意外的数据格式",
        rawOutput: output,
        outputType: typeof output
      };
    }
    if (imageData && isValidUrl(imageData)) {
      console.log("[Flux API] ✅ 图片URL验证通过，准备显示图片");
    } else {
      console.error("[Flux API] ❌ 图片URL验证失败:", imageData);
      return {
        success: false,
        error: "URL验证失败",
        rawOutput: output,
        imageData
      };
    }
    const outputPath = await ensureOutputDirectory();
    const fileName = generateFileName(
      imageFile.name,
      seed,
      options.output_format || "jpg"
    );
    let localPath = null;
    try {
      console.log("[文件管理] 准备保存图片，数据类型:", isBase64Data ? "Base64" : "URL");
      if (isBase64Data) {
        localPath = await saveBase64Image(
          imageData,
          fileName,
          outputPath
        );
      } else {
        localPath = await downloadAndSaveImage(
          imageData,
          fileName,
          outputPath
        );
      }
    } catch (saveError) {
      console.warn("[文件管理] 保存到本地失败，但转换成功:", saveError);
      console.warn("[文件管理] 错误详情:", saveError.stack);
    }
    const finalResult = {
      success: true,
      output: imageData,
      // 图片数据（URL或DataURL）
      localPath,
      // 本地路径
      fileName,
      outputPath,
      seed,
      isBase64: isBase64Data
      // 标识数据类型
    };
    try {
      const batchId = generateBatchId();
      const historyData = {
        batchId,
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        prompt: finalPrompt,
        originalPrompt: prompt,
        options,
        total: 1,
        successful: 1,
        failed: 0,
        images: [{
          originalFileName: imageFile.name,
          fileName,
          localPath,
          output: imageData,
          seed
        }]
      };
      if (window.electronAPI) {
        await window.electronAPI.saveConversionHistory(historyData);
      }
      finalResult.batchId = batchId;
    } catch (error) {
      console.warn("[历史记录] 保存失败:", error);
    }
    return finalResult;
  } catch (error) {
    console.error("[Flux API] 转换失败:", error);
    throw error;
  }
}
async function transformImages(imageFiles, prompt, options = {}) {
  console.log("[Flux API] 开始批量转换:", {
    count: imageFiles.length,
    prompt,
    options
  });
  const results = [];
  const errors = [];
  const batchId = generateBatchId();
  const finalPrompt = options.useDeepseek ? await optimizePrompt(prompt) : prompt;
  console.log("[Flux API] 批量转换使用提示词:", finalPrompt);
  const outputPath = await ensureOutputDirectory();
  for (let i = 0; i < imageFiles.length; i++) {
    const file = imageFiles[i];
    try {
      console.log(`[Flux API] 转换第 ${i + 1}/${imageFiles.length} 张图片:`, file.name);
      const result = await transformImage(file, finalPrompt, {
        ...options,
        useDeepseek: false
        // 避免重复优化提示词
      });
      results.push({
        ...result,
        originalFileName: file.name,
        index: i,
        batchId
      });
      console.log(`[Flux API] 第 ${i + 1} 张图片转换成功`);
      if (i < imageFiles.length - 1) {
        await new Promise((resolve) => setTimeout(resolve, 3e3));
      }
    } catch (error) {
      console.error(`[Flux API] 第 ${i + 1} 张图片转换失败:`, error);
      errors.push({
        index: i,
        fileName: file.name,
        error: error.message
      });
    }
  }
  if (results.length > 0) {
    try {
      const historyData = {
        batchId,
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        prompt: finalPrompt,
        originalPrompt: prompt,
        options,
        total: imageFiles.length,
        successful: results.length,
        failed: errors.length,
        images: results.map((result) => ({
          originalFileName: result.originalFileName,
          fileName: result.fileName,
          localPath: result.localPath,
          output: result.output,
          seed: result.seed
        }))
      };
      if (window.electronAPI) {
        await window.electronAPI.saveConversionHistory(historyData);
      }
    } catch (error) {
      console.warn("[历史记录] 保存失败:", error);
    }
  }
  return {
    success: results.length > 0,
    results,
    errors,
    total: imageFiles.length,
    successful: results.length,
    failed: errors.length,
    outputPath,
    // 批量转换的输出目录
    batchId
  };
}
const fileManager = {
  // 打开输出文件夹
  async openOutputFolder(path) {
    if (window.electronAPI) {
      return await window.electronAPI.openFolder(path || getOutputPath());
    } else {
      console.warn("文件夹打开功能仅在桌面版本中可用");
      return false;
    }
  },
  // 获取今日输出目录
  getTodayOutputPath() {
    return getOutputPath();
  },
  // 列出输出目录中的文件
  async listOutputFiles(path) {
    if (window.electronAPI) {
      return await window.electronAPI.listFiles(path || getOutputPath());
    }
    return [];
  },
  // 清理旧文件（保留最近N天）
  async cleanOldFiles(daysToKeep = 7) {
    if (window.electronAPI) {
      return await window.electronAPI.cleanOldFiles(daysToKeep);
    }
    return false;
  }
};
const historyManager = {
  // 获取转换历史记录
  async getHistory() {
    if (window.electronAPI) {
      return await window.electronAPI.getConversionHistory();
    }
    return [];
  },
  // 删除历史记录
  async deleteHistory(batchId) {
    if (window.electronAPI) {
      return await window.electronAPI.deleteConversionHistory(batchId);
    }
    return false;
  },
  // 格式化历史记录显示
  formatHistoryItem(item) {
    const date = new Date(item.timestamp);
    return {
      ...item,
      displayDate: date.toLocaleDateString("zh-CN"),
      displayTime: date.toLocaleTimeString("zh-CN"),
      displayPrompt: item.prompt.length > 50 ? item.prompt.substring(0, 50) + "..." : item.prompt
    };
  }
};
const validateApiConfig = () => {
  const config = {
    replicate: {
      configured: !!window.electronApiClient,
      url: "Main Process API (CORS-free)"
    },
    deepseek: {
      configured: !!window.electronApiClient,
      url: "Main Process API (CORS-free)"
    }
  };
  console.log("[API配置] 当前配置状态:", config);
  return config;
};
const api = {
  // 转换单张图片
  async transformImage(imageFile, prompt, options = {}) {
    return transformImage(imageFile, prompt, options);
  },
  // 批量转换图片
  async transformImages(imageFiles, prompt, options = {}) {
    return transformImages(imageFiles, prompt, options);
  },
  // 文件管理
  fileManager,
  // 历史记录管理
  historyManager,
  // 工具方法
  fileToBase64,
  isValidUrl,
  isBase64Image,
  base64ToDataUrl,
  optimizePrompt,
  validateApiConfig
};
function LoadingIndicator({ size = "medium", text = "加载中..." }) {
  const sizeClass = {
    small: "loading-indicator-small",
    medium: "loading-indicator-medium",
    large: "loading-indicator-large"
  }[size] || "loading-indicator-medium";
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "loading-indicator", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: `loading-spinner ${sizeClass}`, children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "spinner-dot" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "spinner-dot" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "spinner-dot" })
    ] }),
    text && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "loading-text", children: text })
  ] });
}
LoadingIndicator.propTypes = {
  size: PropTypes.oneOf(["small", "medium", "large"]),
  text: PropTypes.string
};
function ImageModal({ image, alt, onClose }) {
  reactExports.useEffect(() => {
    const handleEsc = (event) => {
      if (event.key === "Escape") {
        onClose();
      }
    };
    window.addEventListener("keydown", handleEsc);
    return () => {
      window.removeEventListener("keydown", handleEsc);
    };
  }, [onClose]);
  return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "image-modal-overlay", onClick: onClose, children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "image-modal-content", onClick: (e) => e.stopPropagation(), children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx("button", { className: "image-modal-close", onClick: onClose, children: "×" }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("img", { src: image, alt, className: "image-modal-image" })
  ] }) });
}
ImageModal.propTypes = {
  image: PropTypes.string.isRequired,
  alt: PropTypes.string,
  onClose: PropTypes.func.isRequired
};
function HistoryViewer({ onClose, onSelectBatch }) {
  const [history, setHistory] = reactExports.useState([]);
  const [loading, setLoading] = reactExports.useState(true);
  const [selectedBatch, setSelectedBatch] = reactExports.useState(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = reactExports.useState(null);
  reactExports.useEffect(() => {
    loadHistory();
  }, []);
  const loadHistory = async () => {
    try {
      setLoading(true);
      const historyData = await api.historyManager.getHistory();
      const formattedHistory = historyData.map(
        (item) => api.historyManager.formatHistoryItem(item)
      );
      setHistory(formattedHistory);
    } catch (error) {
      console.error("加载历史记录失败:", error);
    } finally {
      setLoading(false);
    }
  };
  const deleteHistory = async (batchId) => {
    try {
      const success = await api.historyManager.deleteHistory(batchId);
      if (success) {
        setHistory((prev) => prev.filter((item) => item.batchId !== batchId));
        setShowDeleteConfirm(null);
      } else {
        alert("删除失败");
      }
    } catch (error) {
      console.error("删除历史记录失败:", error);
      alert("删除失败");
    }
  };
  const viewBatchDetails = (batch) => {
    setSelectedBatch(batch);
  };
  const useBatch = (batch) => {
    if (onSelectBatch) {
      onSelectBatch(batch);
    }
    onClose();
  };
  if (loading) {
    return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "history-viewer-overlay", children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "history-viewer", children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "loading", children: "加载历史记录中..." }) }) });
  }
  return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "history-viewer-overlay", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "history-viewer", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "history-header", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("h2", { children: "转换历史记录" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("button", { className: "close-btn", onClick: onClose, children: "✕" })
    ] }),
    history.length === 0 ? /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "empty-history", children: /* @__PURE__ */ jsxRuntimeExports.jsx("p", { children: "暂无转换历史记录" }) }) : /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "history-content", children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "history-list", children: history.map((item) => /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "history-item", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "history-item-header", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "history-meta", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "history-date", children: [
            item.displayDate,
            " ",
            item.displayTime
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "history-stats", children: [
            item.successful,
            "/",
            item.total,
            " 张成功"
          ] })
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "history-actions", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "button",
            {
              className: "view-btn",
              onClick: () => viewBatchDetails(item),
              children: "查看详情"
            }
          ),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "button",
            {
              className: "use-btn",
              onClick: () => useBatch(item),
              children: "重新使用"
            }
          ),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "button",
            {
              className: "delete-btn",
              onClick: () => setShowDeleteConfirm(item.batchId),
              children: "删除"
            }
          )
        ] })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "history-prompt", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("strong", { children: "提示词:" }),
        " ",
        item.displayPrompt
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "history-images-preview", children: [
        item.images.slice(0, 4).map((image, index) => /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "preview-image", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
          "img",
          {
            src: image.output,
            alt: image.originalFileName,
            onError: (e) => {
              e.target.style.display = "none";
            }
          }
        ) }, index)),
        item.images.length > 4 && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "more-images", children: [
          "+",
          item.images.length - 4
        ] })
      ] })
    ] }, item.batchId)) }) }),
    selectedBatch && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "batch-detail-overlay", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "batch-detail", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "batch-detail-header", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { children: "批次详情" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "button",
          {
            className: "close-btn",
            onClick: () => setSelectedBatch(null),
            children: "✕"
          }
        )
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "batch-detail-content", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "batch-info", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("p", { children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("strong", { children: "时间:" }),
            " ",
            selectedBatch.displayDate,
            " ",
            selectedBatch.displayTime
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("p", { children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("strong", { children: "原始提示词:" }),
            " ",
            selectedBatch.originalPrompt
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("p", { children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("strong", { children: "最终提示词:" }),
            " ",
            selectedBatch.prompt
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("p", { children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("strong", { children: "转换结果:" }),
            " ",
            selectedBatch.successful,
            "/",
            selectedBatch.total,
            " 张成功"
          ] })
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "batch-images", children: selectedBatch.images.map((image, index) => /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "batch-image-item", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "img",
            {
              src: image.output,
              alt: image.originalFileName,
              onError: (e) => {
                e.target.style.display = "none";
              }
            }
          ),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "image-info", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "image-name", children: image.originalFileName }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "image-seed", children: [
              "种子: ",
              image.seed
            ] }),
            image.localPath && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "local-status", children: "✅ 已保存到本地" })
          ] })
        ] }, index)) })
      ] })
    ] }) }),
    showDeleteConfirm && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "delete-confirm-overlay", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "delete-confirm", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { children: "确认删除" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("p", { children: "确定要删除这个转换批次的历史记录吗？" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "warning", children: "注意：这不会删除本地保存的图片文件" }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "confirm-actions", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "button",
          {
            className: "cancel-btn",
            onClick: () => setShowDeleteConfirm(null),
            children: "取消"
          }
        ),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "button",
          {
            className: "confirm-btn",
            onClick: () => deleteHistory(showDeleteConfirm),
            children: "确认删除"
          }
        )
      ] })
    ] }) })
  ] }) });
}
HistoryViewer.propTypes = {
  onClose: PropTypes.func.isRequired,
  onSelectBatch: PropTypes.func
};
function ImageConverter() {
  const [images, setImages] = reactExports.useState([]);
  const [prompt, setPrompt] = reactExports.useState("Transform this into Studio Ghibli anime style, maintain temporal continuity and smooth transitions");
  const [processing, setProcessing] = reactExports.useState(false);
  const [results, setResults] = reactExports.useState([]);
  const [selectedImage, setSelectedImage] = reactExports.useState(null);
  const [useDeepseek, setUseDeepseek] = reactExports.useState(true);
  const [aspectRatio, setAspectRatio] = reactExports.useState("match_input_image");
  const [outputFormat, setOutputFormat] = reactExports.useState("jpg");
  const [safetyTolerance, setSafetyTolerance] = reactExports.useState(2);
  const [seed, setSeed] = reactExports.useState("42");
  const [guidanceScale, setGuidanceScale] = reactExports.useState(3.5);
  const [numInferenceSteps, setNumInferenceSteps] = reactExports.useState(25);
  const [stylePreset, setStylePreset] = reactExports.useState("ghibli");
  const [enableFrameContinuity, setEnableFrameContinuity] = reactExports.useState(true);
  const [apiConfig, setApiConfig] = reactExports.useState(null);
  const [stats, setStats] = reactExports.useState({
    total: 0,
    successful: 0,
    failed: 0,
    processing: 0
  });
  const [outputPath, setOutputPath] = reactExports.useState("");
  const [showFileManager, setShowFileManager] = reactExports.useState(false);
  const [showHistoryViewer, setShowHistoryViewer] = reactExports.useState(false);
  const fileInputRef = reactExports.useRef(null);
  reactExports.useEffect(() => {
    const checkApiConfig = () => {
      const config = api.validateApiConfig();
      setApiConfig(config);
    };
    checkApiConfig();
  }, []);
  const handleFileSelect = (event) => {
    const files = Array.from(event.target.files);
    const imageFiles = files.filter((file) => file.type.startsWith("image/"));
    if (imageFiles.length === 0) {
      alert("请选择图片文件");
      return;
    }
    setImages(imageFiles);
    setResults([]);
    console.log("选择的图片:", imageFiles.map((f) => f.name));
  };
  const removeImage = (index) => {
    setImages((prev) => prev.filter((_, i) => i !== index));
    setResults((prev) => prev.filter((r) => r.index !== index));
  };
  const clearImages = () => {
    setImages([]);
    setResults([]);
    setStats({ total: 0, successful: 0, failed: 0, processing: 0 });
  };
  const handleConvert = async () => {
    if (images.length === 0) {
      alert("请先选择图片");
      return;
    }
    if (stylePreset === "custom" && !prompt.trim()) {
      alert("请输入自定义转换提示词");
      return;
    }
    setProcessing(true);
    setResults([]);
    setStats({
      total: images.length,
      successful: 0,
      failed: 0,
      processing: images.length
    });
    try {
      const finalPrompt = getFinalPrompt();
      const options = {
        useDeepseek: stylePreset === "custom" ? useDeepseek : false,
        // 预设风格不需要优化
        aspect_ratio: aspectRatio === "match_input_image" ? void 0 : aspectRatio,
        output_format: outputFormat,
        safety_tolerance: safetyTolerance,
        guidance_scale: guidanceScale,
        // 新增脚本参数
        num_inference_steps: numInferenceSteps,
        // 新增脚本参数
        seed: seed ? parseInt(seed) : void 0,
        enable_frame_continuity: enableFrameContinuity
      };
      if (images.length === 1) {
        console.log("开始单张图片转换");
        const result = await api.transformImage(images[0], finalPrompt, options);
        setResults([{
          ...result,
          originalFileName: images[0].name,
          index: 0
        }]);
        setStats({
          total: 1,
          successful: 1,
          failed: 0,
          processing: 0
        });
        if (result.outputPath) {
          setOutputPath(result.outputPath);
        }
      } else {
        console.log("开始批量转换");
        const batchResult = await api.transformImages(images, finalPrompt, options);
        setResults(batchResult.results);
        setStats({
          total: batchResult.total,
          successful: batchResult.successful,
          failed: batchResult.failed,
          processing: 0
        });
        if (batchResult.outputPath) {
          setOutputPath(batchResult.outputPath);
        }
        if (batchResult.errors.length > 0) {
          console.error("转换错误:", batchResult.errors);
        }
      }
    } catch (error) {
      console.error("转换失败:", error);
      alert(`转换失败: ${error.message}`);
      setStats((prev) => ({
        ...prev,
        processing: 0,
        failed: prev.total - prev.successful
      }));
    } finally {
      setProcessing(false);
    }
  };
  const downloadResult = (result) => {
    try {
      const link = document.createElement("a");
      link.href = result.output;
      link.download = `converted_${result.originalFileName}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error("下载失败:", error);
      alert("下载失败");
    }
  };
  const downloadAll = () => {
    results.forEach((result, index) => {
      setTimeout(() => downloadResult(result), index * 100);
    });
  };
  const openOutputFolder = async () => {
    try {
      const success = await api.fileManager.openOutputFolder(outputPath);
      if (!success) {
        alert("打开文件夹失败，可能不在桌面版本中运行");
      }
    } catch (error) {
      console.error("打开文件夹失败:", error);
      alert("打开文件夹失败");
    }
  };
  const handleHistorySelect = (batch) => {
    setPrompt(batch.originalPrompt);
    if (batch.options) {
      if (batch.options.aspect_ratio) setAspectRatio(batch.options.aspect_ratio);
      if (batch.options.output_format) setOutputFormat(batch.options.output_format);
      if (batch.options.safety_tolerance !== void 0) setSafetyTolerance(batch.options.safety_tolerance);
      if (batch.options.guidance_scale !== void 0) setGuidanceScale(batch.options.guidance_scale);
      if (batch.options.num_inference_steps !== void 0) setNumInferenceSteps(batch.options.num_inference_steps);
      if (batch.options.enable_frame_continuity !== void 0) setEnableFrameContinuity(batch.options.enable_frame_continuity);
      if (batch.options.useDeepseek !== void 0) setUseDeepseek(batch.options.useDeepseek);
    }
    const historyResults = batch.images.map((image, index) => ({
      ...image,
      index,
      batchId: batch.batchId
    }));
    setResults(historyResults);
    setStats({
      total: batch.total,
      successful: batch.successful,
      failed: batch.failed,
      processing: 0
    });
    alert(`已加载历史批次：${batch.successful}/${batch.total} 张图片`);
  };
  const getTodayOutputPath = () => {
    return api.fileManager.getTodayOutputPath();
  };
  const cleanOldFiles = async () => {
    if (confirm("确定要清理7天前的旧文件吗？此操作不可撤销。")) {
      try {
        const success = await api.fileManager.cleanOldFiles(7);
        if (success) {
          alert("清理完成");
        } else {
          alert("清理失败");
        }
      } catch (error) {
        console.error("清理失败:", error);
        alert("清理失败");
      }
    }
  };
  const stylePrompts = {
    ghibli: "Transform this into Studio Ghibli anime style, maintain high quality and artistic consistency, Hayao Miyazaki art style, soft watercolor painting, magical atmosphere, hand-drawn animation feel, whimsical details, preserve temporal continuity, beautiful composition, ensure natural seamless edges without visible borders or white lines, blend all areas smoothly",
    shinkai: "Convert this to Makoto Shinkai anime style, maintain visual consistency and high quality, Your Name movie aesthetic, beautiful detailed sky, dramatic lighting, photorealistic anime, cinematic composition, preserve character identity and scene continuity, ensure seamless borders and natural edge transitions, no white or harsh boundaries",
    anime: "Make this into high quality anime style, maintain consistent art style across sequence, cel shading, vibrant colors, traditional Japanese animation, detailed character art, clean lines, preserve temporal flow and character consistency, ensure smooth edge transitions and natural borders without white edges or visible seams",
    realistic: "Transform into photorealistic style, maintain high quality and natural appearance, professional photography, cinematic lighting, detailed textures, preserve temporal continuity, ensure natural edge blending and seamless transitions, avoid any artificial borders or white edges",
    oil_painting: "Convert to oil painting style, artistic brushstrokes, rich colors, traditional art technique, maintain composition and temporal flow, blend edges naturally with painterly transitions, avoid hard borders or white edges, ensure seamless artistic continuity"
  };
  const getFinalPrompt = () => {
    if (stylePreset !== "custom") {
      const basePrompt = stylePrompts[stylePreset];
      if (enableFrameContinuity && images.length > 1) {
        return `${basePrompt}, maintain consistency across video frames`;
      }
      return basePrompt;
    }
    return prompt;
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "image-converter", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "converter-header", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("h1", { children: "视频帧图像风格转换工具" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("p", { children: "5种预设卡通风格 | 完美的视频帧连续性" })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "converter-content", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "input-section", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "input-group", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("label", { children: "选择图片" }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "file-input-wrapper", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "file",
                ref: fileInputRef,
                onChange: handleFileSelect,
                multiple: true,
                accept: "image/*",
                style: { display: "none" }
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "button",
              {
                className: "file-select-btn",
                onClick: () => fileInputRef.current?.click(),
                disabled: processing,
                children: images.length === 0 ? "选择图片" : `已选择 ${images.length} 张图片`
              }
            ),
            images.length > 0 && /* @__PURE__ */ jsxRuntimeExports.jsx(
              "button",
              {
                className: "clear-btn",
                onClick: clearImages,
                disabled: processing,
                children: "清空"
              }
            )
          ] })
        ] }),
        images.length > 0 && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "image-preview-section", children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "preview-grid", children: images.map((image, index) => /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "preview-item", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "img",
            {
              src: URL.createObjectURL(image),
              alt: image.name,
              className: "preview-image"
            }
          ),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "preview-info", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "image-name", children: image.name }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "button",
              {
                className: "remove-btn",
                onClick: () => removeImage(index),
                disabled: processing,
                children: "×"
              }
            )
          ] })
        ] }, index)) }) }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "input-group", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("label", { children: "转换风格" }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs(
            "select",
            {
              value: stylePreset,
              onChange: (e) => setStylePreset(e.target.value),
              disabled: processing,
              children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: "ghibli", children: "🎨 宫崎骏/吉卜力工作室风格 (推荐)" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: "shinkai", children: "🌸 新海诚风格 (唯美)" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: "anime", children: "📺 传统动漫风格" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: "realistic", children: "📷 写实风格" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: "oil_painting", children: "🖼️ 油画风格" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: "custom", children: "✏️ 自定义提示词" })
              ]
            }
          )
        ] }),
        stylePreset === "custom" && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "input-group", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("label", { children: "自定义转换提示词" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "textarea",
            {
              value: prompt,
              onChange: (e) => setPrompt(e.target.value),
              placeholder: "描述你想要的转换效果，例如：转换为动漫风格，保持帧间连续性",
              rows: 3,
              disabled: processing
            }
          )
        ] }),
        stylePreset !== "custom" && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "style-preview", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("label", { children: "当前风格预设:" }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "style-prompt-preview", children: [
            stylePrompts[stylePreset]?.substring(0, 100),
            "..."
          ] })
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "params-section", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { children: "转换参数 (匹配 convert_video.py 脚本)" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "param-row simple", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("label", { children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "checkbox",
                checked: enableFrameContinuity,
                onChange: (e) => setEnableFrameContinuity(e.target.checked),
                disabled: processing
              }
            ),
            "启用帧间连续性优化 (视频帧必备)"
          ] }) }),
          stylePreset === "custom" && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "param-row simple", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("label", { children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "checkbox",
                checked: useDeepseek,
                onChange: (e) => setUseDeepseek(e.target.checked),
                disabled: processing
              }
            ),
            "使用DeepSeek优化提示词"
          ] }) }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "param-row has-control", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("label", { children: "宽高比" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "param-control-row", children: /* @__PURE__ */ jsxRuntimeExports.jsxs(
              "select",
              {
                value: aspectRatio,
                onChange: (e) => setAspectRatio(e.target.value),
                disabled: processing,
                children: [
                  /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: "match_input_image", children: "匹配输入图像" }),
                  /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: "1:1", children: "1:1 (正方形)" }),
                  /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: "16:9", children: "16:9 (横屏)" }),
                  /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: "9:16", children: "9:16 (竖屏)" }),
                  /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: "4:3", children: "4:3" }),
                  /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: "3:4", children: "3:4" }),
                  /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: "21:9", children: "21:9 (超宽屏)" })
                ]
              }
            ) })
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "param-row has-control", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("label", { children: "输出格式" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "param-control-row", children: /* @__PURE__ */ jsxRuntimeExports.jsxs(
              "select",
              {
                value: outputFormat,
                onChange: (e) => setOutputFormat(e.target.value),
                disabled: processing,
                children: [
                  /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: "jpg", children: "JPG" }),
                  /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: "png", children: "PNG" }),
                  /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: "webp", children: "WebP" })
                ]
              }
            ) })
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "param-row has-control", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("label", { children: "安全容忍度 (0-6)" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "param-control-row", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "param-input-group", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(
                "input",
                {
                  type: "range",
                  min: "0",
                  max: "6",
                  value: safetyTolerance,
                  onChange: (e) => setSafetyTolerance(parseInt(e.target.value)),
                  disabled: processing
                }
              ),
              /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "param-value", children: safetyTolerance })
            ] }) })
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "param-row has-control", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("label", { children: "基础种子 (连续性的关键)" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "param-control-row", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "number",
                value: seed,
                onChange: (e) => setSeed(e.target.value),
                placeholder: "脚本默认42，建议使用固定值",
                disabled: processing
              }
            ) })
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "advanced-params", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "param-divider", children: /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: "高级参数 (来自脚本优化)" }) }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "param-row has-control", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("label", { children: "引导强度 (Guidance Scale)" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "param-control-row", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "param-input-group", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx(
                  "input",
                  {
                    type: "range",
                    min: "1",
                    max: "10",
                    step: "0.1",
                    value: guidanceScale,
                    onChange: (e) => setGuidanceScale(parseFloat(e.target.value)),
                    disabled: processing
                  }
                ),
                /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "param-value", children: guidanceScale })
              ] }) }),
              /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "param-description", children: "脚本默认3.5，降低引导强度保持帧间一致性" })
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "param-row has-control", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("label", { children: "推理步数 (Inference Steps)" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "param-control-row", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "param-input-group", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx(
                  "input",
                  {
                    type: "range",
                    min: "10",
                    max: "50",
                    value: numInferenceSteps,
                    onChange: (e) => setNumInferenceSteps(parseInt(e.target.value)),
                    disabled: processing
                  }
                ),
                /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "param-value", children: numInferenceSteps })
              ] }) }),
              /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "param-description", children: "脚本默认25步，适中的推理步数平衡质量和速度" })
            ] })
          ] })
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "button",
          {
            className: "convert-btn",
            onClick: handleConvert,
            disabled: processing || images.length === 0 || stylePreset === "custom" && !prompt.trim(),
            children: processing ? "转换中..." : `转换 ${images.length} 张图片`
          }
        )
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "results-section", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "results-header", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { children: "转换结果" }),
          stats.total > 0 && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "stats", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { children: [
              "总计: ",
              stats.total
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { children: [
              "成功: ",
              stats.successful
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { children: [
              "失败: ",
              stats.failed
            ] }),
            stats.processing > 0 && /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { children: [
              "处理中: ",
              stats.processing
            ] })
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "results-actions", children: [
            results.length > 0 && /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("button", { className: "download-all-btn", onClick: downloadAll, children: "下载全部" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx("button", { className: "open-folder-btn", onClick: openOutputFolder, children: "📁 打开文件夹" })
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "button",
              {
                className: "file-manager-btn",
                onClick: () => setShowFileManager(!showFileManager),
                children: "🛠️ 文件管理"
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "button",
              {
                className: "history-btn",
                onClick: () => setShowHistoryViewer(true),
                children: "📚 历史记录"
              }
            )
          ] })
        ] }),
        showFileManager && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "file-manager-panel", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "file-info", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("h4", { children: "文件存储信息" }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "path-info", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("label", { children: "今日输出目录:" }),
              /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "path-display", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx("code", { children: getTodayOutputPath() }),
                /* @__PURE__ */ jsxRuntimeExports.jsx("button", { className: "copy-path-btn", onClick: () => {
                  navigator.clipboard.writeText(getTodayOutputPath());
                  alert("路径已复制到剪贴板");
                }, children: "📋" })
              ] })
            ] }),
            outputPath && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "path-info", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("label", { children: "当前批次目录:" }),
              /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "path-display", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx("code", { children: outputPath }),
                /* @__PURE__ */ jsxRuntimeExports.jsx("button", { className: "copy-path-btn", onClick: () => {
                  navigator.clipboard.writeText(outputPath);
                  alert("路径已复制到剪贴板");
                }, children: "📋" })
              ] })
            ] })
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "file-actions", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("button", { className: "clean-btn", onClick: cleanOldFiles, children: "🗑️ 清理旧文件" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("button", { className: "open-folder-btn", onClick: openOutputFolder, children: "📁 打开当前文件夹" })
          ] })
        ] }),
        processing && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "processing-indicator", children: /* @__PURE__ */ jsxRuntimeExports.jsx(LoadingIndicator, { size: "large", text: "正在转换图片..." }) }),
        results.length > 0 && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "results-grid", children: results.map((result, index) => /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "result-item", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "result-image-container", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
            "img",
            {
              src: result.output,
              alt: `转换结果 ${index + 1}`,
              className: "result-image",
              onClick: () => setSelectedImage(result.output)
            }
          ) }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "result-info", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "result-name", children: result.originalFileName }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "result-meta", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
                "种子: ",
                result.seed
              ] }),
              result.fileName && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "local-file", children: [
                "📄 ",
                result.fileName
              ] }),
              result.localPath && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "local-status", children: "✅ 已保存到本地" })
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "result-actions", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(
                "button",
                {
                  className: "download-btn",
                  onClick: () => downloadResult(result),
                  title: "从远程URL下载",
                  children: "🔗 远程下载"
                }
              ),
              result.localPath && /* @__PURE__ */ jsxRuntimeExports.jsx(
                "button",
                {
                  className: "open-local-btn",
                  onClick: () => {
                    navigator.clipboard.writeText(result.localPath);
                    alert("本地路径已复制到剪贴板");
                  },
                  title: "复制本地路径",
                  children: "📋 复制路径"
                }
              )
            ] })
          ] })
        ] }, index)) }),
        results.length === 0 && !processing && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "empty-results", children: /* @__PURE__ */ jsxRuntimeExports.jsx("p", { children: "选择图片并点击转换开始使用" }) })
      ] })
    ] }),
    selectedImage && /* @__PURE__ */ jsxRuntimeExports.jsx(
      ImageModal,
      {
        src: selectedImage,
        alt: "转换结果预览",
        onClose: () => setSelectedImage(null)
      }
    ),
    showHistoryViewer && /* @__PURE__ */ jsxRuntimeExports.jsx(
      HistoryViewer,
      {
        onClose: () => setShowHistoryViewer(false),
        onSelectBatch: handleHistorySelect
      }
    )
  ] });
}
ImageConverter.propTypes = {};
const cloud = "data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'?%3e%3csvg%20width='48'%20height='24'%20viewBox='0%200%2048%2024'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill='currentColor'%20d='M8%2016c0%204.4%203.6%208%208%208h16c4.4%200%208-3.6%208-8%200-3.7-2.5-6.8-6-7.7%200-0.1%200-0.2%200-0.3%200-4.4-3.6-8-8-8-3.7%200-6.8%202.5-7.7%206C18.2%206%2018.1%206%2018%206c-4.4%200-8%203.6-8%208%200%200.7%200.1%201.4%200.2%202H8z'%20opacity='0.15'/%3e%3c/svg%3e";
function GhibliDecorations() {
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "ghibli-decorations", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "cloud cloud-1", children: /* @__PURE__ */ jsxRuntimeExports.jsx("img", { src: cloud, alt: "cloud", className: "decoration-svg" }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "cloud cloud-2", children: /* @__PURE__ */ jsxRuntimeExports.jsx("img", { src: cloud, alt: "cloud", className: "decoration-svg" }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "cloud cloud-3", children: /* @__PURE__ */ jsxRuntimeExports.jsx("img", { src: cloud, alt: "cloud", className: "decoration-svg" }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "cloud cloud-4", children: /* @__PURE__ */ jsxRuntimeExports.jsx("img", { src: cloud, alt: "cloud", className: "decoration-svg" }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "cloud cloud-5", children: /* @__PURE__ */ jsxRuntimeExports.jsx("img", { src: cloud, alt: "cloud", className: "decoration-svg" }) })
  ] });
}
function App() {
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "app-container", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(GhibliDecorations, {}),
    /* @__PURE__ */ jsxRuntimeExports.jsx(ImageConverter, {})
  ] });
}
const style = document.createElement("style");
style.textContent = `
  .app-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100vh;
    overflow: hidden;
    position: relative;
  }
`;
document.head.appendChild(style);
ReactDOM.createRoot(document.getElementById("root")).render(
  /* @__PURE__ */ jsxRuntimeExports.jsx(React.StrictMode, { children: /* @__PURE__ */ jsxRuntimeExports.jsx(App, {}) })
);
