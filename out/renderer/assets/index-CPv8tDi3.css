:root {
  /* 吉卜力风格主色调 */
  --color-natural-green: #4F7942;
  --color-sky-blue: #87CEEB;
  --color-warm-orange: #FFA500;
  --color-wood-brown: #8B4513;
  --color-soft-beige: #F5F5DC;

  /* 辅助色 */
  --color-forest-green: #228B22;
  --color-sunset-purple: #DDA0DD;
  --color-moonlight-silver: #E6E6FA;
  --color-flame-red: #FF4500;

  /* 功能色 */
  --color-primary: var(--color-natural-green);
  --color-secondary: var(--color-sky-blue);
  --color-accent: var(--color-warm-orange);
  --color-background: var(--color-soft-beige);
  --color-text: #333333;
  --color-text-light: #666666;
  --color-border: rgba(139, 69, 19, 0.3);

  /* 字体 */
  --font-family-sans: 'Noto Sans CJK SC', 'Avenir', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-family-serif: 'Noto Serif CJK SC', 'Century Old Style', serif;

  /* 字体大小 */
  --font-size-large: 32px;
  --font-size-title: 24px;
  --font-size-subtitle: 20px;
  --font-size-body: 16px;
  --font-size-small: 14px;
  --font-size-mini: 12px;

  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;

  /* 圆角 */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 16px;

  /* 阴影 */
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.2);

  /* 动画 */
  --transition-fast: 150ms ease;
  --transition-normal: 300ms cubic-bezier(0.45, 0.05, 0.55, 0.95);
  --transition-slow: 450ms cubic-bezier(0.4, 0, 0.2, 1);
}

.image-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.85);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
  animation: modal-fade-in 0.2s ease-out;
}

.image-modal-content {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

.image-modal-image {
  display: block;
  max-width: 100%;
  max-height: 90vh;
  object-fit: contain;
}

.image-modal-close {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  border: none;
  color: white;
  font-size: 24px;
  line-height: 1;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
}

.image-modal-close:hover {
  background-color: rgba(0, 0, 0, 0.7);
  transform: scale(1.1);
}

@keyframes modal-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
} :root {
  /* 吉卜力风格主色调 */
  --color-natural-green: #4F7942;
  --color-sky-blue: #87CEEB;
  --color-warm-orange: #FFA500;
  --color-wood-brown: #8B4513;
  --color-soft-beige: #F5F5DC;

  /* 辅助色 */
  --color-forest-green: #228B22;
  --color-sunset-purple: #DDA0DD;
  --color-moonlight-silver: #E6E6FA;
  --color-flame-red: #FF4500;

  /* 功能色 */
  --color-primary: var(--color-natural-green);
  --color-secondary: var(--color-sky-blue);
  --color-accent: var(--color-warm-orange);
  --color-background: var(--color-soft-beige);
  --color-text: #333333;
  --color-text-light: #666666;
  --color-border: rgba(139, 69, 19, 0.3);

  /* 字体 */
  --font-family-sans: 'Noto Sans CJK SC', 'Avenir', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-family-serif: 'Noto Serif CJK SC', 'Century Old Style', serif;

  /* 字体大小 */
  --font-size-large: 32px;
  --font-size-title: 24px;
  --font-size-subtitle: 20px;
  --font-size-body: 16px;
  --font-size-small: 14px;
  --font-size-mini: 12px;

  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;

  /* 圆角 */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 16px;

  /* 阴影 */
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.2);

  /* 动画 */
  --transition-fast: 150ms ease;
  --transition-normal: 300ms cubic-bezier(0.45, 0.05, 0.55, 0.95);
  --transition-slow: 450ms cubic-bezier(0.4, 0, 0.2, 1);
}

.message-item {
  display: flex;
  margin-bottom: var(--spacing-lg);
  animation: message-fade-in 0.3s ease-out;
  position: relative;
}

.user-message {
  flex-direction: row-reverse;
}

.ai-message, .system-message {
  flex-direction: row;
}

.message-avatar {
  flex-shrink: 0;
  margin: 0 var(--spacing-md);
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-mini);
  color: white;
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
}

.avatar:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20viewBox='0%200%201422%20800'%20opacity='0.3'%3e%3cdefs%3e%3clinearGradient%20x1='50%25'%20y1='0%25'%20x2='50%25'%20y2='100%25'%20id='oooscillate-grad'%3e%3cstop%20stop-color='hsl(206,%2075%25,%2049%25)'%20stop-opacity='1'%20offset='0%25'%3e%3c/stop%3e%3cstop%20stop-color='hsl(331,%2090%25,%2056%25)'%20stop-opacity='1'%20offset='100%25'%3e%3c/stop%3e%3c/linearGradient%3e%3c/defs%3e%3cg%20stroke-width='1'%20stroke='url(%23oooscillate-grad)'%20fill='none'%20stroke-linecap='round'%3e%3cpath%20d='M%200%20448%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20448'%20opacity='0.05'%3e%3c/path%3e%3cpath%20d='M%200%20420%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20420'%20opacity='0.11'%3e%3c/path%3e%3cpath%20d='M%200%20392%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20392'%20opacity='0.18'%3e%3c/path%3e%3cpath%20d='M%200%20364%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20364'%20opacity='0.24'%3e%3c/path%3e%3cpath%20d='M%200%20336%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20336'%20opacity='0.30'%3e%3c/path%3e%3cpath%20d='M%200%20308%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20308'%20opacity='0.37'%3e%3c/path%3e%3cpath%20d='M%200%20280%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20280'%20opacity='0.43'%3e%3c/path%3e%3cpath%20d='M%200%20252%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20252'%20opacity='0.49'%3e%3c/path%3e%3cpath%20d='M%200%20224%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20224'%20opacity='0.56'%3e%3c/path%3e%3cpath%20d='M%200%20196%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20196'%20opacity='0.62'%3e%3c/path%3e%3cpath%20d='M%200%20168%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20168'%20opacity='0.68'%3e%3c/path%3e%3cpath%20d='M%200%20140%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20140'%20opacity='0.75'%3e%3c/path%3e%3cpath%20d='M%200%20112%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20112'%20opacity='0.81'%3e%3c/path%3e%3cpath%20d='M%200%2084%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%2084'%20opacity='0.87'%3e%3c/path%3e%3cpath%20d='M%200%2056%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%2056'%20opacity='0.94'%3e%3c/path%3e%3c/g%3e%3c/svg%3e");
  background-size: 60px;
  opacity: 0.1;
  mix-blend-mode: overlay;
}

.user-avatar {
  background-color: var(--color-sky-blue);
}

.ai-avatar {
  background-color: var(--color-natural-green);
}

.system-avatar {
  background-color: var(--color-warm-orange);
}

.message-content {
  max-width: 70%;
  display: flex;
  flex-direction: column;
}

.user-message .message-content {
  align-items: flex-end;
}

.message-header {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-xs);
  font-size: var(--font-size-small);
}

.user-message .message-header {
  flex-direction: row-reverse;
}

.message-sender {
  font-weight: bold;
  color: var(--color-text);
}

.message-time {
  color: var(--color-text-light);
  margin: 0 var(--spacing-sm);
  font-size: var(--font-size-mini);
}

.message-body {
  background-color: transparent;
  border-radius: var(--border-radius-md);
  padding: var(--spacing-md);
  border: none;
  position: relative;
  overflow: hidden;
}

.user-message .message-body {
  background-color: rgba(135, 206, 235, 0.05);
  border-top-right-radius: 0;
}

.ai-message .message-body {
  background-color: rgba(79, 121, 66, 0.05);
  border-top-left-radius: 0;
}

.system-message .message-body {
  background-color: rgba(255, 165, 0, 0.05);
  border-radius: var(--border-radius-md);
}

.error-message .message-body {
  background-color: rgba(255, 69, 0, 0.05);
}

.message-text {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
}

.message-image-container {
  margin-top: var(--spacing-md);
  position: relative;
  display: inline-block;
  max-width: 100%;
  border-radius: var(--border-radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
  cursor: zoom-in;
}

.message-image-container:hover {
  transform: scale(1.02);
  box-shadow: var(--shadow-md);
}

.message-image {
  display: block;
  max-width: 100%;
  max-height: 300px;
  object-fit: contain;
}

/* 消息加载动画 */

@keyframes message-fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-actions {
  display: flex;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-md);
}

.action-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--color-border);
  background-color: transparent;
  color: var(--color-text);
  font-size: var(--font-size-small);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.action-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.action-button:active {
  transform: translateY(0);
}

.retry-button {
  color: var(--color-flame-red);
  border-color: var(--color-flame-red);
}

.retry-button:hover {
  background-color: rgba(255, 69, 0, 0.1);
}

.copy-button {
  color: var(--color-text-light);
}

.copy-button:hover {
  color: var(--color-text);
}
/* Markdown样式 */

/* 基础文本样式 */
.message-text {
  font-family: var(--font-family-sans);
  font-size: var(--font-size-body);
  line-height: 1.6;
  color: var(--color-text);
  word-break: break-word;
  overflow-wrap: break-word;
  white-space: pre-wrap;
}

/* 标题样式 */
.message-text h1, 
.message-text h2, 
.message-text h3, 
.message-text h4, 
.message-text h5, 
.message-text h6 {
  font-family: var(--font-family-serif);
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  font-weight: 600;
  line-height: 1.2;
  color: var(--color-primary);
}

.message-text h1 {
  font-size: 1.6em;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding-bottom: 0.2em;
}

.message-text h2 {
  font-size: 1.4em;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding-bottom: 0.2em;
}

.message-text h3 {
  font-size: 1.2em;
}

.message-text h4 {
  font-size: 1.1em;
}

.message-text h5, .message-text h6 {
  font-size: 1em;
}

/* 段落样式 */
.message-text p {
  margin: 0.8em 0;
}

/* 链接样式 */
.message-text a {
  color: var(--color-primary);
  text-decoration: none;
  position: relative;
  transition: color var(--transition-fast);
}

.message-text a:hover {
  color: var(--color-forest-green);
  text-decoration: underline;
}

/* 行内代码样式 */
.message-text .inline-code {
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
  padding: 0.2em 0.4em;
  font-family: var(--font-family-mono);
  font-size: 0.9em;
  color: #d73a49;
}

/* 代码块样式 */
.message-text .code-block {
  background-color: #f8f8f8;
  border-radius: var(--border-radius-sm);
  padding: 0.8em;
  margin: 1em 0;
  overflow: auto;
  border: 1px solid #e0e0e0;
  box-shadow: var(--shadow-sm);
}

.message-text .code-block code {
  font-family: var(--font-family-mono);
  font-size: 0.9em;
  line-height: 1.5;
  display: block;
  color: #333;
}

/* 引用块样式 */
.message-text blockquote {
  border-left: 3px solid var(--color-accent);
  margin: 1em 0;
  padding: 0.5em 1em;
  background-color: rgba(165, 214, 167, 0.1);
  color: var(--color-text-light);
  font-style: italic;
}

/* 列表样式 */
.message-text ul, 
.message-text ol {
  padding-left: 1.8em;
  margin: 0.8em 0;
}

.message-text li {
  margin: 0.3em 0;
}

.message-text li > ul, 
.message-text li > ol {
  margin: 0.2em 0;
}

/* 表格样式 */
.message-text .md-table {
  border-collapse: collapse;
  margin: 1em 0;
  width: 100%;
  overflow-x: auto;
  display: block;
}

.message-text .md-table th,
.message-text .md-table td {
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  text-align: left;
}

.message-text .md-table th {
  background-color: rgba(0, 0, 0, 0.05);
  font-weight: 600;
}

.message-text .md-table tr:nth-child(even) {
  background-color: rgba(0, 0, 0, 0.02);
}

/* 水平线样式 */
.message-text hr {
  border: 0;
  height: 1px;
  background-color: #e0e0e0;
  margin: 2em 0;
}

/* 图片样式 */
.message-text img {
  max-width: 100%;
  border-radius: var(--border-radius-sm);
  margin: 1em 0;
  box-shadow: var(--shadow-sm);
}

/* 复选框样式 */
.message-text input[type="checkbox"] {
  margin-right: 0.5em;
}

/* 突出显示的语法 */
.message-text mark {
  background-color: rgba(255, 255, 0, 0.3);
  padding: 0.1em 0.2em;
  border-radius: 3px;
}

/* 适配深色模式 */
@media (prefers-color-scheme: dark) {
  .message-text code {
    background-color: rgba(255, 255, 255, 0.1);
  }
  
  .message-text .code-block {
    background-color: #2d2d2d;
    border-color: #444;
  }
  
  .message-text .code-block code {
    color: #eee;
  }
  
  .message-text .md-table th,
  .message-text .md-table td {
    border-color: #444;
  }
  
  .message-text .md-table th {
    background-color: rgba(255, 255, 255, 0.1);
  }
  
  .message-text .md-table tr:nth-child(even) {
    background-color: rgba(255, 255, 255, 0.05);
  }
  
  .message-text blockquote {
    background-color: rgba(165, 214, 167, 0.05);
  }
}
:root {
  /* 吉卜力风格主色调 */
  --color-natural-green: #4F7942;
  --color-sky-blue: #87CEEB;
  --color-warm-orange: #FFA500;
  --color-wood-brown: #8B4513;
  --color-soft-beige: #F5F5DC;

  /* 辅助色 */
  --color-forest-green: #228B22;
  --color-sunset-purple: #DDA0DD;
  --color-moonlight-silver: #E6E6FA;
  --color-flame-red: #FF4500;

  /* 功能色 */
  --color-primary: var(--color-natural-green);
  --color-secondary: var(--color-sky-blue);
  --color-accent: var(--color-warm-orange);
  --color-background: var(--color-soft-beige);
  --color-text: #333333;
  --color-text-light: #666666;
  --color-border: rgba(139, 69, 19, 0.3);

  /* 字体 */
  --font-family-sans: 'Noto Sans CJK SC', 'Avenir', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-family-serif: 'Noto Serif CJK SC', 'Century Old Style', serif;

  /* 字体大小 */
  --font-size-large: 32px;
  --font-size-title: 24px;
  --font-size-subtitle: 20px;
  --font-size-body: 16px;
  --font-size-small: 14px;
  --font-size-mini: 12px;

  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;

  /* 圆角 */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 16px;

  /* 阴影 */
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.2);

  /* 动画 */
  --transition-fast: 150ms ease;
  --transition-normal: 300ms cubic-bezier(0.45, 0.05, 0.55, 0.95);
  --transition-slow: 450ms cubic-bezier(0.4, 0, 0.2, 1);
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-md);
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.spinner-dot {
  background-color: var(--color-primary);
  border-radius: 50%;
  display: inline-block;
  margin: 0 var(--spacing-xs);
  opacity: 0.6;
  animation: bounce 1.4s infinite ease-in-out both;
}

.spinner-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.spinner-dot:nth-child(2) {
  animation-delay: -0.16s;
}

/* 小尺寸 */

.loading-indicator-small .spinner-dot {
  width: 6px;
  height: 6px;
}

/* 中尺寸 */

.loading-indicator-medium .spinner-dot {
  width: 10px;
  height: 10px;
}

/* 大尺寸 */

.loading-indicator-large .spinner-dot {
  width: 14px;
  height: 14px;
}

.loading-text {
  margin-top: var(--spacing-md);
  font-size: var(--font-size-small);
  color: var(--color-text-light);
  font-family: var(--font-family-serif);
  letter-spacing: 1px;
}

/* 吉卜力风格的弹性动画 */

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0.6);
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}
:root {
  /* 吉卜力风格主色调 */
  --color-natural-green: #4F7942;
  --color-sky-blue: #87CEEB;
  --color-warm-orange: #FFA500;
  --color-wood-brown: #8B4513;
  --color-soft-beige: #F5F5DC;

  /* 辅助色 */
  --color-forest-green: #228B22;
  --color-sunset-purple: #DDA0DD;
  --color-moonlight-silver: #E6E6FA;
  --color-flame-red: #FF4500;

  /* 功能色 */
  --color-primary: var(--color-natural-green);
  --color-secondary: var(--color-sky-blue);
  --color-accent: var(--color-warm-orange);
  --color-background: var(--color-soft-beige);
  --color-text: #333333;
  --color-text-light: #666666;
  --color-border: rgba(139, 69, 19, 0.3);

  /* 字体 */
  --font-family-sans: 'Noto Sans CJK SC', 'Avenir', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-family-serif: 'Noto Serif CJK SC', 'Century Old Style', serif;

  /* 字体大小 */
  --font-size-large: 32px;
  --font-size-title: 24px;
  --font-size-subtitle: 20px;
  --font-size-body: 16px;
  --font-size-small: 14px;
  --font-size-mini: 12px;

  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;

  /* 圆角 */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 16px;

  /* 阴影 */
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.2);

  /* 动画 */
  --transition-fast: 150ms ease;
  --transition-normal: 300ms cubic-bezier(0.45, 0.05, 0.55, 0.95);
  --transition-slow: 450ms cubic-bezier(0.4, 0, 0.2, 1);
}

.message-list {
  flex: 1;
  padding: 0;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 200px;
  box-sizing: border-box;
}

.empty-message-list {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  padding: var(--spacing-xl);
  opacity: 0.9;
}

.welcome-message {
  max-width: 600px;
  width: 100%;
  background-color: transparent;
  border: none;
  animation: float 6s infinite ease-in-out;
}

.welcome-message h2 {
  color: var(--color-primary);
  text-align: center;
  margin-bottom: var(--spacing-md);
}

.welcome-message p {
  margin-bottom: var(--spacing-md);
  line-height: 1.6;
}

.welcome-message ul {
  list-style-type: none;
  padding-left: var(--spacing-md);
}

.welcome-message li {
  margin-bottom: var(--spacing-sm);
  position: relative;
  padding-left: var(--spacing-md);
}

.welcome-message li:before {
  content: '•';
  color: var(--color-accent);
  position: absolute;
  left: 0;
  font-size: 1.2em;
}

.loading-message {
  align-self: center;
  margin: var(--spacing-md) 0;
}

/* 吉卜力风格的轻微浮动动画 */

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* 确保在触摸设备上有良好的滚动体验 */

@media (pointer: coarse) {
  .message-list {
    -webkit-overflow-scrolling: touch;
  }
}
:root {
  /* 吉卜力风格主色调 */
  --color-natural-green: #4F7942;
  --color-sky-blue: #87CEEB;
  --color-warm-orange: #FFA500;
  --color-wood-brown: #8B4513;
  --color-soft-beige: #F5F5DC;

  /* 辅助色 */
  --color-forest-green: #228B22;
  --color-sunset-purple: #DDA0DD;
  --color-moonlight-silver: #E6E6FA;
  --color-flame-red: #FF4500;

  /* 功能色 */
  --color-primary: var(--color-natural-green);
  --color-secondary: var(--color-sky-blue);
  --color-accent: var(--color-warm-orange);
  --color-background: var(--color-soft-beige);
  --color-text: #333333;
  --color-text-light: #666666;
  --color-border: rgba(139, 69, 19, 0.3);

  /* 字体 */
  --font-family-sans: 'Noto Sans CJK SC', 'Avenir', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-family-serif: 'Noto Serif CJK SC', 'Century Old Style', serif;

  /* 字体大小 */
  --font-size-large: 32px;
  --font-size-title: 24px;
  --font-size-subtitle: 20px;
  --font-size-body: 16px;
  --font-size-small: 14px;
  --font-size-mini: 12px;

  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;

  /* 圆角 */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 16px;

  /* 阴影 */
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.2);

  /* 动画 */
  --transition-fast: 150ms ease;
  --transition-normal: 300ms cubic-bezier(0.45, 0.05, 0.55, 0.95);
  --transition-slow: 450ms cubic-bezier(0.4, 0, 0.2, 1);
}

.input-area {
  border-radius: var(--border-radius-lg);
  background-color: var(--color-background-light);
  border: 1px solid var(--color-border);
  transition: all var(--transition-normal);
  margin: var(--spacing-md) 0;
  overflow: hidden;
}

.input-area.focused {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(79, 121, 66, 0.1);
}

.input-container {
  display: flex;
  flex-direction: column;
  position: relative;
}

.input-main {
  display: flex;
  align-items: flex-end;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm);
  background: white;
}

textarea {
  flex: 1;
  font-family: var(--font-family-sans);
  font-size: var(--font-size-body);
  border: none;
  resize: none;
  background-color: transparent;
  padding: var(--spacing-sm);
  min-height: 42px;
  max-height: 150px;
  outline: none;
  line-height: 1.5;
}

.input-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.upload-button,
.send-button {
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  background: transparent;
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--border-radius-sm);
  transition: all var(--transition-fast);
  width: 36px;
  height: 36px;
}

.upload-button svg,
.send-button svg {
  width: 22px;
  height: 22px;
  transition: all var(--transition-fast);
}

.upload-button {
  color: var(--color-text);
}

.upload-button:hover {
  background-color: var(--color-background);
}

.send-button {
  color: var(--color-primary);
}

.send-button:hover {
  background-color: var(--color-primary);
  color: white;
}

.upload-button:active,
.send-button:active {
  transform: scale(0.95);
}

.upload-button[disabled],
.send-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.input-settings {
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--color-background);
  border-top: 1px solid var(--color-border);
}

.deepseek-toggle {
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
  padding: 4px 8px;
  border-radius: var(--border-radius-full);
  transition: all 0.2s ease;
  opacity: 0.85;
}

.deepseek-toggle:hover {
  opacity: 1;
}

.deepseek-toggle input[type="checkbox"] {
  position: relative;
  width: 36px;
  height: 20px;
  margin: 0 8px 0 0;
  appearance: none;
  background: var(--color-background-dark);
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.deepseek-toggle input[type="checkbox"]:checked {
  background: var(--color-primary);
}

.deepseek-toggle input[type="checkbox"]::before {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: white;
  top: 2px;
  left: 2px;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.deepseek-toggle input[type="checkbox"]:checked::before {
  left: 18px;
}

.toggle-label {
  font-size: 13px;
  color: var(--color-text);
  opacity: 0.8;
  font-weight: 500;
}

.selected-image-container {
  padding: var(--spacing-sm);
  background: white;
  border-bottom: 1px solid var(--color-border);
}

.selected-image {
  max-height: 120px;
  max-width: 240px;
  border-radius: var(--border-radius-sm);
  display: block;
}

.remove-image-btn {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  line-height: 1;
  padding: 0;
  cursor: pointer;
  border: none;
  transition: all var(--transition-fast);
}

.remove-image-btn:hover {
  background-color: rgba(0, 0, 0, 0.8);
}

@media (max-width: 768px) {
  .input-area {
    margin: var(--spacing-sm) 0;
  }

  .input-main {
    padding: var(--spacing-xs);
  }

  textarea {
    padding: var(--spacing-xs);
    min-height: 36px;
  }

  .upload-button,
  .send-button {
    width: 32px;
    height: 32px;
  }

  .upload-button svg,
  .send-button svg {
    width: 20px;
    height: 20px;
  }
}
:root {
  /* 吉卜力风格主色调 */
  --color-natural-green: #4F7942;
  --color-sky-blue: #87CEEB;
  --color-warm-orange: #FFA500;
  --color-wood-brown: #8B4513;
  --color-soft-beige: #F5F5DC;

  /* 辅助色 */
  --color-forest-green: #228B22;
  --color-sunset-purple: #DDA0DD;
  --color-moonlight-silver: #E6E6FA;
  --color-flame-red: #FF4500;

  /* 功能色 */
  --color-primary: var(--color-natural-green);
  --color-secondary: var(--color-sky-blue);
  --color-accent: var(--color-warm-orange);
  --color-background: var(--color-soft-beige);
  --color-text: #333333;
  --color-text-light: #666666;
  --color-border: rgba(139, 69, 19, 0.3);

  /* 字体 */
  --font-family-sans: 'Noto Sans CJK SC', 'Avenir', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-family-serif: 'Noto Serif CJK SC', 'Century Old Style', serif;

  /* 字体大小 */
  --font-size-large: 32px;
  --font-size-title: 24px;
  --font-size-subtitle: 20px;
  --font-size-body: 16px;
  --font-size-small: 14px;
  --font-size-mini: 12px;

  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;

  /* 圆角 */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 16px;

  /* 阴影 */
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.2);

  /* 动画 */
  --transition-fast: 150ms ease;
  --transition-normal: 300ms cubic-bezier(0.45, 0.05, 0.55, 0.95);
  --transition-slow: 450ms cubic-bezier(0.4, 0, 0.2, 1);
}

.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  margin: 0;
  padding: var(--spacing-md);
  box-sizing: border-box;
}

.chat-header {
  text-align: center;
  margin-bottom: var(--spacing-lg);
  animation: fade-in 1s ease-out;
}

.chat-title {
  font-family: var(--font-family-serif);
  color: var(--color-primary);
  font-size: var(--font-size-large);
  margin-bottom: var(--spacing-xs);
  position: relative;
  display: inline-block;
}

.chat-title:after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 60%;
  height: 2px;
  background-color: var(--color-accent);
  border-radius: 2px;
}

.chat-subtitle {
  color: var(--color-text-light);
  font-size: var(--font-size-subtitle);
  font-weight: normal;
  margin-top: var(--spacing-xs);
}

.chat-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
  width: 100%;
  border: none;
  padding: var(--spacing-md);
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 768px) {
  .chat-container {
    padding: var(--spacing-sm);
    height: 100%;
  }
  
  .chat-title {
    font-size: var(--font-size-title);
  }
  
  .chat-subtitle {
    font-size: var(--font-size-body);
  }
  
  .chat-content {
    padding: var(--spacing-md);
  }
}
/* 吉卜力风格装饰元素样式 */

/* 基础样式 */
.ghibli-decorations {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
  overflow: hidden;
}

.decoration-svg {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 云朵样式 */
.cloud {
  position: absolute;
  opacity: 0.2;
}

.cloud-1 {
  top: 15%;
  left: -5%;
  width: 150px;
  height: 75px;
  animation: cloud-drift 120s linear infinite;
}

.cloud-2 {
  top: 45%;
  left: -8%;
  width: 200px;
  height: 100px;
  animation: cloud-drift 150s linear infinite 30s;
}

.cloud-3 {
  top: 75%;
  left: -7%;
  width: 120px;
  height: 60px;
  animation: cloud-drift 180s linear infinite 60s;
}

.cloud-4 {
  top: 25%;
  left: -10%;
  width: 100px;
  height: 50px;
  animation: cloud-drift 140s linear infinite 15s;
}

.cloud-5 {
  top: 60%;
  left: -12%;
  width: 160px;
  height: 80px;
  animation: cloud-drift 160s linear infinite 45s;
}



/* 动画效果 */
@keyframes cloud-drift {
  from {
    transform: translateX(0);
    opacity: 0;
  }
  10% {
    opacity: 0.2;
  }
  90% {
    opacity: 0.2;
  }
  to {
    transform: translateX(calc(100vw + 200px));
    opacity: 0;
  }
}



/* 媒体查询 - 在小屏幕上减少装饰元素 */
@media (max-width: 768px) {
  .cloud-4, .cloud-5 {
    display: none;
  }
}

/* 减少动画 - 对动画敏感的用户 */
@media (prefers-reduced-motion) {
  .cloud {
    animation: none !important;
    opacity: 0.2 !important;
  }
}
:root {
  /* 吉卜力风格主色调 */
  --color-natural-green: #4F7942;
  --color-sky-blue: #87CEEB;
  --color-warm-orange: #FFA500;
  --color-wood-brown: #8B4513;
  --color-soft-beige: #F5F5DC;

  /* 辅助色 */
  --color-forest-green: #228B22;
  --color-sunset-purple: #DDA0DD;
  --color-moonlight-silver: #E6E6FA;
  --color-flame-red: #FF4500;

  /* 功能色 */
  --color-primary: var(--color-natural-green);
  --color-secondary: var(--color-sky-blue);
  --color-accent: var(--color-warm-orange);
  --color-background: var(--color-soft-beige);
  --color-text: #333333;
  --color-text-light: #666666;
  --color-border: rgba(139, 69, 19, 0.3);

  /* 字体 */
  --font-family-sans: 'Noto Sans CJK SC', 'Avenir', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-family-serif: 'Noto Serif CJK SC', 'Century Old Style', serif;

  /* 字体大小 */
  --font-size-large: 32px;
  --font-size-title: 24px;
  --font-size-subtitle: 20px;
  --font-size-body: 16px;
  --font-size-small: 14px;
  --font-size-mini: 12px;

  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;

  /* 圆角 */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 16px;

  /* 阴影 */
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.2);

  /* 动画 */
  --transition-fast: 150ms ease;
  --transition-normal: 300ms cubic-bezier(0.45, 0.05, 0.55, 0.95);
  --transition-slow: 450ms cubic-bezier(0.4, 0, 0.2, 1);
}
/* 定义字体 */
@font-face {
  font-family: 'Alibaba PuHuiTi';
  font-style: normal;
  font-weight: 400;
  src: local('Alibaba PuHuiTi Regular'),
       local('AlibabaPuHuiTi-Regular'),
       url('data:font/woff2;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPEVycm9yPgogIDxDb2RlPkFjY2Vzc0RlbmllZDwvQ29kZT4KICA8TWVzc2FnZT5Zb3UgYXJlIGRlbmllZCBieSBidWNrZXQgcmVmZXJlciBwb2xpY3kuPC9NZXNzYWdlPgogIDxSZXF1ZXN0SWQ+NjdFRkQzODhBNzQ1M0YzNTM4NDcxRDdBPC9SZXF1ZXN0SWQ+CiAgPEhvc3RJZD5wdWh1aXRpLm9zcy1jbi1oYW5nemhvdS5hbGl5dW5jcy5jb208L0hvc3RJZD4KICA8QnVja2V0TmFtZT5wdWh1aXRpPC9CdWNrZXROYW1lPgogIDxFQz4wMDAzLTAwMDAwNTAxPC9FQz4KICA8UmVjb21tZW5kRG9jPmh0dHBzOi8vYXBpLmFsaXl1bi5jb20vdHJvdWJsZXNob290P3E9MDAwMy0wMDAwMDUwMTwvUmVjb21tZW5kRG9jPgo8L0Vycm9yPgo=') format('woff2');
}
@font-face {
  font-family: 'Alibaba PuHuiTi';
  font-style: normal;
  font-weight: 500;
  src: local('Alibaba PuHuiTi Medium'),
       local('AlibabaPuHuiTi-Medium'),
       url('data:font/woff2;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPEVycm9yPgogIDxDb2RlPkFjY2Vzc0RlbmllZDwvQ29kZT4KICA8TWVzc2FnZT5Zb3UgYXJlIGRlbmllZCBieSBidWNrZXQgcmVmZXJlciBwb2xpY3kuPC9NZXNzYWdlPgogIDxSZXF1ZXN0SWQ+NjdFRkQzODlDMEU1RjgzMDMyN0M5MDA0PC9SZXF1ZXN0SWQ+CiAgPEhvc3RJZD5wdWh1aXRpLm9zcy1jbi1oYW5nemhvdS5hbGl5dW5jcy5jb208L0hvc3RJZD4KICA8QnVja2V0TmFtZT5wdWh1aXRpPC9CdWNrZXROYW1lPgogIDxFQz4wMDAzLTAwMDAwNTAxPC9FQz4KICA8UmVjb21tZW5kRG9jPmh0dHBzOi8vYXBpLmFsaXl1bi5jb20vdHJvdWJsZXNob290P3E9MDAwMy0wMDAwMDUwMTwvUmVjb21tZW5kRG9jPgo8L0Vycm9yPgo=') format('woff2');
}
@font-face {
  font-family: 'Alibaba PuHuiTi';
  font-style: normal;
  font-weight: 700;
  src: local('Alibaba PuHuiTi SemiBold'),
       local('AlibabaPuHuiTi-SemiBold'),
       url('data:font/woff2;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPEVycm9yPgogIDxDb2RlPkFjY2Vzc0RlbmllZDwvQ29kZT4KICA8TWVzc2FnZT5Zb3UgYXJlIGRlbmllZCBieSBidWNrZXQgcmVmZXJlciBwb2xpY3kuPC9NZXNzYWdlPgogIDxSZXF1ZXN0SWQ+NjdFRkQzODlCQTgyQUQzNjM5QTM4Nzc0PC9SZXF1ZXN0SWQ+CiAgPEhvc3RJZD5wdWh1aXRpLm9zcy1jbi1oYW5nemhvdS5hbGl5dW5jcy5jb208L0hvc3RJZD4KICA8QnVja2V0TmFtZT5wdWh1aXRpPC9CdWNrZXROYW1lPgogIDxFQz4wMDAzLTAwMDAwNTAxPC9FQz4KICA8UmVjb21tZW5kRG9jPmh0dHBzOi8vYXBpLmFsaXl1bi5jb20vdHJvdWJsZXNob290P3E9MDAwMy0wMDAwMDUwMTwvUmVjb21tZW5kRG9jPgo8L0Vycm9yPgo=') format('woff2');
}
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
html, body, #root {
  height: 100%;
  width: 100%;
}
body {
  font-family: 'Alibaba PuHuiTi', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  font-size: var(--font-size-body);
  line-height: 1.5;
  color: var(--color-text);
  background-color: var(--color-background);
  background-image: url("data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20viewBox='0%200%201422%20800'%20opacity='0.3'%3e%3cdefs%3e%3clinearGradient%20x1='50%25'%20y1='0%25'%20x2='50%25'%20y2='100%25'%20id='oooscillate-grad'%3e%3cstop%20stop-color='hsl(206,%2075%25,%2049%25)'%20stop-opacity='1'%20offset='0%25'%3e%3c/stop%3e%3cstop%20stop-color='hsl(331,%2090%25,%2056%25)'%20stop-opacity='1'%20offset='100%25'%3e%3c/stop%3e%3c/linearGradient%3e%3c/defs%3e%3cg%20stroke-width='1'%20stroke='url(%23oooscillate-grad)'%20fill='none'%20stroke-linecap='round'%3e%3cpath%20d='M%200%20448%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20448'%20opacity='0.05'%3e%3c/path%3e%3cpath%20d='M%200%20420%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20420'%20opacity='0.11'%3e%3c/path%3e%3cpath%20d='M%200%20392%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20392'%20opacity='0.18'%3e%3c/path%3e%3cpath%20d='M%200%20364%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20364'%20opacity='0.24'%3e%3c/path%3e%3cpath%20d='M%200%20336%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20336'%20opacity='0.30'%3e%3c/path%3e%3cpath%20d='M%200%20308%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20308'%20opacity='0.37'%3e%3c/path%3e%3cpath%20d='M%200%20280%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20280'%20opacity='0.43'%3e%3c/path%3e%3cpath%20d='M%200%20252%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20252'%20opacity='0.49'%3e%3c/path%3e%3cpath%20d='M%200%20224%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20224'%20opacity='0.56'%3e%3c/path%3e%3cpath%20d='M%200%20196%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20196'%20opacity='0.62'%3e%3c/path%3e%3cpath%20d='M%200%20168%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20168'%20opacity='0.68'%3e%3c/path%3e%3cpath%20d='M%200%20140%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20140'%20opacity='0.75'%3e%3c/path%3e%3cpath%20d='M%200%20112%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20112'%20opacity='0.81'%3e%3c/path%3e%3cpath%20d='M%200%2084%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%2084'%20opacity='0.87'%3e%3c/path%3e%3cpath%20d='M%200%2056%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%2056'%20opacity='0.94'%3e%3c/path%3e%3c/g%3e%3c/svg%3e");
  background-repeat: repeat;
  background-size: 300px;
  background-blend-mode: soft-light;
  background-attachment: fixed;
  overflow-x: hidden;
}
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-serif);
  margin-bottom: var(--spacing-md);
}
h1 {
  font-size: var(--font-size-large);
}
h2 {
  font-size: var(--font-size-title);
}
h3 {
  font-size: var(--font-size-subtitle);
}
p {
  margin-bottom: var(--spacing-md);
}
a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}
a:hover {
  color: var(--color-forest-green);
}
button {
  cursor: pointer;
  font-family: var(--font-family-sans);
  border: 2px solid var(--color-border);
  background-color: var(--color-soft-beige);
  color: var(--color-text);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-sm);
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
}
button:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20viewBox='0%200%201422%20800'%20opacity='0.3'%3e%3cdefs%3e%3clinearGradient%20x1='50%25'%20y1='0%25'%20x2='50%25'%20y2='100%25'%20id='oooscillate-grad'%3e%3cstop%20stop-color='hsl(206,%2075%25,%2049%25)'%20stop-opacity='1'%20offset='0%25'%3e%3c/stop%3e%3cstop%20stop-color='hsl(331,%2090%25,%2056%25)'%20stop-opacity='1'%20offset='100%25'%3e%3c/stop%3e%3c/linearGradient%3e%3c/defs%3e%3cg%20stroke-width='1'%20stroke='url(%23oooscillate-grad)'%20fill='none'%20stroke-linecap='round'%3e%3cpath%20d='M%200%20448%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20448'%20opacity='0.05'%3e%3c/path%3e%3cpath%20d='M%200%20420%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20420'%20opacity='0.11'%3e%3c/path%3e%3cpath%20d='M%200%20392%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20392'%20opacity='0.18'%3e%3c/path%3e%3cpath%20d='M%200%20364%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20364'%20opacity='0.24'%3e%3c/path%3e%3cpath%20d='M%200%20336%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20336'%20opacity='0.30'%3e%3c/path%3e%3cpath%20d='M%200%20308%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20308'%20opacity='0.37'%3e%3c/path%3e%3cpath%20d='M%200%20280%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20280'%20opacity='0.43'%3e%3c/path%3e%3cpath%20d='M%200%20252%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20252'%20opacity='0.49'%3e%3c/path%3e%3cpath%20d='M%200%20224%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20224'%20opacity='0.56'%3e%3c/path%3e%3cpath%20d='M%200%20196%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20196'%20opacity='0.62'%3e%3c/path%3e%3cpath%20d='M%200%20168%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20168'%20opacity='0.68'%3e%3c/path%3e%3cpath%20d='M%200%20140%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20140'%20opacity='0.75'%3e%3c/path%3e%3cpath%20d='M%200%20112%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20112'%20opacity='0.81'%3e%3c/path%3e%3cpath%20d='M%200%2084%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%2084'%20opacity='0.87'%3e%3c/path%3e%3cpath%20d='M%200%2056%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%2056'%20opacity='0.94'%3e%3c/path%3e%3c/g%3e%3c/svg%3e");
  background-size: 150px;
  opacity: 0.1;
  z-index: -1;
}
button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}
button:active {
  transform: translateY(1px);
  box-shadow: var(--shadow-sm);
}
button.primary {
  background-color: var(--color-primary);
  color: white;
}
button.secondary {
  background-color: var(--color-secondary);
  color: var(--color-text);
}
button.accent {
  background-color: var(--color-accent);
  color: white;
}
input, textarea {
  font-family: var(--font-family-sans);
  padding: var(--spacing-sm) var(--spacing-md);
  border: 2px solid var(--color-border);
  border-radius: var(--border-radius-sm);
  background-color: rgba(255, 255, 255, 0.8);
  transition: all var(--transition-fast);
}
input:focus, textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(79, 121, 66, 0.2);
}
.paper-card {
  background-color: transparent;
  border-radius: var(--border-radius-md);
  border: 1px solid rgba(139, 69, 19, 0.2);
  padding: var(--spacing-lg);
  position: relative;
  overflow: hidden;
}
/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}
::-webkit-scrollbar-thumb {
  background: var(--color-primary);
  opacity: 0.5;
  border-radius: 4px;
}
::-webkit-scrollbar-thumb:hover {
  background: var(--color-forest-green);
}
/* 页面转场动画 */
.fade-enter {
  opacity: 0;
}
.fade-enter-active {
  opacity: 1;
  transition: opacity var(--transition-normal);
}
.fade-exit {
  opacity: 1;
}
.fade-exit-active {
  opacity: 0;
  transition: opacity var(--transition-normal);
}
/* 响应式布局辅助样式 */

/* 大屏幕设备 (桌面, 1200px 及以上) */
@media (min-width: 1200px) {
  .chat-container {
    width: 100%; /* 移除最大宽度限制，占满屏幕 */
    max-width: 100%;
  }
}

/* 中等屏幕设备 (平板电脑, 768px 到 1199px) */
@media (min-width: 768px) and (max-width: 1199px) {
  .chat-container {
    max-width: 100%;
    padding: var(--spacing-md);
  }
}

/* 小屏幕设备 (手机, 767px 及以下) */
@media (max-width: 767px) {
  .chat-container {
    padding: var(--spacing-sm);
  }
  
  .chat-content {
    padding: var(--spacing-sm);
  }
  
  .chat-header {
    margin-bottom: var(--spacing-md);
  }
  
  .chat-title {
    font-size: var(--font-size-subtitle);
  }
  
  .chat-subtitle {
    font-size: var(--font-size-small);
  }
  
  .message-item {
    margin-bottom: var(--spacing-md);
  }
  
  .message-content {
    max-width: 85%;
  }
  
  .avatar {
    width: 32px;
    height: 32px;
    font-size: 10px;
  }
  
  .input-area {
    margin-top: var(--spacing-sm);
  }
}

/* 确保内容不会被键盘挤压在移动设备上 */
@media (max-height: 500px) {
  .chat-header {
    display: none;
  }
  
  .chat-container {
    padding-top: var(--spacing-xs);
    padding-bottom: var(--spacing-xs);
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  :root {
    --color-text: #000000;
    --color-background: #ffffff;
    --color-primary: #006400;
    --color-border: #000000;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.4);
  }
  
  .message-body {
    border: 2px solid var(--color-border);
  }
}

/* 减少动画 - 对动画敏感的用户 */
@media (prefers-reduced-motion) {
  *, *::before, *::after {
    animation-duration: 0.001ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.001ms !important;
  }
  
  .welcome-message {
    animation: none !important;
  }
}
