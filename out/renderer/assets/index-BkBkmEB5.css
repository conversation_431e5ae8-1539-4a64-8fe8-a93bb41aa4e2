:root {
  /* 吉卜力风格主色调 */
  --color-natural-green: #4F7942;
  --color-sky-blue: #87CEEB;
  --color-warm-orange: #FFA500;
  --color-wood-brown: #8B4513;
  --color-soft-beige: #F5F5DC;

  /* 辅助色 */
  --color-forest-green: #228B22;
  --color-sunset-purple: #DDA0DD;
  --color-moonlight-silver: #E6E6FA;
  --color-flame-red: #FF4500;

  /* 功能色 */
  --color-primary: var(--color-natural-green);
  --color-secondary: var(--color-sky-blue);
  --color-accent: var(--color-warm-orange);
  --color-background: var(--color-soft-beige);
  --color-text: #333333;
  --color-text-light: #666666;
  --color-border: rgba(139, 69, 19, 0.3);

  /* 字体 */
  --font-family-sans: 'Noto Sans CJK SC', 'Avenir', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-family-serif: 'Noto Serif CJK SC', 'Century Old Style', serif;

  /* 字体大小 */
  --font-size-large: 32px;
  --font-size-title: 24px;
  --font-size-subtitle: 20px;
  --font-size-body: 16px;
  --font-size-small: 14px;
  --font-size-mini: 12px;

  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;

  /* 圆角 */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 16px;

  /* 阴影 */
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.2);

  /* 动画 */
  --transition-fast: 150ms ease;
  --transition-normal: 300ms cubic-bezier(0.45, 0.05, 0.55, 0.95);
  --transition-slow: 450ms cubic-bezier(0.4, 0, 0.2, 1);
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-md);
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.spinner-dot {
  background-color: var(--color-primary);
  border-radius: 50%;
  display: inline-block;
  margin: 0 var(--spacing-xs);
  opacity: 0.6;
  animation: bounce 1.4s infinite ease-in-out both;
}

.spinner-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.spinner-dot:nth-child(2) {
  animation-delay: -0.16s;
}

/* 小尺寸 */

.loading-indicator-small .spinner-dot {
  width: 6px;
  height: 6px;
}

/* 中尺寸 */

.loading-indicator-medium .spinner-dot {
  width: 10px;
  height: 10px;
}

/* 大尺寸 */

.loading-indicator-large .spinner-dot {
  width: 14px;
  height: 14px;
}

.loading-text {
  margin-top: var(--spacing-md);
  font-size: var(--font-size-small);
  color: var(--color-text-light);
  font-family: var(--font-family-serif);
  letter-spacing: 1px;
}

/* 吉卜力风格的弹性动画 */

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0.6);
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}
:root {
  /* 吉卜力风格主色调 */
  --color-natural-green: #4F7942;
  --color-sky-blue: #87CEEB;
  --color-warm-orange: #FFA500;
  --color-wood-brown: #8B4513;
  --color-soft-beige: #F5F5DC;

  /* 辅助色 */
  --color-forest-green: #228B22;
  --color-sunset-purple: #DDA0DD;
  --color-moonlight-silver: #E6E6FA;
  --color-flame-red: #FF4500;

  /* 功能色 */
  --color-primary: var(--color-natural-green);
  --color-secondary: var(--color-sky-blue);
  --color-accent: var(--color-warm-orange);
  --color-background: var(--color-soft-beige);
  --color-text: #333333;
  --color-text-light: #666666;
  --color-border: rgba(139, 69, 19, 0.3);

  /* 字体 */
  --font-family-sans: 'Noto Sans CJK SC', 'Avenir', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-family-serif: 'Noto Serif CJK SC', 'Century Old Style', serif;

  /* 字体大小 */
  --font-size-large: 32px;
  --font-size-title: 24px;
  --font-size-subtitle: 20px;
  --font-size-body: 16px;
  --font-size-small: 14px;
  --font-size-mini: 12px;

  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;

  /* 圆角 */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 16px;

  /* 阴影 */
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.2);

  /* 动画 */
  --transition-fast: 150ms ease;
  --transition-normal: 300ms cubic-bezier(0.45, 0.05, 0.55, 0.95);
  --transition-slow: 450ms cubic-bezier(0.4, 0, 0.2, 1);
}

.image-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.85);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
  animation: modal-fade-in 0.2s ease-out;
}

.image-modal-content {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

.image-modal-image {
  display: block;
  max-width: 100%;
  max-height: 90vh;
  object-fit: contain;
}

.image-modal-close {
  position: absolute;
  top: 15px;
  right: 15px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.6);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  font-size: 28px;
  font-weight: bold;
  line-height: 1;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
  z-index: 1001;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.image-modal-close:hover {
  background-color: rgba(255, 0, 0, 0.8);
  border-color: rgba(255, 255, 255, 0.8);
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

.image-modal-close:active {
  transform: scale(0.95);
}

.image-modal-hint {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  opacity: 0.8;
  pointer-events: none;
  animation: hint-fade-in 0.5s ease-out 0.5s both;
}

@keyframes modal-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes hint-fade-in {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(10px);
  }
  to {
    opacity: 0.8;
    transform: translateX(-50%) translateY(0);
  }
}/* 历史记录查看器样式 */
.history-viewer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.history-viewer {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 1200px;
  height: 80%;
  max-height: 800px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
  border-radius: 12px 12px 0 0;
}

.history-header h2 {
  margin: 0;
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6b7280;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s;
}

.close-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #6b7280;
  font-size: 1.1rem;
}

.empty-history {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #6b7280;
  font-size: 1.1rem;
}

.history-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.history-list {
  flex: 1;
  overflow-y: auto;
  padding: 20px 24px;
}

.history-item {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  transition: all 0.2s;
}

.history-item:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
}

.history-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.history-meta {
  display: flex;
  gap: 16px;
  align-items: center;
}

.history-date {
  color: #374151;
  font-weight: 500;
}

.history-stats {
  background: #dbeafe;
  color: #1e40af;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
}

.history-actions {
  display: flex;
  gap: 8px;
}

.view-btn, .use-btn, .delete-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.view-btn {
  background: #f3f4f6;
  color: #374151;
}

.view-btn:hover {
  background: #e5e7eb;
}

.use-btn {
  background: #3b82f6;
  color: white;
}

.use-btn:hover {
  background: #2563eb;
}

.delete-btn {
  background: #fef2f2;
  color: #dc2626;
}

.delete-btn:hover {
  background: #fee2e2;
}

.history-prompt {
  color: #4b5563;
  margin-bottom: 12px;
  line-height: 1.5;
}

.history-images-preview {
  display: flex;
  gap: 8px;
  align-items: center;
}

.preview-image {
  width: 80px;
  height: 60px;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
  background: #f9fafb;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-image img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.preview-image img:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.more-images {
  width: 80px;
  height: 60px;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f9fafb;
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
}

/* 批次详情模态框 */
.batch-detail-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1100;
}

.batch-detail {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  max-height: 80%;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.batch-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
  border-radius: 8px 8px 0 0;
}

.batch-detail-header h3 {
  margin: 0;
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
}

.batch-detail-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.batch-info {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 6px;
}

.batch-info p {
  margin: 8px 0;
  line-height: 1.5;
}

.batch-images {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 16px;
}

.batch-image-item {
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  overflow: hidden;
  background: white;
  display: flex;
  flex-direction: column;
}

.batch-image-item .image-container {
  height: 160px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f9fafb;
  padding: 8px;
}

.batch-image-item img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.batch-image-item img:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.image-info {
  padding: 12px;
}

.image-name {
  font-weight: 500;
  color: #374151;
  margin-bottom: 4px;
  font-size: 0.875rem;
}

.image-seed {
  color: #6b7280;
  font-size: 0.75rem;
  margin-bottom: 8px;
}

.image-url, .local-path {
  margin-bottom: 8px;
  font-size: 0.75rem;
}

.image-url strong, .local-path strong {
  color: #374151;
  display: block;
  margin-bottom: 2px;
}

.url-text, .path-text {
  color: #6b7280;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: #f3f4f6;
  padding: 4px 6px;
  border-radius: 3px;
  word-break: break-all;
  font-size: 0.7rem;
  line-height: 1.3;
  cursor: pointer;
}

.url-text:hover, .path-text:hover {
  background: #e5e7eb;
}

.local-status {
  color: #059669;
  font-size: 0.75rem;
  font-weight: 500;
  margin-top: 4px;
}

.local-status-missing {
  color: #dc2626;
  font-size: 0.75rem;
  font-weight: 500;
  margin-top: 4px;
}

/* 删除确认对话框 */
.delete-confirm-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1200;
}

.delete-confirm {
  background: white;
  border-radius: 8px;
  padding: 24px;
  width: 90%;
  max-width: 400px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.delete-confirm h3 {
  margin: 0 0 16px 0;
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
}

.delete-confirm p {
  margin: 8px 0;
  color: #4b5563;
  line-height: 1.5;
}

.warning {
  color: #dc2626 !important;
  font-size: 0.875rem;
}

.confirm-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 20px;
}

.cancel-btn, .confirm-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.cancel-btn {
  background: #f3f4f6;
  color: #374151;
}

.cancel-btn:hover {
  background: #e5e7eb;
}

.confirm-btn {
  background: #dc2626;
  color: white;
}

.confirm-btn:hover {
  background: #b91c1c;
}
/* 吉卜力风格装饰元素样式 */

/* 基础样式 */
.ghibli-decorations {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
  overflow: hidden;
}

.decoration-svg {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 云朵样式 */
.cloud {
  position: absolute;
  opacity: 0.2;
}

.cloud-1 {
  top: 15%;
  left: -5%;
  width: 150px;
  height: 75px;
  animation: cloud-drift 120s linear infinite;
}

.cloud-2 {
  top: 45%;
  left: -8%;
  width: 200px;
  height: 100px;
  animation: cloud-drift 150s linear infinite 30s;
}

.cloud-3 {
  top: 75%;
  left: -7%;
  width: 120px;
  height: 60px;
  animation: cloud-drift 180s linear infinite 60s;
}

.cloud-4 {
  top: 25%;
  left: -10%;
  width: 100px;
  height: 50px;
  animation: cloud-drift 140s linear infinite 15s;
}

.cloud-5 {
  top: 60%;
  left: -12%;
  width: 160px;
  height: 80px;
  animation: cloud-drift 160s linear infinite 45s;
}



/* 动画效果 */
@keyframes cloud-drift {
  from {
    transform: translateX(0);
    opacity: 0;
  }
  10% {
    opacity: 0.2;
  }
  90% {
    opacity: 0.2;
  }
  to {
    transform: translateX(calc(100vw + 200px));
    opacity: 0;
  }
}



/* 媒体查询 - 在小屏幕上减少装饰元素 */
@media (max-width: 768px) {
  .cloud-4, .cloud-5 {
    display: none;
  }
}

/* 减少动画 - 对动画敏感的用户 */
@media (prefers-reduced-motion) {
  .cloud {
    animation: none !important;
    opacity: 0.2 !important;
  }
}
:root {
  /* 吉卜力风格主色调 */
  --color-natural-green: #4F7942;
  --color-sky-blue: #87CEEB;
  --color-warm-orange: #FFA500;
  --color-wood-brown: #8B4513;
  --color-soft-beige: #F5F5DC;

  /* 辅助色 */
  --color-forest-green: #228B22;
  --color-sunset-purple: #DDA0DD;
  --color-moonlight-silver: #E6E6FA;
  --color-flame-red: #FF4500;

  /* 功能色 */
  --color-primary: var(--color-natural-green);
  --color-secondary: var(--color-sky-blue);
  --color-accent: var(--color-warm-orange);
  --color-background: var(--color-soft-beige);
  --color-text: #333333;
  --color-text-light: #666666;
  --color-border: rgba(139, 69, 19, 0.3);

  /* 字体 */
  --font-family-sans: 'Noto Sans CJK SC', 'Avenir', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-family-serif: 'Noto Serif CJK SC', 'Century Old Style', serif;

  /* 字体大小 */
  --font-size-large: 32px;
  --font-size-title: 24px;
  --font-size-subtitle: 20px;
  --font-size-body: 16px;
  --font-size-small: 14px;
  --font-size-mini: 12px;

  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;

  /* 圆角 */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 16px;

  /* 阴影 */
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.2);

  /* 动画 */
  --transition-fast: 150ms ease;
  --transition-normal: 300ms cubic-bezier(0.45, 0.05, 0.55, 0.95);
  --transition-slow: 450ms cubic-bezier(0.4, 0, 0.2, 1);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body, #root {
  height: 100%;
  width: 100%;
}

body {
  font-family: 'Alibaba PuHuiTi', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  font-size: var(--font-size-body);
  line-height: 1.5;
  color: var(--color-text);
  background-color: var(--color-background);
  background-image: url("data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20viewBox='0%200%201422%20800'%20opacity='0.3'%3e%3cdefs%3e%3clinearGradient%20x1='50%25'%20y1='0%25'%20x2='50%25'%20y2='100%25'%20id='oooscillate-grad'%3e%3cstop%20stop-color='hsl(206,%2075%25,%2049%25)'%20stop-opacity='1'%20offset='0%25'%3e%3c/stop%3e%3cstop%20stop-color='hsl(331,%2090%25,%2056%25)'%20stop-opacity='1'%20offset='100%25'%3e%3c/stop%3e%3c/linearGradient%3e%3c/defs%3e%3cg%20stroke-width='1'%20stroke='url(%23oooscillate-grad)'%20fill='none'%20stroke-linecap='round'%3e%3cpath%20d='M%200%20448%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20448'%20opacity='0.05'%3e%3c/path%3e%3cpath%20d='M%200%20420%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20420'%20opacity='0.11'%3e%3c/path%3e%3cpath%20d='M%200%20392%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20392'%20opacity='0.18'%3e%3c/path%3e%3cpath%20d='M%200%20364%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20364'%20opacity='0.24'%3e%3c/path%3e%3cpath%20d='M%200%20336%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20336'%20opacity='0.30'%3e%3c/path%3e%3cpath%20d='M%200%20308%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20308'%20opacity='0.37'%3e%3c/path%3e%3cpath%20d='M%200%20280%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20280'%20opacity='0.43'%3e%3c/path%3e%3cpath%20d='M%200%20252%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20252'%20opacity='0.49'%3e%3c/path%3e%3cpath%20d='M%200%20224%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20224'%20opacity='0.56'%3e%3c/path%3e%3cpath%20d='M%200%20196%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20196'%20opacity='0.62'%3e%3c/path%3e%3cpath%20d='M%200%20168%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20168'%20opacity='0.68'%3e%3c/path%3e%3cpath%20d='M%200%20140%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20140'%20opacity='0.75'%3e%3c/path%3e%3cpath%20d='M%200%20112%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20112'%20opacity='0.81'%3e%3c/path%3e%3cpath%20d='M%200%2084%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%2084'%20opacity='0.87'%3e%3c/path%3e%3cpath%20d='M%200%2056%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%2056'%20opacity='0.94'%3e%3c/path%3e%3c/g%3e%3c/svg%3e");
  background-repeat: repeat;
  background-size: 300px;
  background-blend-mode: soft-light;
  background-attachment: fixed;
  overflow-x: hidden;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-serif);
  margin-bottom: var(--spacing-md);
}

h1 {
  font-size: var(--font-size-large);
}

h2 {
  font-size: var(--font-size-title);
}

h3 {
  font-size: var(--font-size-subtitle);
}

p {
  margin-bottom: var(--spacing-md);
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--color-forest-green);
}

button {
  cursor: pointer;
  font-family: var(--font-family-sans);
  border: 2px solid var(--color-border);
  background-color: var(--color-soft-beige);
  color: var(--color-text);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-sm);
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
}

button:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20viewBox='0%200%201422%20800'%20opacity='0.3'%3e%3cdefs%3e%3clinearGradient%20x1='50%25'%20y1='0%25'%20x2='50%25'%20y2='100%25'%20id='oooscillate-grad'%3e%3cstop%20stop-color='hsl(206,%2075%25,%2049%25)'%20stop-opacity='1'%20offset='0%25'%3e%3c/stop%3e%3cstop%20stop-color='hsl(331,%2090%25,%2056%25)'%20stop-opacity='1'%20offset='100%25'%3e%3c/stop%3e%3c/linearGradient%3e%3c/defs%3e%3cg%20stroke-width='1'%20stroke='url(%23oooscillate-grad)'%20fill='none'%20stroke-linecap='round'%3e%3cpath%20d='M%200%20448%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20448'%20opacity='0.05'%3e%3c/path%3e%3cpath%20d='M%200%20420%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20420'%20opacity='0.11'%3e%3c/path%3e%3cpath%20d='M%200%20392%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20392'%20opacity='0.18'%3e%3c/path%3e%3cpath%20d='M%200%20364%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20364'%20opacity='0.24'%3e%3c/path%3e%3cpath%20d='M%200%20336%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20336'%20opacity='0.30'%3e%3c/path%3e%3cpath%20d='M%200%20308%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20308'%20opacity='0.37'%3e%3c/path%3e%3cpath%20d='M%200%20280%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20280'%20opacity='0.43'%3e%3c/path%3e%3cpath%20d='M%200%20252%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20252'%20opacity='0.49'%3e%3c/path%3e%3cpath%20d='M%200%20224%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20224'%20opacity='0.56'%3e%3c/path%3e%3cpath%20d='M%200%20196%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20196'%20opacity='0.62'%3e%3c/path%3e%3cpath%20d='M%200%20168%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20168'%20opacity='0.68'%3e%3c/path%3e%3cpath%20d='M%200%20140%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20140'%20opacity='0.75'%3e%3c/path%3e%3cpath%20d='M%200%20112%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%20112'%20opacity='0.81'%3e%3c/path%3e%3cpath%20d='M%200%2084%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%2084'%20opacity='0.87'%3e%3c/path%3e%3cpath%20d='M%200%2056%20Q%20355.5%20-100%20711%20400%20Q%201066.5%20900%201422%2056'%20opacity='0.94'%3e%3c/path%3e%3c/g%3e%3c/svg%3e");
  background-size: 150px;
  opacity: 0.1;
  z-index: -1;
}

button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

button:active {
  transform: translateY(1px);
  box-shadow: var(--shadow-sm);
}

button.primary {
  background-color: var(--color-primary);
  color: white;
}

button.secondary {
  background-color: var(--color-secondary);
  color: var(--color-text);
}

button.accent {
  background-color: var(--color-accent);
  color: white;
}

input, textarea {
  font-family: var(--font-family-sans);
  padding: var(--spacing-sm) var(--spacing-md);
  border: 2px solid var(--color-border);
  border-radius: var(--border-radius-sm);
  background-color: rgba(255, 255, 255, 0.8);
  transition: all var(--transition-fast);
}

input:focus, textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(79, 121, 66, 0.2);
}

.paper-card {
  background-color: transparent;
  border-radius: var(--border-radius-md);
  border: 1px solid rgba(139, 69, 19, 0.2);
  padding: var(--spacing-lg);
  position: relative;
  overflow: hidden;
}

/* 滚动条样式 */

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--color-primary);
  opacity: 0.5;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-forest-green);
}

/* 页面转场动画 */

.fade-enter {
  opacity: 0;
}

.fade-enter-active {
  opacity: 1;
  transition: opacity var(--transition-normal);
}

.fade-exit {
  opacity: 1;
}

.fade-exit-active {
  opacity: 0;
  transition: opacity var(--transition-normal);
}
/* 响应式布局辅助样式 */

/* 大屏幕设备 (桌面, 1200px 及以上) */
@media (min-width: 1200px) {
  .chat-container {
    width: 100%; /* 移除最大宽度限制，占满屏幕 */
    max-width: 100%;
  }
}

/* 中等屏幕设备 (平板电脑, 768px 到 1199px) */
@media (min-width: 768px) and (max-width: 1199px) {
  .chat-container {
    max-width: 100%;
    padding: var(--spacing-md);
  }
}

/* 小屏幕设备 (手机, 767px 及以下) */
@media (max-width: 767px) {
  .chat-container {
    padding: var(--spacing-sm);
  }
  
  .chat-content {
    padding: var(--spacing-sm);
  }
  
  .chat-header {
    margin-bottom: var(--spacing-md);
  }
  
  .chat-title {
    font-size: var(--font-size-subtitle);
  }
  
  .chat-subtitle {
    font-size: var(--font-size-small);
  }
  
  .message-item {
    margin-bottom: var(--spacing-md);
  }
  
  .message-content {
    max-width: 85%;
  }
  
  .avatar {
    width: 32px;
    height: 32px;
    font-size: 10px;
  }
  
  .input-area {
    margin-top: var(--spacing-sm);
  }
}

/* 确保内容不会被键盘挤压在移动设备上 */
@media (max-height: 500px) {
  .chat-header {
    display: none;
  }
  
  .chat-container {
    padding-top: var(--spacing-xs);
    padding-bottom: var(--spacing-xs);
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  :root {
    --color-text: #000000;
    --color-background: #ffffff;
    --color-primary: #006400;
    --color-border: #000000;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.4);
  }
  
  .message-body {
    border: 2px solid var(--color-border);
  }
}

/* 减少动画 - 对动画敏感的用户 */
@media (prefers-reduced-motion) {
  *, *::before, *::after {
    animation-duration: 0.001ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.001ms !important;
  }
  
  .welcome-message {
    animation: none !important;
  }
}
.image-converter {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #333;
  font-family: var(--font-family-sans, 'Segoe UI', 'Microsoft YaHei', sans-serif);
}

.converter-header {
  background: rgba(255, 255, 255, 0.95);
  padding: 20px 30px;
  text-align: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.converter-header h1 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
}

.converter-header p {
  margin: 0 0 15px 0;
  color: #666;
  font-size: 14px;
}

/* API配置状态 */
.api-status {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 15px;
  padding: 12px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.status-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 500;
  color: #2c3e50;
}

.status-indicator {
  font-size: 14px;
}

.status-indicator.connected {
  color: #27ae60;
}

.status-indicator.warning {
  color: #f39c12;
}

.status-indicator.error {
  color: #e74c3c;
}

.converter-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 左侧输入区域 */
.input-section {
  width: 400px;
  background: rgba(255, 255, 255, 0.95);
  padding: 20px;
  overflow-y: auto;
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.input-group {
  margin-bottom: 20px;
}

.input-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #2c3e50;
  font-size: 14px;
}

.file-input-wrapper {
  display: flex;
  gap: 10px;
}

.file-select-btn {
  flex: 1;
  padding: 12px 16px;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.file-select-btn:hover:not(:disabled) {
  background: #2980b9;
  transform: translateY(-1px);
}

.file-select-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.clear-btn {
  padding: 12px 16px;
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.clear-btn:hover:not(:disabled) {
  background: #c0392b;
  transform: translateY(-1px);
}

/* 图片预览区域 */
.image-preview-section {
  margin-bottom: 20px;
}

.preview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 12px;
  max-height: 200px;
  overflow-y: auto;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.preview-item {
  position: relative;
  background: white;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.preview-image {
  width: 100%;
  height: 80px;
  object-fit: cover;
}

.preview-info {
  padding: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.image-name {
  font-size: 11px;
  color: #666;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.remove-btn {
  width: 20px;
  height: 20px;
  border: none;
  background: #e74c3c;
  color: white;
  border-radius: 50%;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.remove-btn:hover:not(:disabled) {
  background: #c0392b;
  transform: scale(1.1);
}

/* 提示词输入 */
textarea {
  width: 100%;
  padding: 12px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  transition: border-color 0.2s ease;
  box-sizing: border-box;
}

textarea:focus {
  outline: none;
  border-color: #3498db;
}

textarea:disabled {
  background: #f8f9fa;
  opacity: 0.7;
}

/* 参数设置 */
.params-section {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.params-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #2c3e50;
}

/* 风格预览 */
.style-preview {
  margin-bottom: 16px;
  padding: 12px;
  background: #e8f4fd;
  border-radius: 6px;
  border-left: 4px solid #3498db;
}

.style-preview label {
  font-size: 12px;
  color: #2c3e50;
  font-weight: 600;
  margin-bottom: 6px;
  display: block;
}

.style-prompt-preview {
  font-size: 11px;
  color: #555;
  line-height: 1.4;
  font-family: 'Courier New', monospace;
  background: rgba(255, 255, 255, 0.7);
  padding: 8px;
  border-radius: 4px;
}

/* 高级参数 */
.advanced-params {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 2px dashed #ddd;
}

.param-divider {
  text-align: center;
  margin-bottom: 16px;
}

.param-divider span {
  background: #f8f9fa;
  padding: 0 12px;
  color: #666;
  font-size: 13px;
  font-weight: 500;
}

.param-input-group {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.param-value {
  min-width: 40px;
  text-align: center;
  font-weight: 600;
  color: #2c3e50;
  background: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.param-description {
  font-size: 11px;
  color: #666;
  margin-top: 6px;
  line-height: 1.4;
  font-style: italic;
  padding: 6px 8px;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 4px;
  border-left: 2px solid rgba(102, 126, 234, 0.3);
}

.param-row {
  margin-bottom: 16px;
}

.param-row:last-child {
  margin-bottom: 0;
}

.param-row label {
  font-size: 13px;
  color: #555;
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
}

/* 复合参数行（带控件和值显示的） */
.param-row.has-control {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.param-control-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

/* 简单参数行（只有复选框的） */
.param-row.simple {
  display: flex;
  align-items: center;
  gap: 8px;
}

.param-row.simple label {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.param-control-row select,
.param-control-row input[type="number"] {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 13px;
  min-width: 140px;
  flex: 1;
  background: white;
  transition: border-color 0.2s ease;
}

.param-control-row select:focus,
.param-control-row input[type="number"]:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.param-row input[type="range"] {
  flex: 1;
  margin: 0 8px;
}

.param-row input[type="checkbox"] {
  margin: 0;
}

/* 转换按钮 */
.convert-btn {
  width: 100%;
  padding: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.convert-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.convert-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 右侧结果区域 */
.results-section {
  flex: 1;
  background: rgba(255, 255, 255, 0.9);
  padding: 20px;
  overflow-y: auto;
  backdrop-filter: blur(10px);
}

.results-header {
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid #e1e5e9;
}

.results-header > h3 {
  margin-bottom: 12px;
}

.results-header h3 {
  margin: 0;
  font-size: 20px;
  color: #2c3e50;
}

.stats {
  display: flex;
  gap: 16px;
  font-size: 13px;
  color: #666;
}

.stats span {
  padding: 4px 8px;
  background: #f8f9fa;
  border-radius: 4px;
}

.results-actions {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-top: 12px;
  flex-wrap: wrap;
}

.download-all-btn,
.open-folder-btn,
.file-manager-btn,
.history-btn,
.clean-btn,
.copy-path-btn,
.open-local-btn {
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.download-all-btn {
  background: #27ae60;
  color: white;
}

.download-all-btn:hover {
  background: #219a52;
  transform: translateY(-1px);
}

.open-folder-btn {
  background: #3498db;
  color: white;
}

.open-folder-btn:hover {
  background: #2980b9;
  transform: translateY(-1px);
}

.file-manager-btn {
  background: #9b59b6;
  color: white;
}

.file-manager-btn:hover {
  background: #8e44ad;
  transform: translateY(-1px);
}

.history-btn {
  background: #e67e22;
  color: white;
}

.history-btn:hover {
  background: #d35400;
  transform: translateY(-1px);
}

.clean-btn {
  background: #e74c3c;
  color: white;
}

.clean-btn:hover {
  background: #c0392b;
  transform: translateY(-1px);
}

.copy-path-btn,
.open-local-btn {
  background: #95a5a6;
  color: white;
  padding: 4px 8px;
  font-size: 11px;
}

.copy-path-btn:hover,
.open-local-btn:hover {
  background: #7f8c8d;
  transform: translateY(-1px);
}

/* 文件管理面板 */
.file-manager-panel {
  background: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.file-info h4 {
  margin: 0 0 12px 0;
  color: #2c3e50;
  font-size: 14px;
}

.path-info {
  margin-bottom: 12px;
}

.path-info label {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
  font-weight: 500;
}

.path-display {
  display: flex;
  align-items: center;
  gap: 8px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px;
}

.path-display code {
  flex: 1;
  font-size: 11px;
  color: #2c3e50;
  background: none;
  padding: 0;
  word-break: break-all;
}

.file-actions {
  display: flex;
  gap: 8px;
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid #e1e5e9;
  flex-wrap: wrap;
}

/* 处理中指示器 */
.processing-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* 结果网格 */
.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.result-item {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.result-item:hover {
  transform: translateY(-4px);
}

.result-image-container {
  position: relative;
  height: 200px;
  overflow: hidden;
  background-color: #f8f9fa;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.result-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  cursor: pointer;
  transition: transform 0.2s ease;
  background-color: #f8f9fa;
}

.result-image:hover {
  transform: scale(1.05);
}

.result-info {
  padding: 16px;
}

.result-name {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.result-meta {
  font-size: 11px;
  color: #666;
  margin-bottom: 12px;
  line-height: 1.4;
}

.result-meta > div {
  margin-bottom: 4px;
}

.local-file {
  color: #2c3e50;
  font-weight: 500;
}

.local-status {
  color: #27ae60;
  font-weight: 500;
}

.result-actions {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.download-btn {
  flex: 1;
  padding: 8px 12px;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: auto;
}

.download-btn:hover {
  background: #2980b9;
  transform: translateY(-1px);
}

/* 空状态 */
.empty-results {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  color: #999;
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .converter-content {
    flex-direction: column;
  }
  
  .input-section {
    width: 100%;
    max-height: 50vh;
  }
  
  .results-section {
    max-height: 50vh;
  }
}

@media (max-width: 768px) {
  .converter-header {
    padding: 16px 20px;
  }

  .converter-header h1 {
    font-size: 24px;
  }
  
  .api-status {
    flex-direction: column;
    gap: 8px;
  }
  
  .status-item {
    justify-content: center;
  }
  
  .input-section,
  .results-section {
    padding: 16px;
  }
  
  .file-input-wrapper {
    flex-direction: column;
  }
  
  .preview-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  }
  
  .results-grid {
    grid-template-columns: 1fr;
  }
  
  .param-row.has-control {
    gap: 6px;
  }
  
  .param-control-row {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .param-control-row select,
  .param-control-row input[type="number"] {
    width: 100%;
    min-width: unset;
  }
  
  .param-input-group {
    width: 100%;
  }
  
  .results-actions {
    justify-content: flex-start;
  }
  
  .file-actions {
    flex-direction: column;
  }
  
  .path-display {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .result-actions {
    flex-direction: column;
  }
  
  .download-btn,
  .open-local-btn {
    width: 100%;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.result-item {
  animation: fadeIn 0.3s ease-out;
}

/* 滚动条样式 */
.input-section::-webkit-scrollbar,
.results-section::-webkit-scrollbar,
.preview-grid::-webkit-scrollbar {
  width: 6px;
}

.input-section::-webkit-scrollbar-track,
.results-section::-webkit-scrollbar-track,
.preview-grid::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.input-section::-webkit-scrollbar-thumb,
.results-section::-webkit-scrollbar-thumb,
.preview-grid::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.input-section::-webkit-scrollbar-thumb:hover,
.results-section::-webkit-scrollbar-thumb:hover,
.preview-grid::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
} 