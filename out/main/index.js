"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
const electron = require("electron");
const path = require("path");
const utils = require("@electron-toolkit/utils");
const fs = require("fs");
const Replicate = require("replicate");
const dotenv = require("dotenv");
const icon = path.join(__dirname, "../../resources/icon.png");
function setupEnvironment() {
  try {
    if (process.env.NODE_ENV === "development") {
      dotenv.config();
      console.log("[环境配置] 开发环境：从.env文件加载配置");
    } else {
      const possibleEnvPaths = [
        // 应用数据目录
        path.join(electron.app.getPath("userData"), ".env"),
        // 应用安装目录
        path.join(process.resourcesPath, ".env"),
        // 用户主目录
        path.join(electron.app.getPath("home"), ".observer-137.env"),
        // 系统配置目录
        path.join(electron.app.getPath("home"), "Library", "Preferences", "Observer-137", ".env")
      ];
      let envLoaded = false;
      for (const envPath of possibleEnvPaths) {
        if (fs.existsSync(envPath)) {
          console.log("[环境配置] 生产环境：从文件加载配置:", envPath);
          dotenv.config({ path: envPath });
          envLoaded = true;
          break;
        }
      }
      if (!envLoaded) {
        console.warn("[环境配置] 警告：未找到环境配置文件");
        console.log("[环境配置] 尝试的路径:", possibleEnvPaths);
        createDefaultEnvFile();
      }
    }
    validateEnvironmentVariables();
  } catch (error) {
    console.error("[环境配置] 加载环境变量失败:", error);
  }
}
function validateEnvironmentVariables() {
  const requiredVars = [
    "VITE_REPLICATE_API_TOKEN",
    "VITE_DEEPSEEK_API_KEY"
  ];
  const missing = [];
  for (const varName of requiredVars) {
    if (!process.env[varName]) {
      missing.push(varName);
    }
  }
  if (missing.length > 0) {
    console.error("[环境配置] 缺少必要的环境变量:", missing);
    console.log("[环境配置] 请在以下位置之一创建.env文件:");
    console.log("  1.", path.join(electron.app.getPath("userData"), ".env"));
    console.log("  2.", path.join(electron.app.getPath("home"), ".observer-137.env"));
  } else {
    console.log("[环境配置] ✅ 所有必要的环境变量已加载");
  }
}
function createDefaultEnvFile() {
  try {
    const { writeFileSync, mkdirSync } = require("fs");
    const configPath = path.join(electron.app.getPath("userData"), ".env");
    const configDir = path.join(electron.app.getPath("userData"));
    if (!fs.existsSync(configDir)) {
      mkdirSync(configDir, { recursive: true });
    }
    const defaultConfig = `# Observer-137 配置文件
# 请填入您的API密钥

# Replicate API Token (必需)
# 获取地址: https://replicate.com/account/api-tokens
VITE_REPLICATE_API_TOKEN=your_replicate_token_here

# DeepSeek API Key (可选，用于提示词优化)
# 获取地址: https://platform.deepseek.com/api_keys
VITE_DEEPSEEK_API_KEY=your_deepseek_key_here

# 其他配置
NODE_ENV=production
`;
    writeFileSync(configPath, defaultConfig, "utf8");
    console.log("[环境配置] 已创建默认配置文件:", configPath);
    console.log("[环境配置] 请编辑此文件并填入您的API密钥");
  } catch (error) {
    console.error("[环境配置] 创建默认配置文件失败:", error);
  }
}
function getConfigInfo() {
  return {
    nodeEnv: process.env.NODE_ENV,
    hasReplicateToken: !!process.env.VITE_REPLICATE_API_TOKEN,
    hasDeepseekKey: !!process.env.VITE_DEEPSEEK_API_KEY,
    userDataPath: electron.app.getPath("userData"),
    resourcesPath: process.resourcesPath || "N/A"
  };
}
setupEnvironment();
let replicate = null;
try {
  if (process.env.VITE_REPLICATE_API_TOKEN) {
    replicate = new Replicate({
      auth: process.env.VITE_REPLICATE_API_TOKEN
    });
    console.log("[Main Process] ✅ Replicate客户端初始化成功");
  } else {
    console.warn("[Main Process] ⚠️ Replicate API Token未配置");
  }
} catch (error) {
  console.error("[Main Process] ❌ Replicate客户端初始化失败:", error);
}
console.log("[Main Process] 配置信息:", getConfigInfo());
function createWindow() {
  const mainWindow = new electron.BrowserWindow({
    width: 1200,
    height: 800,
    show: false,
    autoHideMenuBar: true,
    title: "FLUX 图像转换工具",
    backgroundColor: "#F5F5DC",
    // 吉卜力风格的柔和米色
    ...process.platform === "linux" ? { icon } : {},
    webPreferences: {
      preload: path.join(__dirname, "../preload/index.js"),
      sandbox: false,
      contextIsolation: true,
      nodeIntegration: false
    }
  });
  mainWindow.on("ready-to-show", () => {
    mainWindow.show();
  });
  mainWindow.webContents.setWindowOpenHandler((details) => {
    electron.shell.openExternal(details.url);
    return { action: "deny" };
  });
  if (utils.is.dev) {
    mainWindow.webContents.openDevTools();
  }
  if (utils.is.dev && process.env["ELECTRON_RENDERER_URL"]) {
    mainWindow.loadURL(process.env["ELECTRON_RENDERER_URL"]);
  } else {
    mainWindow.loadFile(path.join(__dirname, "../renderer/index.html"));
  }
}
function setupFileSystemHandlers() {
  electron.ipcMain.handle("get-app-data-path", () => {
    return electron.app.getPath("userData");
  });
  electron.ipcMain.handle("ensure-directory", async (event, dirPath) => {
    try {
      if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
        console.log("[文件系统] 创建目录:", dirPath);
      }
      return true;
    } catch (error) {
      console.error("[文件系统] 创建目录失败:", error);
      return false;
    }
  });
  electron.ipcMain.handle("write-file", async (event, filePath, data) => {
    try {
      await fs.promises.writeFile(filePath, Buffer.from(data));
      console.log("[文件系统] 文件写入成功:", filePath);
      return true;
    } catch (error) {
      console.error("[文件系统] 文件写入失败:", error);
      throw error;
    }
  });
  electron.ipcMain.handle("open-folder", async (event, folderPath) => {
    try {
      if (!fs.existsSync(folderPath)) {
        fs.mkdirSync(folderPath, { recursive: true });
        console.log("[文件系统] 创建目录:", folderPath);
      }
      const result = await electron.shell.openPath(folderPath);
      if (result) {
        console.error("[文件系统] 打开文件夹失败:", result);
        electron.dialog.showErrorBox("无法打开文件夹", `无法打开文件夹: ${folderPath}
错误: ${result}`);
        return false;
      } else {
        console.log("[文件系统] 打开文件夹成功:", folderPath);
        return true;
      }
    } catch (error) {
      console.error("[文件系统] 打开文件夹异常:", error);
      electron.dialog.showErrorBox("文件夹访问错误", `访问文件夹时发生错误: ${error.message}`);
      return false;
    }
  });
  electron.ipcMain.handle("list-files", async (event, dirPath) => {
    try {
      if (!fs.existsSync(dirPath)) {
        return [];
      }
      const files = await fs.promises.readdir(dirPath, { withFileTypes: true });
      return files.filter((file) => file.isFile() && /\.(jpg|jpeg|png|webp|gif)$/i.test(file.name)).map((file) => ({
        name: file.name,
        path: path.join(dirPath, file.name)
      }));
    } catch (error) {
      console.error("[文件系统] 列出文件失败:", error);
      return [];
    }
  });
  electron.ipcMain.handle("clean-old-files", async (event, daysToKeep = 7) => {
    try {
      const appDataPath = electron.app.getPath("userData");
      const outputsPath = path.join(appDataPath, "outputs");
      if (!fs.existsSync(outputsPath)) {
        return true;
      }
      const cutoffDate = /* @__PURE__ */ new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
      const dirs = await fs.promises.readdir(outputsPath, { withFileTypes: true });
      let cleanedCount = 0;
      for (const dir of dirs) {
        if (dir.isDirectory()) {
          const dirDate = new Date(dir.name);
          if (dirDate < cutoffDate) {
            const dirPath = path.join(outputsPath, dir.name);
            await fs.promises.rm(dirPath, { recursive: true, force: true });
            cleanedCount++;
            console.log("[文件系统] 清理旧目录:", dirPath);
          }
        }
      }
      console.log(`[文件系统] 清理完成，删除了 ${cleanedCount} 个旧目录`);
      return true;
    } catch (error) {
      console.error("[文件系统] 清理失败:", error);
      return false;
    }
  });
  electron.ipcMain.handle("save-conversion-history", async (event, historyData) => {
    try {
      const historyDir = path.join(electron.app.getPath("userData"), "history");
      if (!fs.existsSync(historyDir)) {
        fs.mkdirSync(historyDir, { recursive: true });
      }
      const historyFile = path.join(historyDir, "conversions.json");
      let history = [];
      if (fs.existsSync(historyFile)) {
        const data = await fs.promises.readFile(historyFile, "utf8");
        history = JSON.parse(data);
      }
      history.unshift(historyData);
      if (history.length > 100) {
        history = history.slice(0, 100);
      }
      await fs.promises.writeFile(historyFile, JSON.stringify(history, null, 2));
      console.log("[历史记录] 保存成功:", historyData.batchId);
      return true;
    } catch (error) {
      console.error("[历史记录] 保存失败:", error);
      return false;
    }
  });
  electron.ipcMain.handle("get-conversion-history", async (event) => {
    try {
      const historyFile = path.join(electron.app.getPath("userData"), "history", "conversions.json");
      if (!fs.existsSync(historyFile)) {
        return [];
      }
      const data = await fs.promises.readFile(historyFile, "utf8");
      const history = JSON.parse(data);
      console.log(`[历史记录] 读取成功，共 ${history.length} 条记录`);
      return history;
    } catch (error) {
      console.error("[历史记录] 读取失败:", error);
      return [];
    }
  });
  electron.ipcMain.handle("delete-conversion-history", async (event, batchId) => {
    try {
      const historyFile = path.join(electron.app.getPath("userData"), "history", "conversions.json");
      if (!fs.existsSync(historyFile)) {
        return true;
      }
      const data = await fs.promises.readFile(historyFile, "utf8");
      let history = JSON.parse(data);
      history = history.filter((item) => item.batchId !== batchId);
      await fs.promises.writeFile(historyFile, JSON.stringify(history, null, 2));
      console.log("[历史记录] 删除成功:", batchId);
      return true;
    } catch (error) {
      console.error("[历史记录] 删除失败:", error);
      return false;
    }
  });
  electron.ipcMain.handle("get-file-stats", async (event, filePath) => {
    try {
      const stats = await fs.promises.stat(filePath);
      return {
        size: stats.size,
        created: stats.birthtime,
        modified: stats.mtime
      };
    } catch (error) {
      console.error("[文件系统] 获取文件统计失败:", error);
      return null;
    }
  });
}
function setupApiHandlers() {
  electron.ipcMain.handle("check-api-config", () => {
    const config = getConfigInfo();
    return {
      ...config,
      replicateConfigured: !!process.env.VITE_REPLICATE_API_TOKEN,
      deepseekConfigured: !!process.env.VITE_DEEPSEEK_API_KEY,
      configPath: path.join(electron.app.getPath("userData"), ".env")
    };
  });
  electron.ipcMain.handle("reload-env-config", () => {
    try {
      setupEnvironment();
      if (process.env.VITE_REPLICATE_API_TOKEN) {
        replicate = new Replicate({
          auth: process.env.VITE_REPLICATE_API_TOKEN
        });
        console.log("[Main Process] ✅ Replicate客户端重新初始化成功");
      }
      return { success: true, config: getConfigInfo() };
    } catch (error) {
      console.error("[Main Process] 重新加载配置失败:", error);
      return { success: false, error: error.message };
    }
  });
  electron.ipcMain.handle("replicate-run", async (event, model, input) => {
    try {
      if (!replicate) {
        throw new Error("Replicate API未配置。请检查VITE_REPLICATE_API_TOKEN环境变量。");
      }
      console.log("[Replicate Run] 运行模型:", model);
      console.log("[Replicate Run] 输入参数:", input);
      const output = await replicate.run(model, { input });
      console.log("[Replicate Run] 模型运行成功，输出类型:", typeof output);
      console.log("[Replicate Run] 输出长度:", output?.length || "N/A");
      let finalData = output;
      if (typeof output === "object" && output !== null) {
        console.log("[Replicate Run] 对象属性:", Object.keys(output));
        console.log("[Replicate Run] 有url方法:", typeof output.url);
        if (typeof output.url === "function") {
          try {
            const url = output.url();
            console.log("[Replicate Run] ✅ 成功获取图片URL:", url);
            finalData = typeof url === "string" ? url : url.toString();
          } catch (e) {
            console.error("[Replicate Run] URL方法调用失败:", e);
            finalData = output.toString();
          }
        } else if (output.url) {
          finalData = typeof output.url === "string" ? output.url : output.url.toString();
          console.log("[Replicate Run] ✅ 使用url属性:", finalData);
        } else {
          try {
            finalData = output.toString();
            console.log("[Replicate Run] ✅ 使用toString:", finalData);
          } catch (e) {
            console.error("[Replicate Run] toString失败:", e);
          }
        }
      }
      console.log("[Replicate Run] 最终返回数据:", finalData);
      return { success: true, data: finalData };
    } catch (error) {
      console.error("[Replicate Run] 运行失败:", error);
      return { success: false, error: error.message };
    }
  });
  electron.ipcMain.handle("replicate-api", async (event, endpoint, options = {}) => {
    const { default: fetch } = await import("node-fetch");
    try {
      const url = `https://api.replicate.com/v1${endpoint}`;
      console.log("[Replicate API] 发送请求:", options.method || "GET", url);
      const response = await fetch(url, {
        ...options,
        headers: {
          "Authorization": `Token ${process.env.VITE_REPLICATE_API_TOKEN}`,
          "Content-Type": "application/json",
          ...options.headers
        }
      });
      if (!response.ok) {
        const errorText = await response.text().catch(() => "No error details");
        console.error(`[Replicate API] 响应错误 ${response.status}: ${errorText}`);
        throw new Error(`Replicate API错误 ${response.status}: ${errorText}`);
      }
      const data = await response.json();
      console.log("[Replicate API] 成功响应:", url);
      return { success: true, data };
    } catch (error) {
      console.error("[Replicate API] 请求失败:", error);
      return { success: false, error: error.message };
    }
  });
  electron.ipcMain.handle("deepseek-api", async (event, endpoint, options = {}) => {
    const { default: fetch } = await import("node-fetch");
    try {
      const url = `https://api.deepseek.com/v1${endpoint}`;
      console.log("[DeepSeek API] 发送请求:", options.method || "GET", url);
      const response = await fetch(url, {
        ...options,
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${process.env.VITE_DEEPSEEK_API_KEY}`,
          ...options.headers
        }
      });
      if (!response.ok) {
        const errorText = await response.text().catch(() => "No error details");
        console.error(`[DeepSeek API] 响应错误 ${response.status}: ${errorText}`);
        throw new Error(`DeepSeek API错误 ${response.status}: ${errorText}`);
      }
      const data = await response.json();
      console.log("[DeepSeek API] 成功响应:", url);
      return { success: true, data };
    } catch (error) {
      console.error("[DeepSeek API] 请求失败:", error);
      return { success: false, error: error.message };
    }
  });
  electron.ipcMain.handle("fetch-url", async (event, url, options = {}) => {
    const { default: fetch } = await import("node-fetch");
    try {
      console.log("[HTTP] 下载:", url);
      const response = await fetch(url, options);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      const buffer = await response.buffer();
      return { success: true, data: buffer };
    } catch (error) {
      console.error("[HTTP] 请求失败:", error);
      return { success: false, error: error.message };
    }
  });
}
electron.app.whenReady().then(() => {
  utils.electronApp.setAppUserModelId("com.fluxconverter.app");
  setupFileSystemHandlers();
  setupApiHandlers();
  electron.app.on("browser-window-created", (_, window) => {
    utils.optimizer.watchWindowShortcuts(window);
  });
  createWindow();
  electron.app.on("activate", function() {
    if (electron.BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});
electron.app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    electron.app.quit();
  }
});
