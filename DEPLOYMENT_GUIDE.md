# Observer-137 部署和使用指南

## 🚀 打包应用

### 构建Mac应用
```bash
npm run build:mac
```

构建完成后，应用将位于 `dist/` 目录中。

## 📋 用户使用指南

### 首次运行配置

当用户首次运行打包后的应用时，需要配置API密钥：

#### 1. 自动配置向导
- 应用会自动检测API配置状态
- 如果缺少必要配置，会显示配置向导
- 按照向导提示完成配置

#### 2. 手动配置步骤

**步骤1: 获取API密钥**
- **Replicate API Token (必需)**
  - 访问: https://replicate.com/account/api-tokens
  - 注册账户并创建API Token
  
- **DeepSeek API Key (可选)**
  - 访问: https://platform.deepseek.com/api_keys
  - 用于提示词优化功能

**步骤2: 创建配置文件**

配置文件位置 (选择其中一个):
- `~/Library/Application Support/Observer-137/.env` (推荐)
- `~/.observer-137.env`

配置文件内容:
```env
# Observer-137 配置文件
# 请填入您的API密钥

# Replicate API Token (必需)
VITE_REPLICATE_API_TOKEN=your_replicate_token_here

# DeepSeek API Key (可选，用于提示词优化)
VITE_DEEPSEEK_API_KEY=your_deepseek_key_here

# 其他配置
NODE_ENV=production
```

**步骤3: 重启应用**
配置完成后重启应用即可正常使用。

## 🔧 故障排除

### 问题1: 转换图片时报错

**可能原因:**
- API密钥未配置或配置错误
- 网络连接问题
- API配额不足

**解决方案:**
1. 检查配置文件中的API密钥是否正确
2. 确认网络连接正常
3. 检查Replicate账户余额和API配额
4. 查看应用日志 (开发者工具 -> Console)

### 问题2: 无法打开本地目录

**可能原因:**
- macOS权限限制
- 目录不存在或权限不足

**解决方案:**
1. 在系统偏好设置中给应用授予文件访问权限
2. 手动创建输出目录: `~/Library/Application Support/Observer-137/outputs`
3. 检查目录权限: `chmod 755 ~/Library/Application\ Support/Observer-137`

### 问题3: 应用无法启动

**可能原因:**
- macOS安全限制
- 应用签名问题

**解决方案:**
1. 右键点击应用 -> "打开" (而不是双击)
2. 在系统偏好设置 -> 安全性与隐私中允许应用运行
3. 如果仍有问题，在终端中运行:
   ```bash
   xattr -cr /Applications/Observer-137.app
   ```

## 📁 文件结构

打包后的应用文件结构:
```
Observer-137.app/
├── Contents/
│   ├── MacOS/
│   │   └── Observer-137          # 主执行文件
│   ├── Resources/
│   │   ├── app.asar             # 应用代码
│   │   └── ...
│   └── Info.plist               # 应用信息
```

用户数据目录:
```
~/Library/Application Support/Observer-137/
├── .env                         # 配置文件
├── outputs/                     # 输出图片目录
│   └── YYYY-MM-DD_HH-MM-SS/    # 按时间戳分组的批次
│       └── batch_xxx/          # 具体批次目录
└── history/                     # 历史记录
    └── conversions.json        # 转换历史
```

## 🔐 安全注意事项

1. **API密钥安全**
   - 不要在公共场所或截图中暴露API密钥
   - 定期轮换API密钥
   - 监控API使用情况

2. **文件权限**
   - 配置文件应设置适当的权限 (600)
   - 避免在共享计算机上使用

3. **网络安全**
   - 确保在安全的网络环境中使用
   - 注意API调用的数据传输

## 📞 技术支持

如果遇到其他问题:

1. **查看日志**
   - 打开开发者工具 (Cmd+Option+I)
   - 查看Console标签页的错误信息

2. **重置配置**
   ```bash
   rm -rf ~/Library/Application\ Support/Observer-137/.env
   ```
   然后重新配置

3. **完全重置**
   ```bash
   rm -rf ~/Library/Application\ Support/Observer-137/
   ```
   这将删除所有用户数据和配置

## 🔄 更新应用

1. 下载新版本的应用
2. 替换旧版本
3. 配置文件和用户数据会自动保留
4. 如有配置格式变更，应用会自动提示更新

---

**注意**: 此应用需要网络连接才能正常工作，因为图片转换需要调用在线API服务。
