/**
 * 测试环境变量加载的脚本
 * 用于验证打包后的应用是否能正确加载环境变量
 */

const { join } = require('path')
const { existsSync } = require('fs')
const dotenv = require('dotenv')

console.log('=== 环境变量测试 ===')
console.log('NODE_ENV:', process.env.NODE_ENV)
console.log('__dirname:', __dirname)
console.log('process.resourcesPath:', process.resourcesPath)

// 模拟打包后的路径
const possibleEnvPaths = [
  // 应用包内的.env文件 (打包时包含的，在app.asar.unpacked中)
  join(process.resourcesPath || __dirname, 'app.asar.unpacked', '.env'),
  join(process.resourcesPath || __dirname, '.env'),
  // 当前目录的.env文件
  join(__dirname, '.env'),
]

console.log('\n=== 检查可能的.env文件路径 ===')
possibleEnvPaths.forEach((path, index) => {
  const exists = existsSync(path)
  console.log(`${index + 1}. ${path} - ${exists ? '✅ 存在' : '❌ 不存在'}`)
  
  if (exists) {
    console.log('   尝试加载此文件...')
    dotenv.config({ path })
    console.log('   VITE_REPLICATE_API_TOKEN:', process.env.VITE_REPLICATE_API_TOKEN ? '已设置' : '未设置')
    console.log('   VITE_DEEPSEEK_API_KEY:', process.env.VITE_DEEPSEEK_API_KEY ? '已设置' : '未设置')
  }
})

console.log('\n=== 最终环境变量状态 ===')
console.log('VITE_REPLICATE_API_TOKEN:', process.env.VITE_REPLICATE_API_TOKEN ? '✅ 已配置' : '❌ 未配置')
console.log('VITE_DEEPSEEK_API_KEY:', process.env.VITE_DEEPSEEK_API_KEY ? '✅ 已配置' : '❌ 未配置')
