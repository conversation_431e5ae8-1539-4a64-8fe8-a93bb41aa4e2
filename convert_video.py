#!/usr/bin/env python3
"""
AI视频转换脚本 - 从预处理帧转换并合成视频
专门负责AI转换和视频合成，读取prepare_frames.py的输出
"""

import replicate
import os
import glob
import requests
from PIL import Image
import cv2
import numpy as np
from tqdm import tqdm
import time
import hashlib

print("=== AI视频转换脚本 - 从预处理帧转换并合成视频 ===")

# 获取脚本所在目录的父目录作为工作根目录
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
ROOT_DIR = os.path.dirname(SCRIPT_DIR)
WORK_DIR = os.path.join(ROOT_DIR, "outputs/video_processing")

def load_video_info(frames_dir):
    """从预处理目录加载视频信息"""
    info_file = os.path.join(frames_dir, "video_info.txt")
    if not os.path.exists(info_file):
        print("❌ 没有找到视频信息文件")
        return None
    
    video_info = {}
    with open(info_file, "r") as f:
        for line in f:
            line = line.strip()
            if "=" in line:
                key, value = line.split("=", 1)
                try:
                    video_info[key] = float(value)
                except ValueError:
                    video_info[key] = value
    
    return video_info

def detect_aspect_ratio_mode(frames_dir):
    """检测预处理模式是否保持了原始比例"""
    # 检查目录名是否包含aspect_ratio标识
    if "aspect_ratio" in os.path.basename(frames_dir):
        return True
    return False

def calculate_flux_aspect_ratio(video_info):
    """计算适合FLUX模型的aspect_ratio参数"""
    width = int(video_info['original_width'])
    height = int(video_info['original_height'])
    aspect_ratio = width / height
    
    # 映射到FLUX支持的标准比例
    if abs(aspect_ratio - 1.0) < 0.1:  # 接近1:1
        return "1:1"
    elif abs(aspect_ratio - 16/9) < 0.1:  # 接近16:9
        return "16:9"
    elif abs(aspect_ratio - 9/16) < 0.1:  # 接近9:16
        return "9:16"
    elif abs(aspect_ratio - 4/3) < 0.1:  # 接近4:3
        return "4:3"
    elif abs(aspect_ratio - 3/4) < 0.1:  # 接近3:4
        return "3:4"
    elif abs(aspect_ratio - 3/2) < 0.1:  # 接近3:2
        return "3:2"
    elif abs(aspect_ratio - 2/3) < 0.1:  # 接近2:3
        return "2:3"
    elif abs(aspect_ratio - 21/9) < 0.1:  # 接近21:9
        return "21:9"
    elif abs(aspect_ratio - 9/21) < 0.1:  # 接近9:21
        return "9:21"
    else:
        # 对于720x1280，ratio = 0.5625，最接近9:16 (0.5625)
        if aspect_ratio < 1.0:  # 竖屏
            return "9:16"
        else:  # 横屏
            return "16:9"

def generate_consistent_seed(frame_number, base_seed=42):
    """生成一致性种子，确保相邻帧相似"""
    seed_string = f"{base_seed}_{frame_number // 5}"  # 每5帧使用相同基础种子
    return int(hashlib.md5(seed_string.encode()).hexdigest(), 16) % 1000000

def create_reference_image(current_frame, previous_converted=None, alpha=0.3):
    """创建参考图像，融合当前帧和前一个转换结果"""
    if previous_converted is None:
        return current_frame
    
    # 调整尺寸匹配
    if current_frame.size != previous_converted.size:
        previous_converted = previous_converted.resize(current_frame.size, Image.LANCZOS)
    
    # 创建融合图像作为参考
    current_array = np.array(current_frame)
    previous_array = np.array(previous_converted)
    
    # 加权融合：当前帧权重更高，但保留前一帧的风格特征
    blended = (current_array * (1 - alpha) + previous_array * alpha).astype(np.uint8)
    
    return Image.fromarray(blended)

def save_flux_output(output, output_path):
    """保存 FLUX 输出"""
    try:
        if hasattr(output, 'url'):
            response = requests.get(output.url, timeout=60)
        elif isinstance(output, str) and output.startswith('http'):
            response = requests.get(output, timeout=60)
        else:
            return False
        
        if response.status_code == 200:
            with open(output_path, "wb") as f:
                f.write(response.content)
            return True
        return False
    except Exception as e:
        print(f"保存失败: {e}")
        return False

def convert_frames_with_continuity(frames_dir, output_dir, style="ghibli", video_info=None):
    """转换预处理的帧 - 确保帧间连续性"""
    
    os.makedirs(output_dir, exist_ok=True)
    frame_files = sorted(glob.glob(os.path.join(frames_dir, "frame_*.png")))
    
    # 检测是否使用aspect_ratio模式
    use_aspect_ratio = detect_aspect_ratio_mode(frames_dir)
    flux_aspect_ratio = None
    
    if use_aspect_ratio and video_info:
        flux_aspect_ratio = calculate_flux_aspect_ratio(video_info)
        print(f"🎯 检测到保持比例模式，将使用aspect_ratio: {flux_aspect_ratio}")
    
    if not frame_files:
        print("❌ 没有找到预处理的帧文件")
        return []
    
    total_frames = len(frame_files)
    
    # 获取第一帧的尺寸信息
    first_frame = cv2.imread(frame_files[0])
    if first_frame is not None:
        h, w = first_frame.shape[:2]
        print(f"\n📐 预处理帧信息:")
        print(f"   分辨率: {w}x{h}")
        print(f"   帧数: {total_frames}")
        print(f"   AI兼容性: {'✅ 完美匹配' if w == h else '⚠️ 需要调整'}")
    
    # 询问转换帧数选择
    print(f"\n📊 帧数选择:")
    print(f"   总帧数: {total_frames} 帧")
    print(f"   1. 转换全部帧数 ({total_frames} 帧)")
    print(f"   2. 指定转换帧数 (用于测试)")
    
    frame_choice = input("请选择转换方式 (1-2，默认1): ").strip()
    
    frames_to_convert = total_frames
    if frame_choice == "2":
        while True:
            try:
                frames_input = input(f"请输入要转换的帧数 (1-{total_frames}): ").strip()
                frames_to_convert = int(frames_input)
                if 1 <= frames_to_convert <= total_frames:
                    break
                else:
                    print(f"❌ 请输入1到{total_frames}之间的数字")
            except ValueError:
                print("❌ 请输入有效的数字")
    
    print(f"✅ 选择转换: {frames_to_convert} 帧")
    
    print(f"\n🎨 开始AI转换 {frames_to_convert} 帧")
    print(f"🎯 优化特性:")
    print(f"   ✓ 帧间参考机制")
    print(f"   ✓ 一致性种子控制") 
    print(f"   ✓ 预填充高质量输入")
    print(f"   ✓ 时间连续性保证")
    print(f"💰 预估成本: ${frames_to_convert * 0.01:.2f}")
    print(f"⏱️ 预估时间: {frames_to_convert * 8 / 60:.0f} 分钟 ({frames_to_convert * 8 / 3600:.1f} 小时)")
    
    # 最终确认
    print(f"\n⚠️ 重要提醒:")
    print(f"   - 这将处理 {frames_to_convert} 帧 {'(全部)' if frames_to_convert == total_frames else '(部分测试)'}")
    print(f"   - 使用AI转换为 {style.upper()} 风格")
    print(f"   - 预估成本: ${frames_to_convert * 0.01:.2f}")
    print(f"   - 预估时间: {frames_to_convert * 8 / 60:.0f} 分钟")

    final_confirm = input(f"\n最终确认转换 {frames_to_convert} 帧？(y/n): ").strip().lower()
    if final_confirm not in ['y', 'yes']:
        print("❌ 用户取消转换")
        return []

    print("✅ 开始AI转换...")
    
    # 检查已处理的帧
    existing_files = glob.glob(os.path.join(output_dir, f"converted_{style}_*.png"))
    start_frame = len(existing_files)
    
    if start_frame > 0:
        print(f"\n🔄 发现已处理 {start_frame} 帧")
        print(f"📊 进度: {start_frame}/{total_frames} ({start_frame/total_frames*100:.1f}%)")
        
        continue_choice = input("是否从断点继续？(y/n): ").strip().lower()
        if continue_choice != 'y':
            start_frame = 0
            print("🔄 从头开始处理")
    
    # 优化的风格提示词 - 特别注意边缘处理
    style_prompts = {
        "ghibli": "Transform this into Studio Ghibli anime style, maintain high quality and artistic consistency, Hayao Miyazaki art style, soft watercolor painting, magical atmosphere, hand-drawn animation feel, whimsical details, preserve temporal continuity, beautiful composition, ensure natural seamless edges without visible borders or white lines, blend all areas smoothly",
        "shinkai": "Convert this to Makoto Shinkai anime style, maintain visual consistency and high quality, Your Name movie aesthetic, beautiful detailed sky, dramatic lighting, photorealistic anime, cinematic composition, preserve character identity and scene continuity, ensure seamless borders and natural edge transitions, no white or harsh boundaries",
        "anime": "Make this into high quality anime style, maintain consistent art style across sequence, cel shading, vibrant colors, traditional Japanese animation, detailed character art, clean lines, preserve temporal flow and character consistency, ensure smooth edge transitions and natural borders without white edges or visible seams",
        "realistic": "Transform into photorealistic style, maintain high quality and natural appearance, professional photography, cinematic lighting, detailed textures, preserve temporal continuity, ensure natural edge blending and seamless transitions, avoid any artificial borders or white edges",
        "oil_painting": "Convert to oil painting style, artistic brushstrokes, rich colors, traditional art technique, maintain composition and temporal flow, blend edges naturally with painterly transitions, avoid hard borders or white edges, ensure seamless artistic continuity"
    }
    
    selected_prompt = style_prompts.get(style, style_prompts["ghibli"])
    print(f"\n🎨 转换风格: {style.upper()}")
    print(f"💬 优化提示词: {selected_prompt}")
    
    processed_frames = []
    success_count = start_frame
    failed_frames = []
    previous_converted = None  # 存储前一帧的转换结果
    base_seed = 42  # 基础种子
    
    # 处理指定的帧数
    actual_end_frame = min(start_frame + frames_to_convert, total_frames) if frames_to_convert < total_frames else total_frames
    frames_to_process = actual_end_frame - start_frame
    
    with tqdm(total=frames_to_process, desc="AI转换", unit="帧") as pbar:
        for i in range(start_frame, actual_end_frame):
            frame_path = frame_files[i]
            
            try:
                # 加载当前帧（已预填充）
                current_frame = Image.open(frame_path)
                
                # 生成一致性种子
                current_seed = generate_consistent_seed(i, base_seed)
                
                # 创建参考图像（融合当前帧和前一个转换结果）
                reference_image = create_reference_image(current_frame, previous_converted, alpha=0.2)
                
                # 构建动态提示词
                if i == 0:
                    dynamic_prompt = f"First frame: {selected_prompt}"
                else:
                    dynamic_prompt = f"Continue the sequence: {selected_prompt}, maintain consistency with previous frame"
                
                # 准备临时参考图像文件
                reference_path = os.path.join(output_dir, f"temp_ref_{i:04d}.png")
                reference_image.save(reference_path)
                
                # 构建API调用参数
                api_params = {
                    "prompt": dynamic_prompt,
                    "input_image": open(reference_path, "rb"),
                    "output_format": "jpg",
                    "safety_tolerance": 2,
                    "guidance_scale": 3.5,  # 降低引导强度，保持一致性
                    "num_inference_steps": 25,  # 适中的推理步数
                    "seed": current_seed  # 使用一致性种子
                }
                
                # 如果是保持比例模式，添加aspect_ratio参数
                if use_aspect_ratio and flux_aspect_ratio:
                    api_params["aspect_ratio"] = flux_aspect_ratio
                    if i == 0:  # 只在第一帧时打印
                        print(f"🎯 使用aspect_ratio: {flux_aspect_ratio}")
                
                # API 调用
                output = replicate.run("black-forest-labs/flux-kontext-pro", input=api_params)
                
                if output:
                    output_filename = f"converted_{style}_{i+1:04d}.jpg"
                    output_path = os.path.join(output_dir, output_filename)
                    
                    if save_flux_output(output, output_path):
                        # 转换为PNG并保存为前一帧参考
                        try:
                            img = Image.open(output_path)
                            png_path = output_path.replace('.jpg', '.png')
                            img.save(png_path)
                            
                            # 更新前一帧参考
                            previous_converted = img.copy()
                            
                            success_count += 1
                            pbar.set_postfix({
                                '成功': f"{success_count}/{actual_end_frame}",
                                '成功率': f"{success_count/(i+1)*100:.1f}%",
                                '种子': current_seed,
                                '尺寸': f"{img.size[0]}x{img.size[1]}"
                            })
                            
                        except Exception as e:
                            failed_frames.append(i+1)
                            pbar.set_postfix({'失败': len(failed_frames)})
                    else:
                        failed_frames.append(i+1)
                else:
                    failed_frames.append(i+1)
                
                # 清理临时文件
                if os.path.exists(reference_path):
                    os.remove(reference_path)
                
                # API限制等待
                time.sleep(3)
                
            except Exception as e:
                failed_frames.append(i+1)
                error_str = str(e).lower()
                if "rate limit" in error_str:
                    print(f"\n⏳ API限制，等待60秒...")
                    time.sleep(60)
                elif "timeout" in error_str:
                    print(f"\n⏳ 超时，等待10秒...")
                    time.sleep(10)
                else:
                    time.sleep(5)
            
            pbar.update(1)
            
            # 每50帧显示详细统计
            if (i + 1) % 50 == 0:
                success_rate = success_count / (i + 1) * 100
                remaining = actual_end_frame - (i + 1)
                eta_minutes = remaining * 8 / 60
                
                print(f"\n📊 进度报告 ({i+1}/{actual_end_frame}):")
                print(f"   成功率: {success_rate:.1f}%")
                print(f"   失败数: {len(failed_frames)}")
                print(f"   预计剩余: {eta_minutes:.0f} 分钟")
    
    # 加载所有成功转换的帧
    print(f"\n📋 加载转换结果...")
    all_converted_files = sorted(glob.glob(os.path.join(output_dir, f"converted_{style}_*.png")))
    
    for png_file in tqdm(all_converted_files, desc="加载帧"):
        try:
            img = Image.open(png_file)
            processed_frames.append(np.array(img))
        except Exception as e:
            print(f"⚠️ 加载失败: {png_file}")
    
    # 最终统计
    print(f"\n🎯 AI转换完成统计:")
    print(f"   原始帧数: {total_frames}")
    print(f"   目标转换: {frames_to_convert} 帧 {'(全部)' if frames_to_convert == total_frames else '(部分测试)'}")
    print(f"   成功转换: {len(processed_frames)}")
    print(f"   转换率: {len(processed_frames)/frames_to_convert*100:.1f}%")
    print(f"   失败帧数: {len(failed_frames)}")
    print(f"   连续性优化: ✅ 已应用")
    
    if failed_frames:
        print(f"   失败帧号: {failed_frames[:20]}{'...' if len(failed_frames) > 20 else ''}")
    
    return processed_frames

def apply_edge_enhancement(frame):
    """应用边缘增强处理，减少白边问题"""
    try:
        # 检测可能的白边区域
        gray = cv2.cvtColor(frame, cv2.COLOR_RGB2GRAY)
        
        # 检测边缘区域的亮度异常
        height, width = gray.shape
        edge_threshold = 240  # 检测过亮的区域
        
        # 检查左右边缘是否有异常亮区域
        left_edge = gray[:, :50]  # 左边50像素
        right_edge = gray[:, -50:]  # 右边50像素
        
        # 如果检测到白边，进行修正
        if np.mean(left_edge) > edge_threshold or np.mean(right_edge) > edge_threshold:
            # 使用中心区域的颜色信息修正边缘
            center_region = frame[:, width//4:3*width//4, :]
            
            # 对左边缘进行修正
            if np.mean(left_edge) > edge_threshold:
                fade_width = min(50, width//8)
                for i in range(fade_width):
                    alpha = 1.0 - (i / fade_width) * 0.7
                    avg_color = np.mean(center_region, axis=(0, 1))
                    frame[:, i] = frame[:, i] * alpha + avg_color * (1 - alpha)
            
            # 对右边缘进行修正
            if np.mean(right_edge) > edge_threshold:
                fade_width = min(50, width//8)
                for i in range(fade_width):
                    alpha = 1.0 - (i / fade_width) * 0.7
                    avg_color = np.mean(center_region, axis=(0, 1))
                    col_idx = width - fade_width + i
                    frame[:, col_idx] = frame[:, col_idx] * alpha + avg_color * (1 - alpha)
    
    except Exception as e:
        # 如果处理失败，返回原帧
        pass
    
    return frame

def apply_temporal_smoothing(frames):
    """应用时间平滑处理，进一步减少帧间抖动"""
    if len(frames) < 3:
        return frames
    
    print(f"\n🔧 应用时间平滑处理...")
    smoothed_frames = []
    
    # 第一帧保持不变
    smoothed_frames.append(frames[0])
    
    # 中间帧使用3点平滑
    for i in tqdm(range(1, len(frames) - 1), desc="平滑处理"):
        prev_frame = frames[i-1].astype(np.float32)
        curr_frame = frames[i].astype(np.float32)
        next_frame = frames[i+1].astype(np.float32)
        
        # 时间加权平均：当前帧权重最高
        smoothed = (prev_frame * 0.2 + curr_frame * 0.6 + next_frame * 0.2)
        smoothed_frames.append(smoothed.astype(np.uint8))
    
    # 最后一帧保持不变
    smoothed_frames.append(frames[-1])
    
    print(f"✅ 时间平滑完成，处理了 {len(frames)} 帧")
    return smoothed_frames

def apply_edge_post_processing(frames):
    """应用边缘后处理，解决白边问题"""
    print(f"\n🎨 应用边缘增强处理...")
    
    processed_frames = []
    for frame in tqdm(frames, desc="边缘增强"):
        enhanced_frame = apply_edge_enhancement(frame)
        processed_frames.append(enhanced_frame)
    
    print(f"✅ 边缘增强完成，处理了 {len(frames)} 帧")
    return processed_frames

def create_final_video(frames, output_path, video_info, apply_smoothing=True, apply_edge_fix=True):
    """创建最终视频"""
    if len(frames) == 0:
        print("❌ 没有转换成功的帧")
        return False
    
    # 应用边缘后处理
    if apply_edge_fix:
        frames = apply_edge_post_processing(frames)
    
    # 应用时间平滑
    if apply_smoothing:
        frames = apply_temporal_smoothing(frames)
    
    fps = video_info['fps']
    duration = len(frames) / fps
    
    # 获取转换后帧的实际尺寸
    actual_height, actual_width = frames[0].shape[:2]
    
    print(f"\n🎬 创建最终视频:")
    print(f"   帧数: {len(frames)}")
    print(f"   帧率: {fps} FPS (与原视频一致)")
    print(f"   时长: {duration:.2f} 秒")
    print(f"   目标时长: {video_info['duration']:.2f} 秒")
    print(f"   输出分辨率: {actual_width}x{actual_height}")
    print(f"   时间平滑: {'✅ 已应用' if apply_smoothing else '❌ 未应用'}")
    
    try:
        height, width = frames[0].shape[:2]
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
        
        if not out.isOpened():
            print("❌ 无法创建视频文件")
            return False
        
        for frame in tqdm(frames, desc="合成视频"):
            if len(frame.shape) == 3:
                frame_bgr = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
            else:
                frame_bgr = frame
            out.write(frame_bgr)
        
        out.release()
        
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / (1024*1024)
            print(f"\n✅ 最终视频创建成功!")
            print(f"📁 文件: {output_path}")
            print(f"💾 大小: {file_size:.2f} MB")
            print(f"⏱️ 时长: {duration:.2f} 秒")
            print(f"🎯 帧率: {fps} FPS")
            print(f"📐 分辨率: {width}x{height}")
            
            # 验证时长
            if abs(duration - video_info['duration']) < 0.5:
                print(f"✅ 时长匹配原视频!")
            else:
                print(f"⚠️ 时长与原视频有差异")
            
            return True
        else:
            print("❌ 视频文件未生成")
            return False
            
    except Exception as e:
        print(f"❌ 创建视频失败: {e}")
        return False

def main():
    """主程序 - AI视频转换"""
    
    print("🎯 目标: 从预处理帧进行AI转换并合成视频")
    print("📋 功能: 专门负责AI转换和视频合成")
    print("🔗 依赖: 需要先运行 prepare_frames.py 生成预处理帧")
    
    # 1. 检查API Token
    if not os.environ.get("REPLICATE_API_TOKEN"):
        print("❌ 请设置 REPLICATE_API_TOKEN 环境变量")
        return
    
    # 2. 查找预处理的帧目录
    prepared_dirs = glob.glob(f"{WORK_DIR}/prepared_frames_*")
    
    if not prepared_dirs:
        print("❌ 没有找到预处理的帧目录")
        print("请先运行 prepare_frames.py 预处理帧")
        return
    
    if len(prepared_dirs) > 1:
        print(f"\n📁 找到多个预处理目录:")
        for i, dir_path in enumerate(prepared_dirs):
            dir_name = os.path.basename(dir_path)
            frame_count = len(glob.glob(os.path.join(dir_path, "frame_*.png")))
            print(f"   {i+1}. {dir_name} ({frame_count} 帧)")
        
        try:
            choice = int(input("请选择要转换的目录 (输入序号): ")) - 1
            frames_dir = prepared_dirs[choice]
        except (ValueError, IndexError):
            print("❌ 无效选择，使用第一个目录")
            frames_dir = prepared_dirs[0]
    else:
        frames_dir = prepared_dirs[0]
    
    print(f"\n📁 选择的目录: {os.path.basename(frames_dir)}")
    
    # 3. 加载视频信息
    video_info = load_video_info(frames_dir)
    if not video_info:
        print("❌ 无法加载视频信息")
        return
    
    print(f"\n📹 原始视频信息:")
    print(f"   帧率: {video_info['fps']} FPS")
    print(f"   总帧数: {int(video_info['total_frames'])}")
    print(f"   时长: {video_info['duration']:.2f} 秒")
    print(f"   原始分辨率: {int(video_info['original_width'])}x{int(video_info['original_height'])}")
    
    # 4. 选择转换风格
    print(f"\n🎨 选择转换风格:")
    print(f"   1. ghibli - 宫崎骏/吉卜力工作室风格（推荐）")
    print(f"   2. shinkai - 新海诚风格（唯美）")
    print(f"   3. anime - 传统动漫风格")
    print(f"   4. realistic - 写实风格")
    print(f"   5. oil_painting - 油画风格")
    
    style_choice = input("请选择转换风格 (1-5，默认1): ").strip()
    style_map = {
        "1": "ghibli", "2": "shinkai", "3": "anime", 
        "4": "realistic", "5": "oil_painting", "": "ghibli"
    }
    style = style_map.get(style_choice, "ghibli")
    print(f"✅ 选择转换风格: {style.upper()}")
    
    # 5. 设置输出目录
    base_name = os.path.basename(frames_dir)
    output_dir = f"{WORK_DIR}/converted_{base_name}_{style}"
    
    # 6. 开始AI转换
    print(f"\n🚀 步骤1: AI转换所有帧为 {style.upper()} 风格")
    converted_frames = convert_frames_with_continuity(frames_dir, output_dir, style, video_info)
    
    if not converted_frames:
        print("❌ 没有成功转换任何帧")
        return
    
    # 7. 创建最终视频
    output_video = f"{WORK_DIR}/final_{base_name}_{style}.mp4"
    
    print(f"\n🚀 步骤2: 创建最终视频")
    success = create_final_video(converted_frames, output_video, video_info, apply_smoothing=True)
    
    if success:
        print(f"\n🎉 AI视频转换完成!")
        print(f"🎬 输出文件: {output_video}")
        print(f"📊 转换统计: {len(converted_frames)} 帧转换成功")
        print(f"🎨 转换风格: {style.upper()}")
        print(f"🔧 优化特性: 帧间连续性 + 时间平滑 + 预填充策略")
        
        # 询问是否打开输出文件
        open_video = input(f"\n是否打开生成的视频？(y/n): ").strip().lower()
        if open_video in ['y', 'yes']:
            import subprocess
            subprocess.run(["open", output_video])
    else:
        print(f"\n❌ 视频创建失败")

if __name__ == "__main__":
    main() 